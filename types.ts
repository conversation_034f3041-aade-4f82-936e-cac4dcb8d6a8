export interface Step {
  id: number;
  title: string;
  description: string;
  techniques: string[];
  task: 'analyse' | 'génération' | 'validation' | 'synthèse';
}

export interface Message {
  sender: 'user' | 'ai';
  text: string;
}

// Interface pour le contexte de mission persistant P.R.O.F.
export interface MissionContext {
  personnage: string;      // Description détaillée du sujet, de son état d'esprit, etc.
  objectif: string;        // Le résultat idéal, la condition de victoire
  contraintes: string;     // Les lignes rouges absolues, les actions interdites
  roleAgent: string;       // La posture que Rooney doit adopter (ex: 'Avocat', 'Stratège', 'Médiateur')
  formatSortie: string;    // Le type de livrable final attendu par l'utilisateur
}

// Types pour le mode Premium OpenRouter
export interface PremiumUser {
  isAuthenticated: boolean;
  apiKey?: string;
  credits?: number;
  plan?: 'free' | 'premium';
  models?: string[];
}

export interface PremiumModel {
  id: string;
  name: string;
  pricing: {
    prompt: number;
    completion: number;
  };
  context_length: number;
  owned_by: string;
}

export interface OpenRouterResponse {
  models: PremiumModel[];
}

export interface AuthenticationState {
  user: PremiumUser;
  isLoading: boolean;
  error?: string;
}