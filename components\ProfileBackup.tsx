import React, { useState, useEffect } from 'react';
import { profileBackupService, type UserProfile, type UserPreferences } from '../services/profileBackupService';

interface ProfileBackupProps {
  className?: string;
}

/**
 * Composant de gestion des sauvegardes du profil utilisateur
 * Interface pour export/import et configuration des sauvegardes
 */
export const ProfileBackup: React.FC<ProfileBackupProps> = ({ className = '' }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [backupStats, setBackupStats] = useState<any>({});
  const [preferences, setPreferences] = useState<UserPreferences>({});
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [notification, setNotification] = useState<{ type: 'success' | 'error' | 'warning', message: string } | null>(null);

  // Mise à jour des statistiques
  useEffect(() => {
    const updateStats = () => {
      const stats = profileBackupService.getBackupStats();
      const profile = profileBackupService.getProfile();
      setBackupStats(stats);
      setPreferences(profile.preferences);
    };

    updateStats();
    const interval = setInterval(updateStats, 5000);

    // Callback pour les sauvegardes
    profileBackupService.onSave(() => {
      updateStats();
      showNotification('success', 'Profil sauvegardé automatiquement');
    });

    return () => clearInterval(interval);
  }, []);

  const showNotification = (type: 'success' | 'error' | 'warning', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 3000);
  };

  const handleExport = () => {
    try {
      const profileData = profileBackupService.exportProfile();
      const blob = new Blob([profileData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `profil-roony-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      showNotification('success', 'Profil exporté avec succès !');
    } catch (error) {
      showNotification('error', 'Erreur lors de l\'export du profil');
    }
  };

  const handleImport = async () => {
    if (!importFile) return;

    try {
      const text = await importFile.text();
      const success = profileBackupService.importProfile(text);
      
      if (success) {
        showNotification('success', 'Profil importé avec succès !');
        setShowImportDialog(false);
        setImportFile(null);
        
        // Actualiser les stats
        const stats = profileBackupService.getBackupStats();
        const profile = profileBackupService.getProfile();
        setBackupStats(stats);
        setPreferences(profile.preferences);
      } else {
        showNotification('error', 'Fichier de profil invalide');
      }
    } catch (error) {
      showNotification('error', 'Erreur lors de l\'import du profil');
    }
  };

  const handlePreferenceChange = (key: keyof UserPreferences, value: any) => {
    const newPreferences = { ...preferences, [key]: value };
    setPreferences(newPreferences);
    profileBackupService.updatePreferences({ [key]: value });
  };

  const handleForceSave = () => {
    profileBackupService.forceSave();
    showNotification('success', 'Sauvegarde manuelle effectuée !');
  };

  const handleReset = () => {
    if (confirm('⚠️ Êtes-vous sûr de vouloir réinitialiser votre profil ? Cette action est irréversible.')) {
      profileBackupService.resetProfile();
      showNotification('warning', 'Profil réinitialisé');
      
      // Actualiser les stats
      const stats = profileBackupService.getBackupStats();
      const profile = profileBackupService.getProfile();
      setBackupStats(stats);
      setPreferences(profile.preferences);
    }
  };

  const formatLastSave = (timestamp: number) => {
    if (!timestamp) return 'Jamais';
    const date = new Date(timestamp);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffMinutes < 1) return 'À l\'instant';
    if (diffMinutes < 60) return `Il y a ${diffMinutes} min`;
    if (diffMinutes < 1440) return `Il y a ${Math.floor(diffMinutes / 60)}h`;
    return date.toLocaleDateString('fr-FR');
  };

  return (
    <div className={`bg-slate-800/50 rounded-xl shadow-lg border border-slate-700 ${className}`}>
      {/* Notification */}
      {notification && (
        <div className={`fixed top-4 right-4 z-50 px-4 py-2 rounded-lg shadow-lg ${
          notification.type === 'success' ? 'bg-emerald-900/20 text-emerald-400 border border-emerald-500/30' :
          notification.type === 'error' ? 'bg-red-900/20 text-red-400 border border-red-500/30' :
          'bg-yellow-900/20 text-yellow-400 border border-yellow-500/30'
        }`}>
          {notification.message}
        </div>
      )}

      {/* Header */}
      <div
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-slate-700/50 rounded-t-xl transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-slate-700/50 rounded-lg flex items-center justify-center">
            <svg className="w-5 h-5 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
            </svg>
          </div>
          <div>
            <h3 className="font-semibold text-slate-200">💾 Sauvegarde Profil</h3>
            <p className="text-sm text-slate-400">
              Dernière sauvegarde : {formatLastSave(backupStats.lastSaveTime)}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Indicateur de statut */}
          <div className={`w-3 h-3 rounded-full ${
            preferences.autoSave ? 'bg-emerald-400' : 'bg-slate-500'
          }`} title={preferences.autoSave ? 'Sauvegarde automatique active' : 'Sauvegarde automatique désactivée'} />

          <svg
            className={`w-5 h-5 text-slate-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>

      {/* Contenu étendu */}
      {isExpanded && (
        <div className="px-4 pb-4 space-y-4 border-t border-slate-600/50">

          {/* Alerte importante */}
          <div className="bg-orange-900/20 border border-orange-500/30 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <svg className="w-5 h-5 text-orange-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div>
                <h4 className="font-medium text-orange-300">🔒 Confidentialité Garantie</h4>
                <p className="text-sm text-orange-200 mt-1">
                  Toutes vos données sont sauvegardées <strong>uniquement sur votre PC</strong>.
                  Aucune donnée personnelle n'est envoyée sur des serveurs externes.
                </p>
              </div>
            </div>
          </div>

          {/* Statistiques */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-slate-700/30 border border-slate-600/30 rounded-lg p-3">
              <div className="text-2xl font-bold text-blue-400">{backupStats.conversationsCount || 0}</div>
              <div className="text-sm text-slate-300">Conversations</div>
            </div>
            <div className="bg-slate-700/30 border border-slate-600/30 rounded-lg p-3">
              <div className="text-2xl font-bold text-emerald-400">{backupStats.workflowsCount || 0}</div>
              <div className="text-sm text-slate-300">Workflows</div>
            </div>
          </div>

          {/* Configuration */}
          <div className="space-y-3">
            <h4 className="font-medium text-slate-200">⚙️ Configuration</h4>

            {/* Sauvegarde automatique */}
            <div className="flex items-center justify-between">
              <label className="text-sm text-slate-300">Sauvegarde automatique</label>
              <button
                onClick={() => handlePreferenceChange('autoSave', !preferences.autoSave)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  preferences.autoSave ? 'bg-emerald-600' : 'bg-slate-600'
                }`}
              >
                <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  preferences.autoSave ? 'translate-x-6' : 'translate-x-1'
                }`} />
              </button>
            </div>

            {/* Intervalle de sauvegarde */}
            {preferences.autoSave && (
              <div className="flex items-center justify-between">
                <label className="text-sm text-slate-300">Intervalle (secondes)</label>
                <select
                  value={preferences.saveInterval || 30}
                  onChange={(e) => handlePreferenceChange('saveInterval', parseInt(e.target.value))}
                  className="text-sm border border-slate-600 bg-slate-700 text-slate-200 rounded px-2 py-1 focus:ring-emerald-500 focus:border-emerald-500"
                >
                  <option value={15}>15s</option>
                  <option value={30}>30s</option>
                  <option value={60}>1min</option>
                  <option value={300}>5min</option>
                </select>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="space-y-2">
            <h4 className="font-medium text-slate-200">🔧 Actions</h4>

            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={handleForceSave}
                className="px-3 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors text-sm"
              >
                💾 Sauvegarder
              </button>

              <button
                onClick={handleExport}
                className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                📤 Exporter
              </button>

              <button
                onClick={() => setShowImportDialog(true)}
                className="px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm"
              >
                📥 Importer
              </button>

              <button
                onClick={handleReset}
                className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
              >
                🔄 Reset
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Dialog d'import */}
      {showImportDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-slate-800 border border-slate-700 rounded-xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4 text-slate-200">📥 Importer un Profil</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Sélectionner un fichier JSON
                </label>
                <input
                  type="file"
                  accept=".json"
                  onChange={(e) => setImportFile(e.target.files?.[0] || null)}
                  className="w-full text-sm text-slate-300 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-slate-700 file:text-slate-200 hover:file:bg-slate-600"
                />
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleImport}
                  disabled={!importFile}
                  className="flex-1 px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 disabled:bg-slate-600 disabled:cursor-not-allowed transition-colors"
                >
                  Importer
                </button>
                <button
                  onClick={() => {
                    setShowImportDialog(false);
                    setImportFile(null);
                  }}
                  className="flex-1 px-4 py-2 bg-slate-600 text-slate-200 rounded-lg hover:bg-slate-500 transition-colors"
                >
                  Annuler
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileBackup;
