/**
 * Service d'Intégration Stratégique
 * Orchestre toutes les capacités stratégiques de l'agent pour une expérience unifiée
 * 
 * MISSION : Transformer chaque interaction en une démonstration d'expertise stratégique
 */

import { expertConsultantService } from './expertConsultantService';
import { strategicMonitoringService } from './strategicMonitoringService';
import { strategicContinuousImprovementService } from './strategicContinuousImprovementService';
import type { Step } from '../../types';
import type { ContextFile } from './contextFileService';

export interface EnhancedInteraction {
  originalInput: string;
  enhancedPrompt: string;
  strategicContext: string;
  expectedOutcome: string;
  qualityChecks: string[];
  improvementHints: string[];
}

export interface InteractionResult {
  response: string;
  strategicScore: number;
  strengthAreas: string[];
  improvementAreas: string[];
  nextOptimizations: string[];
  learningExtracted: boolean;
}

class StrategicIntegrationService {

  /**
   * Traite une interaction complète avec toutes les capacités stratégiques
   */
  async processStrategicInteraction(
    stepIndex: number,
    userInput: string,
    step: Step,
    contextFiles?: ContextFile[]
  ): Promise<{
    enhancedInteraction: EnhancedInteraction;
    qualityGuidance: string;
    postProcessingInstructions: string;
  }> {
    
    // 1. Générer les améliorations personnalisées basées sur l'historique
    const personalizedImprovements = strategicContinuousImprovementService
      .generatePersonalizedImprovements(step.title, userInput);

    // 2. Créer l'interaction enrichie stratégiquement
    const enhancedInteraction = this.createEnhancedInteraction(
      userInput, 
      step, 
      contextFiles, 
      personalizedImprovements
    );

    // 3. Générer les directives de qualité stratégique
    const qualityGuidance = this.generateQualityGuidance(userInput, step.title);

    // 4. Préparer les instructions de post-traitement
    const postProcessingInstructions = this.generatePostProcessingInstructions();

    return {
      enhancedInteraction,
      qualityGuidance,
      postProcessingInstructions
    };
  }

  /**
   * Évalue et améliore une réponse générée
   */
  async evaluateAndImproveResponse(
    userInput: string,
    aiResponse: string,
    stepTitle: string,
    userFeedback?: { satisfaction: number; comments?: string }
  ): Promise<InteractionResult> {
    
    // 1. Évaluer la qualité stratégique
    const strategicEvaluation = strategicMonitoringService.evaluateStrategicQuality(
      userInput, 
      aiResponse, 
      stepTitle
    );

    // 2. Analyser et apprendre de cette interaction
    strategicContinuousImprovementService.analyzeAndLearn(
      userInput,
      aiResponse,
      stepTitle,
      userFeedback
    );

    // 3. Extraire les enseignements pour les prochaines interactions
    const nextOptimizations = this.extractNextOptimizations(strategicEvaluation);

    return {
      response: aiResponse,
      strategicScore: strategicEvaluation.metrics.overallStrategicScore,
      strengthAreas: strategicEvaluation.strengthAreas,
      improvementAreas: strategicEvaluation.strategicGaps,
      nextOptimizations,
      learningExtracted: true
    };
  }

  /**
   * Génère un prompt système enrichi avec toutes les capacités stratégiques
   */
  generateEnhancedSystemPrompt(
    stepIndex: number,
    userInput: string,
    step: Step,
    contextFiles?: ContextFile[]
  ): string {
    
    // 1. Obtenir le prompt expert de base
    const basePrompt = expertConsultantService.generateExpertSystemPrompt(
      stepIndex, 
      userInput, 
      step, 
      contextFiles
    );

    // 2. Ajouter les améliorations stratégiques personnalisées
    const personalizedImprovements = strategicContinuousImprovementService
      .generatePersonalizedImprovements(step.title, userInput);

    // 3. Intégrer le monitoring de qualité
    const qualityInstructions = this.generateRealTimeQualityInstructions();

    // 4. Ajouter les directives d'évolution continue
    const evolutionGuidance = this.generateEvolutionGuidance();

    return `${basePrompt}

=== AMÉLIORATIONS STRATÉGIQUES PERSONNALISÉES ===
Basées sur l'analyse de vos interactions précédentes :
${personalizedImprovements.map(improvement => `• ${improvement}`).join('\n')}

=== DIRECTIVES DE QUALITÉ EN TEMPS RÉEL ===
${qualityInstructions}

=== GUIDANCE D'ÉVOLUTION CONTINUE ===
${evolutionGuidance}

RAPPEL FINAL : Vous êtes maintenant un STRATÈGE expert qui :
1. Maintient TOUJOURS le fil rouge de l'objectif central
2. Enrichit systématiquement avec des pépites de valeur concrètes
3. Utilise une terminologie experte irréprochable
4. S'améliore constamment grâce à chaque interaction

Excellence stratégique = Votre nouvelle norme absolue !`;
  }

  /**
   * Crée une interaction enrichie stratégiquement
   */
  private createEnhancedInteraction(
    userInput: string,
    step: Step,
    contextFiles?: ContextFile[],
    personalizedImprovements?: string[]
  ): EnhancedInteraction {
    
    const strategicContext = this.generateStrategicContext(userInput, step.title);
    const enhancedPrompt = this.enhancePromptWithStrategicElements(userInput, personalizedImprovements);
    const expectedOutcome = this.defineExpectedOutcome(step.title, userInput);
    const qualityChecks = this.generateQualityChecks(step.title);
    const improvementHints = personalizedImprovements || [];

    return {
      originalInput: userInput,
      enhancedPrompt,
      strategicContext,
      expectedOutcome,
      qualityChecks,
      improvementHints
    };
  }

  /**
   * Génère le contexte stratégique de l'interaction
   */
  private generateStrategicContext(userInput: string, stepTitle: string): string {
    let context = `=== CONTEXTE STRATÉGIQUE ===\n\n`;
    
    // Analyser l'objectif central
    const centralObjective = this.extractCentralObjective(userInput);
    context += `🎯 OBJECTIF CENTRAL DÉTECTÉ: ${centralObjective}\n\n`;

    // Identifier le point de levier
    const leveragePoint = this.identifyLeveragePoint(userInput, stepTitle);
    context += `⚡ POINT DE LEVIER: ${leveragePoint}\n\n`;

    // Prévoir les pépites de valeur potentielles
    const potentialGems = this.identifyPotentialValueGems(userInput, stepTitle);
    context += `💎 PÉPITES DE VALEUR ATTENDUES:\n`;
    potentialGems.forEach(gem => {
      context += `• ${gem}\n`;
    });

    return context;
  }

  /**
   * Enrichit le prompt avec des éléments stratégiques
   */
  private enhancePromptWithStrategicElements(
    userInput: string,
    personalizedImprovements?: string[]
  ): string {
    let enhanced = userInput;

    // Ajouter le contexte d'amélioration continue
    if (personalizedImprovements && personalizedImprovements.length > 0) {
      enhanced += `\n\n=== OPTIMISATIONS PERSONNALISÉES ===\n`;
      enhanced += `Basé sur votre historique d'excellence :\n`;
      personalizedImprovements.forEach(improvement => {
        enhanced += `• ${improvement}\n`;
      });
    }

    return enhanced;
  }

  /**
   * Génère les directives de qualité en temps réel
   */
  private generateRealTimeQualityInstructions(): string {
    return `VÉRIFICATIONS QUALITÉ OBLIGATOIRES AVANT ENVOI :

✅ FIL ROUGE STRATÉGIQUE :
• Ai-je clairement identifié l'objectif central du client ?
• Chaque conseil contribue-t-il explicitement à cet objectif ?
• Le plan d'action maintient-il le cap sur la priorité principale ?

✅ PÉPITES DE VALEUR :
• Ai-je intégré au moins 2 pépites concrètes (outil, contact, astuce) ?
• Mes conseils sont-ils immédiatement actionnables ?
• Ai-je partagé une astuce d'initié ou un point d'attention crucial ?

✅ TERMINOLOGIE EXPERTE :
• Utilise-je les termes techniques précis du domaine ?
• Ai-je évité les approximations et le jargon générique ?
• Ma crédibilité d'expert transpire-t-elle dans chaque phrase ?

✅ ENGAGEMENT HUMAIN :
• Mon ton est-il chaleureux et accessible malgré l'expertise ?
• Ai-je sollicité explicitement la continuation ?
• Le client sait-il exactement quoi faire ensuite ?`;
  }

  /**
   * Génère la guidance d'évolution continue
   */
  private generateEvolutionGuidance(): string {
    const evolution = strategicContinuousImprovementService.assessStrategicEvolution();
    
    return `ÉVOLUTION STRATÉGIQUE ACTUELLE :
Niveau : ${evolution.currentLevel.toUpperCase()}
Prochain jalon : ${evolution.nextMilestone}

FOCUS D'AMÉLIORATION IMMÉDIAT :
${evolution.improvementAreas.length > 0 
  ? evolution.improvementAreas.map(area => `• ${area}`).join('\n')
  : '• Maintenir l\'excellence atteinte'
}

FORCES À EXPLOITER :
${evolution.strengths.length > 0 
  ? evolution.strengths.map(strength => `• ${strength}`).join('\n')
  : '• Développement des forces en cours'
}`;
  }

  /**
   * Génère les directives de qualité pour une interaction
   */
  private generateQualityGuidance(userInput: string, stepTitle: string): string {
    return `=== DIRECTIVES DE QUALITÉ STRATÉGIQUE ===

Pour cette interaction "${stepTitle}" :

🎯 PRIORITÉ ABSOLUE : Maintenir le fil rouge de l'objectif central
💎 ENRICHISSEMENT : Intégrer des pépites de valeur spécifiques au contexte
📚 EXPERTISE : Démontrer la maîtrise terminologique du domaine
🤝 HUMANITÉ : Rester chaleureux et accessible malgré l'expertise technique

SEUILS DE QUALITÉ MINIMUM :
• Score stratégique global : ≥ 75%
• Cohérence stratégique : ≥ 70%
• Enrichissement valeur : ≥ 60%
• Précision terminologique : ≥ 80%

SI CES SEUILS NE SONT PAS ATTEINTS : Reprendre et améliorer la réponse.`;
  }

  /**
   * Génère les instructions de post-traitement
   */
  private generatePostProcessingInstructions(): string {
    return `=== INSTRUCTIONS POST-TRAITEMENT ===

Après génération de la réponse :

1. ÉVALUATION AUTOMATIQUE :
   • Mesurer le score stratégique global
   • Identifier les forces et faiblesses
   • Extraire les apprentissages

2. AMÉLIORATION CONTINUE :
   • Enregistrer les patterns de succès
   • Mettre à jour les recommandations personnalisées
   • Ajuster les seuils de qualité si nécessaire

3. PRÉPARATION PROCHAINE INTERACTION :
   • Intégrer les apprentissages dans le contexte
   • Affiner les pépites de valeur spécialisées
   • Optimiser la terminologie experte`;
  }

  /**
   * Extrait les optimisations pour les prochaines interactions
   */
  private extractNextOptimizations(strategicEvaluation: any): string[] {
    const optimizations: string[] = [];

    if (strategicEvaluation.metrics.strategicConsistency < 80) {
      optimizations.push("Renforcer la référence explicite à l'objectif central dans chaque conseil");
    }

    if (strategicEvaluation.metrics.valueEnrichment < 70) {
      optimizations.push("Augmenter le nombre de pépites de valeur concrètes (outils, contacts, templates)");
    }

    if (strategicEvaluation.metrics.terminologyPrecision < 85) {
      optimizations.push("Affiner la précision terminologique et éviter les approximations");
    }

    if (strategicEvaluation.recommendations.length > 0) {
      strategicEvaluation.recommendations.forEach((rec: any) => {
        if (rec.priority === 'critical' || rec.priority === 'high') {
          optimizations.push(`Priorité ${rec.priority}: ${rec.title}`);
        }
      });
    }

    return optimizations;
  }

  /**
   * Génère un rapport d'interaction stratégique complet
   */
  generateStrategicInteractionReport(
    userInput: string,
    aiResponse: string,
    stepTitle: string,
    result: InteractionResult
  ): string {
    let report = `=== RAPPORT D'INTERACTION STRATÉGIQUE ===\n\n`;
    
    report += `📊 PERFORMANCE STRATÉGIQUE\n`;
    report += `Score Global: ${result.strategicScore}%\n`;
    report += `Étape: ${stepTitle}\n`;
    report += `Date: ${new Date().toLocaleString('fr-FR')}\n\n`;

    if (result.strengthAreas.length > 0) {
      report += `✅ POINTS FORTS:\n`;
      result.strengthAreas.forEach(strength => {
        report += `• ${strength}\n`;
      });
      report += '\n';
    }

    if (result.improvementAreas.length > 0) {
      report += `🎯 AXES D'AMÉLIORATION:\n`;
      result.improvementAreas.forEach(area => {
        report += `• ${area}\n`;
      });
      report += '\n';
    }

    if (result.nextOptimizations.length > 0) {
      report += `🚀 PROCHAINES OPTIMISATIONS:\n`;
      result.nextOptimizations.forEach(optimization => {
        report += `• ${optimization}\n`;
      });
      report += '\n';
    }

    // Ajouter l'évolution stratégique générale
    const evolution = strategicContinuousImprovementService.assessStrategicEvolution();
    report += `📈 ÉVOLUTION STRATÉGIQUE GLOBALE:\n`;
    report += `Niveau actuel: ${evolution.currentLevel}\n`;
    report += `Prochain objectif: ${evolution.nextMilestone}\n`;

    return report;
  }

  // Méthodes utilitaires privées
  private extractCentralObjective(userInput: string): string {
    // Analyse simplifiée pour extraire l'objectif
    const objectiveKeywords = ['besoin', 'veux', 'cherche', 'objectif', 'but', 'souhaite'];
    for (const keyword of objectiveKeywords) {
      const index = userInput.toLowerCase().indexOf(keyword);
      if (index !== -1) {
        const context = userInput.substring(index, index + 100);
        return `Objectif identifié: ${context.split('.')[0]}`;
      }
    }
    return "Objectif central à clarifier avec le client";
  }

  private identifyLeveragePoint(userInput: string, stepTitle: string): string {
    if (stepTitle.toLowerCase().includes('analyse')) {
      return "Comprendre précisément la situation pour orienter l'action";
    }
    if (stepTitle.toLowerCase().includes('plan')) {
      return "Structurer les actions pour un impact maximum";
    }
    return "Identifier l'action qui débloquera la situation";
  }

  private identifyPotentialValueGems(userInput: string, stepTitle: string): string[] {
    const gems: string[] = [];
    
    gems.push("Modèle ou template personnalisé");
    gems.push("Contact officiel pertinent");
    gems.push("Astuce d'expert pour optimiser le processus");
    
    if (userInput.toLowerCase().includes('urgent')) {
      gems.push("Stratégie d'accélération des délais");
    }
    
    return gems;
  }

  private defineExpectedOutcome(stepTitle: string, userInput: string): string {
    return `Réponse stratégique complète pour "${stepTitle}" avec fil rouge maintenu, pépites de valeur intégrées et terminologie experte`;
  }

  private generateQualityChecks(stepTitle: string): string[] {
    return [
      "Fil rouge stratégique maintenu",
      "Pépites de valeur intégrées",
      "Terminologie experte utilisée",
      "Ton humain et accessible",
      "Action claire pour continuer"
    ];
  }
}

// Instance singleton
export const strategicIntegrationService = new StrategicIntegrationService();
