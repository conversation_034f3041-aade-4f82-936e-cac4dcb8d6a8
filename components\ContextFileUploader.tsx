import React, { useState, useRef, useEffect } from 'react';
import { contextFileService, type ContextFile, type FileAnalysis } from '../src/services/contextFileService';

interface ContextFileUploaderProps {
  onFilesChange?: (files: ContextFile[]) => void;
  compact?: boolean; // Mode compact pour intégration dans UserInput
}

export const ContextFileUploader: React.FC<ContextFileUploaderProps> = ({ 
  onFilesChange, 
  compact = false 
}) => {
  const [contextFiles, setContextFiles] = useState<ContextFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [showAnalysis, setShowAnalysis] = useState<Record<string, FileAnalysis>>({});
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Charger les fichiers existants au montage
  useEffect(() => {
    const files = contextFileService.getAllContextFiles();
    setContextFiles(files);
    onFilesChange?.(files);
  }, [onFilesChange]);

  /**
   * Gère le téléchargement de fichiers
   */
  const handleFileUpload = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    setIsUploading(true);
    setUploadError(null);

    try {
      const uploadedFiles: ContextFile[] = [];
      
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        try {
          const contextFile = await contextFileService.addContextFile(file);
          uploadedFiles.push(contextFile);
        } catch (error) {
          console.error(`Erreur upload fichier ${file.name}:`, error);
          setUploadError(`Erreur avec ${file.name}: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
        }
      }

      if (uploadedFiles.length > 0) {
        const allFiles = contextFileService.getAllContextFiles();
        setContextFiles(allFiles);
        onFilesChange?.(allFiles);
      }
    } catch (error) {
      setUploadError(error instanceof Error ? error.message : 'Erreur lors du téléchargement');
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  /**
   * Gère la suppression d'un fichier
   */
  const handleFileRemove = (fileId: string) => {
    contextFileService.removeContextFile(fileId);
    const updatedFiles = contextFileService.getAllContextFiles();
    setContextFiles(updatedFiles);
    onFilesChange?.(updatedFiles);
    
    // Supprimer l'analyse si elle existe
    setShowAnalysis((prev: Record<string, FileAnalysis>) => {
      const newAnalysis = { ...prev };
      delete newAnalysis[fileId];
      return newAnalysis;
    });
  };

  /**
   * Analyse un fichier
   */
  const analyzeFile = async (fileId: string) => {
    const analysis = await contextFileService.analyzeFile(fileId);
    if (analysis) {
      setShowAnalysis((prev: Record<string, FileAnalysis>) => ({
        ...prev,
        [fileId]: analysis
      }));
    }
  };

  /**
   * Gère le drag and drop
   */
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files) {
      handleFileUpload(e.dataTransfer.files);
    }
  };

  /**
   * Efface tous les fichiers
   */
  const clearAllFiles = () => {
    contextFileService.clearAllContextFiles();
    setContextFiles([]);
    setShowAnalysis({});
    onFilesChange?.([]);
  };

  /**
   * Formate la taille d'un fichier
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * Obtient l'icône pour un type de fichier
   */
  const getFileIcon = (contentType: string) => {
    switch (contentType) {
      case 'image':
        return '🖼️';
      case 'document':
        return '📄';
      case 'text':
        return '📝';
      default:
        return '📎';
    }
  };

  // Mode compact pour intégration dans UserInput
  if (compact) {
    return (
      <div className="flex items-center gap-2">
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleFileUpload(e.target.files)}
          className="hidden"
          accept=".txt,.md,.json,.csv,.log,.jpg,.jpeg,.png,.gif,.bmp,.webp,.pdf,.doc,.docx,.rtf"
        />
        
        <button
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
          className={`
            p-2 rounded-lg transition-colors duration-200 relative
            ${contextFiles.length > 0 
              ? 'bg-emerald-600/20 text-emerald-400 border border-emerald-500/30' 
              : 'bg-slate-600/20 text-slate-400 border border-slate-500/30'
            }
            hover:bg-slate-500/30 disabled:opacity-50 disabled:cursor-not-allowed
          `}
          title={`${contextFiles.length > 0 ? `${contextFiles.length} fichier(s) ajouté(s)` : 'Ajouter des fichiers contextuels'}`}
        >
          {isUploading ? (
            <div className="w-4 h-4 border-2 border-slate-400/30 border-t-slate-400 rounded-full animate-spin"></div>
          ) : (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
            </svg>
          )}
          
          {contextFiles.length > 0 && (
            <div className="absolute -top-1 -right-1 bg-emerald-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {contextFiles.length}
            </div>
          )}
        </button>

        {uploadError && (
          <div className="text-red-400 text-xs">
            {uploadError}
          </div>
        )}
      </div>
    );
  }

  // Mode complet
  return (
    <div className="space-y-4">
      {/* Zone de téléchargement */}
      <div
        className={`
          border-2 border-dashed rounded-lg p-6 text-center transition-colors duration-200
          ${dragActive 
            ? 'border-indigo-400 bg-indigo-900/20' 
            : 'border-slate-600 hover:border-slate-500'
          }
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleFileUpload(e.target.files)}
          className="hidden"
          accept=".txt,.md,.json,.csv,.log,.jpg,.jpeg,.png,.gif,.bmp,.webp,.pdf,.doc,.docx,.rtf"
        />

        <div className="flex flex-col items-center gap-3">
          <div className="text-4xl text-slate-400">
            {isUploading ? '⏳' : '📎'}
          </div>
          
          <div className="text-slate-300">
            <p className="font-medium">
              {isUploading 
                ? 'Téléchargement en cours...' 
                : 'Glissez vos fichiers ici ou cliquez pour parcourir'
              }
            </p>
            <p className="text-sm text-slate-400 mt-1">
              Formats supportés: Images, Documents, Fichiers texte (max 100MB)
            </p>
          </div>

          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
            className="bg-indigo-600 hover:bg-indigo-500 disabled:bg-slate-600 text-white py-2 px-4 rounded-lg transition-colors"
          >
            {isUploading ? 'Chargement...' : 'Parcourir les fichiers'}
          </button>
        </div>
      </div>

      {/* Message d'erreur */}
      {uploadError && (
        <div className="bg-red-900/30 border border-red-600/50 rounded-lg p-3 text-red-200">
          {uploadError}
        </div>
      )}

      {/* Liste des fichiers */}
      {contextFiles.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-slate-200">
              Fichiers contextuels ({contextFiles.length})
            </h3>
            <button
              onClick={clearAllFiles}
              className="text-red-400 hover:text-red-300 text-sm transition-colors"
            >
              Tout effacer
            </button>
          </div>

          <div className="space-y-2">
            {contextFiles.map((file: ContextFile) => (
              <div key={file.id} className="bg-slate-800/50 rounded-lg p-3 border border-slate-600">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3 flex-1">
                    <span className="text-2xl">{getFileIcon(file.contentType)}</span>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="text-slate-200 font-medium truncate">{file.name}</p>
                        <span className="text-xs text-slate-400 bg-slate-700 px-2 py-1 rounded">
                          {file.contentType}
                        </span>
                      </div>
                      
                      <p className="text-xs text-slate-400 mt-1">
                        {formatFileSize(file.size)} • {file.uploadedAt.toLocaleString()}
                      </p>

                      {file.extractedText && (
                        <p className="text-xs text-slate-300 mt-2 line-clamp-2">
                          {file.extractedText.substring(0, 100)}...
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-3">
                    <button
                      onClick={() => analyzeFile(file.id)}
                      className="text-indigo-400 hover:text-indigo-300 p-1 transition-colors"
                      title="Analyser le fichier"
                    >
                      🔍
                    </button>
                    
                    <button
                      onClick={() => handleFileRemove(file.id)}
                      className="text-red-400 hover:text-red-300 p-1 transition-colors"
                      title="Supprimer le fichier"
                    >
                      🗑️
                    </button>
                  </div>
                </div>

                {/* Analyse du fichier */}
                {showAnalysis[file.id] && (
                  <div className="mt-3 pt-3 border-t border-slate-600">
                    <div className="bg-slate-700/50 rounded p-3 space-y-2">
                      <h4 className="text-sm font-medium text-slate-200">📊 Analyse</h4>
                      <p className="text-xs text-slate-300">{showAnalysis[file.id].summary}</p>
                      
                      {showAnalysis[file.id].keyPoints.length > 0 && (
                        <div>
                          <p className="text-xs font-medium text-slate-300 mb-1">Points clés:</p>
                          <ul className="text-xs text-slate-400 space-y-1">
                            {showAnalysis[file.id].keyPoints.map((point: string, idx: number) => (
                              <li key={idx}>• {point}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Statistiques */}
      {contextFiles.length > 0 && (
        <div className="bg-slate-800/30 rounded-lg p-3 border border-slate-600">
          <div className="text-xs text-slate-400">
            {(() => {
              const stats = contextFileService.getFileStats();
              return `${stats.totalFiles} fichier(s) • ${formatFileSize(stats.totalSize)} • ${Object.entries(stats.typeBreakdown).map(([type, count]: [string, number]) => `${count} ${type}`).join(', ')}`;
            })()}
          </div>
        </div>
      )}
    </div>
  );
};
