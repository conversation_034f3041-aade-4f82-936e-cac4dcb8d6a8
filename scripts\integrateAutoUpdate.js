/**
 * Script d'intégration finale du système de mise à jour automatique
 * Studio Agentique Roony - Août 2025
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function integrateAutoUpdateSystem() {
    console.log('🔧 Intégration du système de mise à jour automatique...');
    console.log('='.repeat(70));

    try {
        // Étape 1: Vérifier les fichiers générés
        console.log('\n1. 📁 Vérification des fichiers générés');
        
        const constantsUpdatedPath = path.join(__dirname, '..', 'constants_updated.ts');
        const reportPath = path.join(__dirname, '..', 'model_update_report.md');
        
        const constantsExists = await fs.access(constantsUpdatedPath).then(() => true).catch(() => false);
        const reportExists = await fs.access(reportPath).then(() => true).catch(() => false);
        
        console.log(`   ${constantsExists ? '✅' : '❌'} constants_updated.ts`);
        console.log(`   ${reportExists ? '✅' : '❌'} model_update_report.md`);

        if (!constantsExists) {
            throw new Error('Fichier constants_updated.ts manquant. Exécutez d\'abord generateUpdatedConstants.js');
        }

        // Étape 2: Créer une sauvegarde des constantes actuelles
        console.log('\n2. 💾 Sauvegarde des constantes actuelles');
        
        const currentConstantsPath = path.join(__dirname, '..', 'constants.ts');
        const backupPath = path.join(__dirname, '..', `constants_backup_${Date.now()}.ts`);
        
        try {
            await fs.copyFile(currentConstantsPath, backupPath);
            console.log(`   ✅ Sauvegarde créée: ${path.basename(backupPath)}`);
        } catch (error) {
            console.warn(`   ⚠️ Impossible de créer la sauvegarde: ${error.message}`);
        }

        // Étape 3: Afficher les statistiques de mise à jour
        console.log('\n3. 📊 Statistiques de la mise à jour');
        
        if (reportExists) {
            const reportContent = await fs.readFile(reportPath, 'utf-8');
            const lines = reportContent.split('\n');
            
            lines.forEach(line => {
                if (line.includes('Total modèles') || 
                    line.includes('Modèles gratuits') || 
                    line.includes('Modèles premium')) {
                    console.log(`   ${line.replace('- **', '').replace('**:', ':')}`);
                }
            });
        }

        // Étape 4: Instructions de déploiement
        console.log('\n4. 🚀 Instructions de déploiement');
        console.log('   Pour activer le nouveau système:');
        console.log('   1. Examinez le fichier constants_updated.ts');
        console.log('   2. Si satisfait, remplacez constants.ts par constants_updated.ts');
        console.log('   3. Redémarrez l\'application');
        console.log('   4. Vérifiez les logs de détection automatique');

        // Étape 5: Commandes de déploiement automatique (optionnel)
        console.log('\n5. ⚡ Déploiement automatique (optionnel)');
        console.log('   Voulez-vous activer automatiquement ? (y/N)');
        
        // Pour un déploiement automatique, décommentez les lignes suivantes:
        /*
        await fs.copyFile(constantsUpdatedPath, currentConstantsPath);
        console.log('   ✅ Constantes mises à jour activées');
        */

        // Étape 6: Vérification de l'intégration
        console.log('\n6. 🧪 Tests d\'intégration recommandés');
        console.log('   • node scripts/testImprovedModelDetection.js');
        console.log('   • Démarrer l\'application et vérifier les notifications');
        console.log('   • Tester le mode gratuit et premium');
        console.log('   • Vérifier la détection des nouveaux modèles');

        // Étape 7: Documentation
        console.log('\n7. 📚 Documentation générée');
        console.log(`   • ${path.basename(reportPath)} - Rapport détaillé`);
        console.log('   • docs/RAPPORT_MISE_A_JOUR_MODELES.md - Documentation complète');
        console.log('   • constants_updated.ts - Nouvelles constantes');

        console.log('\n✅ Intégration préparée avec succès !');
        console.log('='.repeat(70));

        return {
            success: true,
            files: {
                constants: constantsExists,
                report: reportExists,
                backup: backupPath
            }
        };

    } catch (error) {
        console.error('❌ Erreur lors de l\'intégration:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Fonction pour activer automatiquement (avec confirmation)
async function activateAutoUpdate() {
    console.log('🚀 Activation automatique du système...');
    
    try {
        const constantsUpdatedPath = path.join(__dirname, '..', 'constants_updated.ts');
        const currentConstantsPath = path.join(__dirname, '..', 'constants.ts');
        
        // Copier les nouvelles constantes
        await fs.copyFile(constantsUpdatedPath, currentConstantsPath);
        console.log('✅ Constantes mises à jour activées');
        
        // Nettoyer le fichier temporaire
        await fs.unlink(constantsUpdatedPath);
        console.log('🧹 Fichier temporaire nettoyé');
        
        console.log('\n🎉 Système de mise à jour automatique activé !');
        console.log('   Redémarrez l\'application pour voir les changements.');
        
        return { success: true };
        
    } catch (error) {
        console.error('❌ Erreur lors de l\'activation:', error);
        return { success: false, error: error.message };
    }
}

// Fonction pour créer un script de surveillance continue
async function createMonitoringScript() {
    const monitoringScript = `#!/bin/bash
# Script de surveillance des modèles OpenRouter
# À exécuter via cron pour des mises à jour automatiques

echo "🔄 Vérification des nouveaux modèles OpenRouter..."
cd "${path.dirname(__dirname)}"

# Exécuter le test de détection
node scripts/testImprovedModelDetection.js > /tmp/openrouter_check.log 2>&1

# Vérifier si de nouveaux modèles sont disponibles
if grep -q "nouveaux modèles" /tmp/openrouter_check.log; then
    echo "📧 Nouveaux modèles détectés - envoi de notification..."
    # Ici, ajouter la logique de notification (email, Slack, etc.)
fi

echo "✅ Vérification terminée"
`;

    const scriptPath = path.join(__dirname, '..', 'scripts', 'monitor_models.sh');
    await fs.writeFile(scriptPath, monitoringScript);
    console.log(`📱 Script de surveillance créé: ${scriptPath}`);
}

// Exécution
const isMainModule = import.meta.url === `file://${process.argv[1]}`;
if (isMainModule) {
    integrateAutoUpdateSystem().then(async result => {
        if (result.success) {
            console.log('\n🔧 Options supplémentaires:');
            console.log('   Pour activer immédiatement: node scripts/integrateAutoUpdate.js --activate');
            console.log('   Pour créer le monitoring: node scripts/integrateAutoUpdate.js --monitor');
            
            // Vérifier les arguments de ligne de commande
            if (process.argv.includes('--activate')) {
                await activateAutoUpdate();
            }
            
            if (process.argv.includes('--monitor')) {
                await createMonitoringScript();
            }
        }
    });
}

export { integrateAutoUpdateSystem, activateAutoUpdate, createMonitoringScript };
