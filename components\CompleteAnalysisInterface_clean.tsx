import React, { useState, useEffect } from 'react';
import { Brain, ArrowRight, <PERSON>rkles, Target, MessageCircle, ArrowLeft } from 'lucide-react';
import RoonyConversationalInterface from './RoonyConversationalInterfaceV2';
import { ContextFileUploader } from './ContextFileUploader';
import { WorkflowTracker } from './WorkflowTracker';
import { UserGuidance } from './UserGuidance';
import { EngagementPrompts } from './EngagementPrompts';
import { FinalActionPlanGenerator } from './FinalActionPlanGenerator';
import { ReportGenerator } from './ReportGenerator';
import { EnhancedReportGenerator } from './EnhancedReportGenerator';
import { ChatInterface } from './ChatInterface';
import { UserInput } from './UserInput';
import { ThinkingSpace } from './ThinkingSpace';
import { ModelMonitor } from './ModelMonitor';
import LanguageComplianceMonitor from './LanguageComplianceMonitor';
import TranslationMonitor from './TranslationMonitor';
import { ProfileBackup } from './ProfileBackup';
import ApiKeyStats from './ApiKeyStats';
import ModelUpdateNotification from './ModelUpdateNotification';
import { WORKFLOW_STEPS } from '../constants';
import type { ContextFile } from '../src/services/contextFileService';
import type { Message } from '../types';

interface PROFData {
  personnage: string;
  role: string;
  objectif: string;
  format: string;
  rawConversation: Message[];
}

interface CompleteAnalysisInterfaceProps {
  onSubmit: (prompt: string, contextFiles?: ContextFile[]) => void;
  isProcessing: boolean;
  conversation?: Message[];
  reasoningLog?: string[];
  currentStepIndex?: number;
  onSendMessage?: (message: string) => void;
  onBackToSelection?: () => void;
  error?: string;
}

export const CompleteAnalysisInterface: React.FC<CompleteAnalysisInterfaceProps> = ({ 
  onSubmit, 
  isProcessing,
  conversation = [],
  reasoningLog = [],
  currentStepIndex = 0,
  onSendMessage,
  onBackToSelection,
  error
}) => {
  const [hasStartedConversation, setHasStartedConversation] = useState(false);
  const [isAnalysisStarted, setIsAnalysisStarted] = useState(false);
  const [showApiStats, setShowApiStats] = useState(false);
  const [initialProblem, setInitialProblem] = useState('');
  const [showDirectInput, setShowDirectInput] = useState(false);
  const [directProblemText, setDirectProblemText] = useState('');

  const handleAnalysisReady = (profData: PROFData, contextFiles?: ContextFile[]) => {
    // Extraire le problème principal de la conversation
    const problemDescription = profData.rawConversation
      .filter(msg => msg.sender === 'user')
      .map(msg => msg.text)
      .join(' ');
    
    // Sauvegarder le problème initial pour les outils locaux
    setInitialProblem(problemDescription);
    
    // Marquer que l'analyse a commencé
    setIsAnalysisStarted(true);
    
    // Appeler onSubmit avec le problème simple pour déclencher le workflow dans App.tsx
    onSubmit(problemDescription, contextFiles);
  };

  const handleStartConversation = () => {
    setHasStartedConversation(true);
  };

  const handleShowDirectInput = () => {
    setShowDirectInput(true);
  };

  const handleDirectSubmit = () => {
    if (directProblemText.trim()) {
      setInitialProblem(directProblemText.trim());
      setIsAnalysisStarted(true);
      onSubmit(directProblemText.trim());
    }
  };

  const handleSuggestedAction = (suggestion: string) => {
    if (onSendMessage) {
      onSendMessage(suggestion);
    }
  };

  // Détecter quand l'analyse démarre automatiquement
  useEffect(() => {
    // Si on a une conversation avec du contenu et qu'on n'est pas en mode conversationnel initial
    if (conversation.length > 0 && !hasStartedConversation && !isAnalysisStarted) {
      console.log('🎯 Détection automatique du démarrage d\'analyse');
      setIsAnalysisStarted(true);
      
      // Extraire le problème initial depuis la conversation s'il n'est pas encore défini
      if (!initialProblem && conversation.length > 0) {
        const userMessages = conversation.filter(msg => msg.sender === 'user');
        if (userMessages.length > 0) {
          setInitialProblem(userMessages[0].text);
        }
      }
    }
  }, [conversation, hasStartedConversation, isAnalysisStarted, initialProblem]);

  // Si l'analyse a démarré, afficher l'interface 3 colonnes
  if (isAnalysisStarted) {
    return (
      <div className="flex-grow flex container mx-auto p-4 gap-4 overflow-hidden">
        {/* COLONNE GAUCHE - Progression + Suggestions + Outils (25%) */}
        <aside className="w-1/4 flex flex-col gap-4">
          <div className="bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 overflow-hidden">
            <WorkflowTracker steps={WORKFLOW_STEPS} currentStepIndex={currentStepIndex || 0} />
          </div>

          <div className="bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 p-4">
            <UserGuidance
              currentStep={currentStepIndex || 0}
              totalSteps={WORKFLOW_STEPS.length}
              isProcessing={isProcessing}
            />
          </div>

          {/* Suggestions d'engagement */}
          {!isProcessing && (currentStepIndex || 0) < WORKFLOW_STEPS.length - 1 && (currentStepIndex || 0) > 0 && (
            <div className="bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 p-4">
              <EngagementPrompts
                currentStep={currentStepIndex || 0}
                totalSteps={WORKFLOW_STEPS.length}
                onSuggestedAction={handleSuggestedAction}
              />
            </div>
          )}

          {/* Générateur de Plan d'Action Final */}
          <FinalActionPlanGenerator
            initialProblem={initialProblem}
            currentStepIndex={currentStepIndex || 0}
            className="flex-shrink-0"
          />

          {/* Générateur de rapport stratégique amélioré v4.3 */}
          {initialProblem && (currentStepIndex || 0) > 1 && (
            <EnhancedReportGenerator
              initialProblem={initialProblem}
              conversation={conversation}
              finalPrompt={'Analyse en cours'} // Utilisera le finalPrompt du workflow
              currentStepIndex={currentStepIndex || 0}
              className="flex-shrink-0"
            />
          )}

          {/* Générateur de rapport legacy (fallback) */}
          {initialProblem && (currentStepIndex || 0) > 1 && (
            <details className="bg-slate-800/30 rounded-lg border border-slate-600/50">
              <summary className="p-3 cursor-pointer text-xs text-slate-400 hover:text-slate-300">
                📊 Rapport Legacy (Ancien Système)
              </summary>
              <div className="p-3 pt-0">
                <ReportGenerator
                  initialProblem={initialProblem}
                  conversation={conversation}
                  reasoningLog={reasoningLog}
                  currentStepIndex={currentStepIndex || 0}
                />
              </div>
            </details>
          )}
        </aside>

        {/* COLONNE CENTRE - Chat + Input (50%) */}
        <div className="flex-1 flex flex-col bg-slate-800/50 rounded-2xl shadow-2xl border border-slate-700 overflow-hidden">
          <ChatInterface conversation={conversation} isProcessing={isProcessing} />
          <div className="p-4 border-t border-slate-700">
            {error && <p className="text-red-400 text-center mb-2">{error}</p>}
            {onSendMessage && (
              <UserInput
                isProcessing={isProcessing}
                onSendMessage={onSendMessage}
                isWorkflowStarted={true}
                isWorkflowComplete={(currentStepIndex || 0) >= WORKFLOW_STEPS.length - 1}
                lastAiMessage={conversation.length > 0 ? conversation[conversation.length - 1]?.sender === 'ai' ? conversation[conversation.length - 1]?.text : undefined : undefined}
              />
            )}
          </div>
        </div>

        {/* COLONNE DROITE - Raisonnement + Modèles + Outils (25%) */}
        <aside className="w-1/4 flex flex-col gap-4">
          {/* Bouton de retour à l'accueil */}
          {onBackToSelection && (
            <div className="flex-shrink-0">
              <button
                onClick={onBackToSelection}
                className="w-full bg-slate-700 hover:bg-slate-600 text-slate-200 text-sm font-semibold py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Retour à l'accueil
              </button>
            </div>
          )}

          <ThinkingSpace log={reasoningLog} className="flex-grow" />
          <ModelMonitor className="flex-shrink-0" />
          <LanguageComplianceMonitor className="flex-shrink-0" />
          <TranslationMonitor className="flex-shrink-0" />

          {/* Composant de sauvegarde du profil utilisateur */}
          <ProfileBackup className="flex-shrink-0" />

          {/* Bouton pour afficher les statistiques des clés API */}
          <div className="flex-shrink-0">
            <button
              onClick={() => setShowApiStats(true)}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-lg"
            >
              📊 Statistiques API
            </button>
          </div>

          {/* Notification de mise à jour des modèles */}
          <ModelUpdateNotification className="flex-shrink-0" />

          <footer className="flex-shrink-0 text-center py-2">
            <a href="https://flexodiv.com" target="_blank" rel="noopener noreferrer" className="inline-block">
              <img src="https://res.cloudinary.com/dte5zykne/image/upload/v1750807840/02-Logo-FlexoDiv_uoxcao.png" alt="Logo FlexoDiv" className="h-10 mx-auto opacity-60 hover:opacity-100 transition-opacity" />
            </a>
          </footer>
        </aside>

        {/* Modal des statistiques API */}
        <ApiKeyStats
          isVisible={showApiStats}
          onClose={() => setShowApiStats(false)}
        />
      </div>
    );
  }

  // Si l'entretien conversationnel a commencé mais l'analyse n'a pas encore démarré
  if (hasStartedConversation && !isAnalysisStarted) {
    return (
      <div className="bg-slate-800/50 rounded-2xl shadow-2xl border border-slate-700 h-full flex flex-col">
        <RoonyConversationalInterface 
          onAnalysisReady={handleAnalysisReady}
          isProcessing={isProcessing}
        />
      </div>
    );
  }

  // Si la saisie directe est activée
  if (showDirectInput && !isAnalysisStarted) {
    return (
      <div className="pt-6 pb-6">
        <div className="max-w-2xl mx-auto text-center mb-8">
          <div className="bg-gradient-to-r from-blue-900/30 to-indigo-900/30 p-6 rounded-2xl border border-blue-500/30 mb-6">
            <div className="flex items-center justify-center gap-3 mb-3">
              <Target className="w-7 h-7 text-blue-400" />
              <h2 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-400">
                Décrivez votre problème
              </h2>
            </div>
            <p className="text-slate-300 text-base leading-relaxed">
              Décrivez directement votre situation ou problème. Rooney va immédiatement 
              commencer une analyse experte approfondie.
            </p>
          </div>

          <div className="bg-slate-800/50 rounded-2xl p-6 border border-slate-600">
            <textarea
              value={directProblemText}
              onChange={(e) => setDirectProblemText(e.target.value)}
              placeholder="Décrivez votre problème, situation ou défi en détail..."
              className="w-full h-40 bg-slate-700/50 border border-slate-600 rounded-xl p-4 text-slate-200 placeholder-slate-400 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isProcessing}
            />
            <div className="flex justify-between items-center mt-4">
              <button
                onClick={() => setShowDirectInput(false)}
                className="text-slate-400 hover:text-slate-300 text-sm transition-colors"
              >
                ← Retour aux options
              </button>
              <button
                onClick={handleDirectSubmit}
                disabled={!directProblemText.trim() || isProcessing}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 disabled:from-slate-600 disabled:to-slate-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100 flex items-center gap-2"
              >
                {isProcessing ? (
                  <>
                    <svg className="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Analyse en cours...
                  </>
                ) : (
                  <>
                    Lancer l'Analyse
                    <ArrowRight className="w-5 h-5" />
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Interface de démarrage par défaut
  return (
    <div className="pt-6 pb-6">
      {/* Titre principal */}
      <div className="max-w-2xl mx-auto text-center mb-8">
        <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400 mb-4">
          Choisissez votre méthode d'analyse
        </h2>
        <p className="text-slate-300 text-base leading-relaxed">
          Deux approches pour obtenir une analyse experte adaptée à vos besoins
        </p>
      </div>

      {/* Deux options */}
      <div className="max-w-4xl mx-auto grid md:grid-cols-2 gap-6 mb-8">
        
        {/* Option 1: Entretien conversationnel */}
        <div className="bg-gradient-to-r from-purple-900/30 to-pink-900/30 p-6 rounded-2xl border border-purple-500/30">
          <div className="flex items-center gap-3 mb-4">
            <MessageCircle className="w-7 h-7 text-purple-400" />
            <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
              Entretien avec Rooney
            </h3>
          </div>
          
          <p className="text-slate-300 text-sm leading-relaxed mb-4">
            <strong>Approche conversationnelle</strong> où Rooney mène personnellement 
            l'entretien pour collecter toutes les informations selon la méthodologie <strong>P.R.O.F.</strong>
          </p>

          <div className="grid grid-cols-2 gap-2 text-xs mb-4">
            <div className="bg-blue-900/30 p-2 rounded border border-blue-500/30">
              <div className="font-semibold text-blue-300">P</div>
              <div className="text-slate-400">Personnage</div>
            </div>
            <div className="bg-green-900/30 p-2 rounded border border-green-500/30">
              <div className="font-semibold text-green-300">R</div>
              <div className="text-slate-400">Rôle</div>
            </div>
            <div className="bg-orange-900/30 p-2 rounded border border-orange-500/30">
              <div className="font-semibold text-orange-300">O</div>
              <div className="text-slate-400">Objectif</div>
            </div>
            <div className="bg-purple-900/30 p-2 rounded border border-purple-500/30">
              <div className="font-semibold text-purple-300">F</div>
              <div className="text-slate-400">Format</div>
            </div>
          </div>

          <button
            onClick={handleStartConversation}
            disabled={isProcessing}
            className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 disabled:from-slate-600 disabled:to-slate-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100 flex items-center justify-center gap-2"
          >
            <MessageCircle className="w-5 h-5" />
            Commencer l'Entretien
          </button>
          
          <p className="text-slate-400 text-xs mt-2 text-center">
            Recommandé pour une analyse complète et personnalisée
          </p>
        </div>

        {/* Option 2: Saisie directe */}
        <div className="bg-gradient-to-r from-blue-900/30 to-indigo-900/30 p-6 rounded-2xl border border-blue-500/30">
          <div className="flex items-center gap-3 mb-4">
            <Target className="w-7 h-7 text-blue-400" />
            <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-400">
              Analyse Directe
            </h3>
          </div>
          
          <p className="text-slate-300 text-sm leading-relaxed mb-4">
            <strong>Approche directe</strong> où vous décrivez immédiatement votre problème 
            et Rooney lance directement une analyse experte approfondie.
          </p>

          <div className="bg-slate-700/30 rounded-lg p-3 mb-4">
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="w-4 h-4 text-blue-400" />
              <span className="text-sm font-medium text-slate-200">Avantages</span>
            </div>
            <ul className="text-xs text-slate-300 space-y-1">
              <li>• Analyse immédiate</li>
              <li>• Plus rapide</li>
              <li>• Idéal si vous savez exactement quoi demander</li>
            </ul>
          </div>

          <button
            onClick={handleShowDirectInput}
            disabled={isProcessing}
            className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 disabled:from-slate-600 disabled:to-slate-600 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100 flex items-center justify-center gap-2"
          >
            <Target className="w-5 h-5" />
            Décrire le Problème
          </button>
          
          <p className="text-slate-400 text-xs mt-2 text-center">
            Parfait pour un problème clairement défini
          </p>
        </div>
      </div>

      {/* Upload de fichiers contextuels */}
      <div className="max-w-2xl mx-auto">
        <div className="bg-slate-800/50 rounded-2xl p-6 border border-slate-600">
          <div className="flex items-center gap-3 mb-4">
            <svg className="w-6 h-6 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
            </svg>
            <h3 className="text-lg font-semibold text-slate-200">Fichiers Contextuels (Optionnel)</h3>
          </div>
          <p className="text-slate-400 text-sm mb-4">
            Ajoutez des documents, rapports ou données pour enrichir l'analyse de Rooney
          </p>
          <ContextFileUploader onFilesChange={() => {}} />
        </div>
      </div>
    </div>
  );
};

export default CompleteAnalysisInterface;
