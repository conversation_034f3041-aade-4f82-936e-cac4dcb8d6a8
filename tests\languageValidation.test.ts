/**
 * Tests pour le système de validation de langue française
 */

import { languageValidationService } from '../services/languageValidationService';

// Exemples de textes pour les tests
const TEST_TEXTS = {
    french: {
        simple: "Bonjour, comment allez-vous aujourd'hui ?",
        complex: "L'analyse de ce problème complexe nécessite une approche méthodologique rigoureuse. Nous devons d'abord identifier les contraintes principales, puis évaluer les différentes solutions possibles en tenant compte des ressources disponibles.",
        withAccents: "Voici une réponse détaillée avec des caractères accentués : à, é, è, ê, ë, î, ï, ô, ö, ù, û, ü, ÿ, ç. Ces éléments sont essentiels pour une détection précise du français.",
        contractions: "C'est l'occasion d'analyser qu'il n'y a pas d'autre solution que celle-ci. J'espère que vous m'avez compris."
    },
    english: {
        simple: "Hello, how are you doing today?",
        complex: "The analysis of this complex problem requires a rigorous methodological approach. We must first identify the main constraints, then evaluate the different possible solutions taking into account the available resources.",
        technical: "This implementation uses advanced algorithms and machine learning techniques to optimize performance and ensure scalability."
    },
    mixed: {
        frenchEnglish: "Bonjour, this is a mixed response avec du français et de l'anglais. We need to analyze cette situation carefully.",
        englishFrench: "Hello, voici une réponse mixte with French et English words. Il faut être careful about this."
    },
    problematic: {
        empty: "",
        tooShort: "Ok.",
        numbers: "123 456 789 000",
        symbols: "!@#$%^&*()_+-=[]{}|;':\",./<>?"
    }
};

describe('Service de Validation de Langue Française', () => {
    beforeEach(() => {
        // Réinitialiser les statistiques avant chaque test
        languageValidationService.resetStats();
    });

    describe('Détection du français', () => {
        test('devrait détecter correctement un texte français simple', () => {
            const result = languageValidationService.validateFrenchResponse(TEST_TEXTS.french.simple);
            
            expect(result.isValid).toBe(true);
            expect(result.detectedLanguage).toBe('french');
            expect(result.confidence).toBeGreaterThan(60);
            expect(result.frenchScore).toBeGreaterThan(result.englishScore);
        });

        test('devrait détecter correctement un texte français complexe', () => {
            const result = languageValidationService.validateFrenchResponse(TEST_TEXTS.french.complex);
            
            expect(result.isValid).toBe(true);
            expect(result.detectedLanguage).toBe('french');
            expect(result.confidence).toBeGreaterThan(70);
        });

        test('devrait détecter les accents français', () => {
            const result = languageValidationService.validateFrenchResponse(TEST_TEXTS.french.withAccents);
            
            expect(result.isValid).toBe(true);
            expect(result.detectedLanguage).toBe('french');
            expect(result.frenchScore).toBeGreaterThan(80);
        });

        test('devrait détecter les contractions françaises', () => {
            const result = languageValidationService.validateFrenchResponse(TEST_TEXTS.french.contractions);
            
            expect(result.isValid).toBe(true);
            expect(result.detectedLanguage).toBe('french');
            expect(result.confidence).toBeGreaterThan(70);
        });
    });

    describe('Détection de l\'anglais', () => {
        test('devrait rejeter un texte anglais simple', () => {
            const result = languageValidationService.validateFrenchResponse(TEST_TEXTS.english.simple);
            
            expect(result.isValid).toBe(false);
            expect(result.detectedLanguage).toBe('english');
            expect(result.englishScore).toBeGreaterThan(result.frenchScore);
            expect(result.issues).toContain(expect.stringMatching(/anglais/i));
        });

        test('devrait rejeter un texte anglais complexe', () => {
            const result = languageValidationService.validateFrenchResponse(TEST_TEXTS.english.complex);
            
            expect(result.isValid).toBe(false);
            expect(result.detectedLanguage).toBe('english');
            expect(result.suggestions).toContain(expect.stringMatching(/FRANÇAIS/i));
        });

        test('devrait rejeter un texte anglais technique', () => {
            const result = languageValidationService.validateFrenchResponse(TEST_TEXTS.english.technical);
            
            expect(result.isValid).toBe(false);
            expect(result.detectedLanguage).toBe('english');
        });
    });

    describe('Détection de textes mixtes', () => {
        test('devrait détecter un texte mixte français-anglais', () => {
            const result = languageValidationService.validateFrenchResponse(TEST_TEXTS.mixed.frenchEnglish);
            
            expect(result.isValid).toBe(false);
            expect(result.detectedLanguage).toBe('mixed');
            expect(result.issues).toContain(expect.stringMatching(/mixte/i));
        });

        test('devrait détecter un texte mixte anglais-français', () => {
            const result = languageValidationService.validateFrenchResponse(TEST_TEXTS.mixed.englishFrench);
            
            expect(result.isValid).toBe(false);
            expect(result.detectedLanguage).toBe('mixed');
            expect(result.suggestions).toContain(expect.stringMatching(/instructions/i));
        });
    });

    describe('Gestion des cas problématiques', () => {
        test('devrait gérer un texte vide', () => {
            const result = languageValidationService.validateFrenchResponse(TEST_TEXTS.problematic.empty);
            
            expect(result.isValid).toBe(false);
            expect(result.detectedLanguage).toBe('unknown');
            expect(result.confidence).toBe(0);
            expect(result.issues).toContain(expect.stringMatching(/vide/i));
        });

        test('devrait gérer un texte trop court', () => {
            const result = languageValidationService.validateFrenchResponse(TEST_TEXTS.problematic.tooShort);
            
            expect(result.confidence).toBeLessThan(30);
        });

        test('devrait gérer un texte avec seulement des chiffres', () => {
            const result = languageValidationService.validateFrenchResponse(TEST_TEXTS.problematic.numbers);
            
            expect(result.isValid).toBe(false);
            expect(result.detectedLanguage).toBe('unknown');
        });
    });

    describe('Statistiques et monitoring', () => {
        test('devrait mettre à jour les statistiques correctement', () => {
            // Tester plusieurs validations
            languageValidationService.validateFrenchResponse(TEST_TEXTS.french.simple, 'test-model-1');
            languageValidationService.validateFrenchResponse(TEST_TEXTS.english.simple, 'test-model-1');
            languageValidationService.validateFrenchResponse(TEST_TEXTS.mixed.frenchEnglish, 'test-model-2');
            
            const stats = languageValidationService.getStats();
            
            expect(stats.totalValidations).toBe(3);
            expect(stats.frenchResponses).toBe(1);
            expect(stats.englishResponses).toBe(1);
            expect(stats.mixedResponses).toBe(1);
            expect(stats.modelStats['test-model-1']).toBeDefined();
            expect(stats.modelStats['test-model-1'].total).toBe(2);
        });

        test('devrait identifier les modèles problématiques', () => {
            // Créer des données de test avec un modèle problématique
            for (let i = 0; i < 5; i++) {
                languageValidationService.validateFrenchResponse(TEST_TEXTS.english.simple, 'problematic-model');
            }
            languageValidationService.validateFrenchResponse(TEST_TEXTS.french.simple, 'problematic-model');
            
            // Créer des données pour un bon modèle
            for (let i = 0; i < 5; i++) {
                languageValidationService.validateFrenchResponse(TEST_TEXTS.french.simple, 'good-model');
            }
            
            const problematicModels = languageValidationService.getProblematicModels();
            
            expect(problematicModels).toHaveLength(1);
            expect(problematicModels[0].modelId).toBe('problematic-model');
            expect(problematicModels[0].frenchPercentage).toBeLessThan(80);
        });

        test('devrait générer un rapport de conformité', () => {
            // Ajouter quelques validations
            languageValidationService.validateFrenchResponse(TEST_TEXTS.french.simple, 'model-1');
            languageValidationService.validateFrenchResponse(TEST_TEXTS.french.complex, 'model-1');
            languageValidationService.validateFrenchResponse(TEST_TEXTS.english.simple, 'model-2');
            
            const report = languageValidationService.generateComplianceReport();
            
            expect(report).toContain('Rapport de Conformité Linguistique');
            expect(report).toContain('Total validations');
            expect(report).toContain('Réponses françaises');
            expect(report).toContain('%');
        });

        test('devrait calculer la confiance moyenne correctement', () => {
            const result1 = languageValidationService.validateFrenchResponse(TEST_TEXTS.french.simple);
            const result2 = languageValidationService.validateFrenchResponse(TEST_TEXTS.french.complex);
            
            const stats = languageValidationService.getStats();
            const expectedAverage = (result1.confidence + result2.confidence) / 2;
            
            expect(stats.averageConfidence).toBeCloseTo(expectedAverage, 1);
        });
    });

    describe('Suggestions et recommandations', () => {
        test('devrait fournir des suggestions pour les réponses anglaises', () => {
            const result = languageValidationService.validateFrenchResponse(TEST_TEXTS.english.simple);
            
            expect(result.suggestions).toContain(expect.stringMatching(/FRANÇAIS/i));
            expect(result.suggestions.length).toBeGreaterThan(0);
        });

        test('devrait fournir des suggestions pour les réponses mixtes', () => {
            const result = languageValidationService.validateFrenchResponse(TEST_TEXTS.mixed.frenchEnglish);
            
            expect(result.suggestions).toContain(expect.stringMatching(/instructions/i));
            expect(result.suggestions).toContain(expect.stringMatching(/retry/i));
        });

        test('devrait fournir des suggestions pour les textes inconnus', () => {
            const result = languageValidationService.validateFrenchResponse(TEST_TEXTS.problematic.empty);
            
            expect(result.suggestions).toContain(expect.stringMatching(/qualité/i));
            expect(result.suggestions.length).toBeGreaterThan(0);
        });
    });
});

// Tests d'intégration avec des scénarios réels
describe('Tests d\'intégration - Scénarios réels', () => {
    test('devrait valider une réponse typique de workflow', () => {
        const workflowResponse = `
        ## Analyse du Problème

        Votre défi concernant l'optimisation des processus métier présente plusieurs dimensions intéressantes :

        ### Points clés identifiés :
        1. **Efficacité opérationnelle** : Réduction des temps de traitement
        2. **Qualité des livrables** : Amélioration de la satisfaction client
        3. **Ressources humaines** : Optimisation de l'allocation des équipes

        ### Questions de clarification :
        - Quels sont les processus les plus critiques à optimiser ?
        - Avez-vous des contraintes budgétaires spécifiques ?
        - Quel est le délai souhaité pour la mise en œuvre ?

        Passons maintenant à l'étape suivante : **Diagnostic Initial**.
        `;

        const result = languageValidationService.validateFrenchResponse(workflowResponse);
        
        expect(result.isValid).toBe(true);
        expect(result.detectedLanguage).toBe('french');
        expect(result.confidence).toBeGreaterThan(80);
    });

    test('devrait rejeter une réponse partiellement en anglais', () => {
        const mixedResponse = `
        ## Problem Analysis

        Votre défi concernant l'optimisation présente plusieurs aspects :

        ### Key points identified:
        1. **Operational efficiency** : Réduction des temps
        2. **Quality deliverables** : Amélioration satisfaction

        Let's move to the next step: **Initial Diagnosis**.
        `;

        const result = languageValidationService.validateFrenchResponse(mixedResponse);
        
        expect(result.isValid).toBe(false);
        expect(['mixed', 'english']).toContain(result.detectedLanguage);
    });
});

// Export pour utilisation dans d'autres tests
export { TEST_TEXTS };
