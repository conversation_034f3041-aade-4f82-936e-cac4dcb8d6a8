import React, { useState, useEffect } from 'react';

interface BackupNotificationProps {
  className?: string;
}

/**
 * Composant de notification pour sensibiliser l'utilisateur à l'importance 
 * de la sauvegarde de ses données personnelles
 */
export const BackupNotification: React.FC<BackupNotificationProps> = ({ className = '' }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    // Vérifier si l'utilisateur a déjà été informé
    const hasBeenNotified = localStorage.getItem('backup_notification_dismissed');
    
    if (!hasBeenNotified) {
      // Afficher la notification après 10 secondes
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 10000);

      return () => clearTimeout(timer);
    } else {
      setIsDismissed(true);
    }
  }, []);

  const handleDismiss = (permanent: boolean = false) => {
    setIsVisible(false);
    
    if (permanent) {
      localStorage.setItem('backup_notification_dismissed', 'true');
      setIsDismissed(true);
    }
  };

  const handleRemindLater = () => {
    setIsVisible(false);
    // Rappeler dans 1 heure
    setTimeout(() => {
      if (!isDismissed) {
        setIsVisible(true);
      }
    }, 60 * 60 * 1000);
  };

  if (!isVisible || isDismissed) {
    return null;
  }

  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${className}`}>
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
        {/* Header avec icône */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <div>
              <h3 className="text-xl font-bold">🔒 Confidentialité Garantie</h3>
              <p className="text-blue-100 text-sm">Vos données restent sur votre PC</p>
            </div>
          </div>
        </div>

        {/* Contenu */}
        <div className="p-6 space-y-4">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">Sauvegarde Automatique Active</h4>
                <p className="text-sm text-gray-600">
                  Toutes vos conversations et préférences sont automatiquement sauvegardées 
                  <strong> uniquement sur votre ordinateur</strong>.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">Aucune Donnée sur nos Serveurs</h4>
                <p className="text-sm text-gray-600">
                  Nous ne stockons <strong>aucune donnée personnelle</strong> sur nos serveurs. 
                  Votre confidentialité est notre priorité absolue.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">Recommandation Importante</h4>
                <p className="text-sm text-gray-600">
                  Pensez à <strong>exporter régulièrement</strong> votre profil via le panneau 
                  "💾 Sauvegarde Profil" pour créer une copie de sécurité.
                </p>
              </div>
            </div>
          </div>

          {/* Zone d'action */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-3">
              <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h4 className="font-medium text-gray-900">Où trouver la sauvegarde ?</h4>
            </div>
            <p className="text-sm text-gray-600">
              Regardez dans la colonne de droite, vous trouverez le panneau 
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium ml-1">
                💾 Sauvegarde Profil
              </span>
            </p>
          </div>
        </div>

        {/* Actions */}
        <div className="bg-gray-50 px-6 py-4 flex space-x-3">
          <button
            onClick={() => handleDismiss(true)}
            className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            ✅ Compris !
          </button>
          <button
            onClick={handleRemindLater}
            className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
          >
            ⏰ Rappeler plus tard
          </button>
        </div>
      </div>
    </div>
  );
};

export default BackupNotification;
