/**
 * Configuration centralisée pour le système de mémoire silencieuse
 * 
 * ✅ OBJECTIF : Paramétrage fin de la solution anti-réinjection
 * ✅ CONTRÔLE : Activation/désactivation facile du système
 * ✅ OPTIMISATION : Réglages performance et tokens
 */

export interface SilentMemoryGlobalConfig {
  // ✅ Activation générale du système
  enabled: boolean;
  
  // ✅ Mode de fonctionnement
  mode: 'silent' | 'legacy' | 'hybrid';
  
  // ✅ Gestion des tokens
  tokenOptimization: {
    maxCasesPerQuery: number;
    maxContextLength: number;
    enableCompression: boolean;
  };
  
  // ✅ Logging et debug
  logging: {
    enableConsoleLog: boolean;
    enablePerformanceMetrics: boolean;
    logLevel: 'minimal' | 'normal' | 'verbose';
  };
  
  // ✅ Sécurité et confidentialité
  privacy: {
    forceInvisibleMode: boolean;
    encryptStoredData: boolean;
    autoCleanupAfterDays: number;
  };
  
  // ✅ Performance
  performance: {
    cacheEnabled: boolean;
    maxCacheSize: number;
    backgroundProcessing: boolean;
  };
}

/**
 * ✅ Configuration par défaut optimisée
 */
export const DEFAULT_SILENT_MEMORY_CONFIG: SilentMemoryGlobalConfig = {
  enabled: true,
  mode: 'silent', // ✅ Mode silencieux par défaut
  
  tokenOptimization: {
    maxCasesPerQuery: 2, // ✅ Limité pour économiser les tokens
    maxContextLength: 500, // ✅ Contexte concis
    enableCompression: true // ✅ Compression du contexte
  },
  
  logging: {
    enableConsoleLog: true, // ✅ Debug console (invisible utilisateur)
    enablePerformanceMetrics: true,
    logLevel: 'normal'
  },
  
  privacy: {
    forceInvisibleMode: true, // ✅ TOUJOURS invisible
    encryptStoredData: false, // ✅ À activer en production
    autoCleanupAfterDays: 30 // ✅ Nettoyage automatique
  },
  
  performance: {
    cacheEnabled: true,
    maxCacheSize: 100, // ✅ 100 interactions en cache
    backgroundProcessing: true
  }
};

/**
 * ✅ Configuration pour environnement de développement
 */
export const DEV_SILENT_MEMORY_CONFIG: SilentMemoryGlobalConfig = {
  ...DEFAULT_SILENT_MEMORY_CONFIG,
  logging: {
    enableConsoleLog: true,
    enablePerformanceMetrics: true,
    logLevel: 'verbose' // ✅ Logs détaillés pour debug
  },
  privacy: {
    ...DEFAULT_SILENT_MEMORY_CONFIG.privacy,
    encryptStoredData: false // ✅ Pas de chiffrement en dev
  }
};

/**
 * ✅ Configuration pour environnement de production
 */
export const PROD_SILENT_MEMORY_CONFIG: SilentMemoryGlobalConfig = {
  ...DEFAULT_SILENT_MEMORY_CONFIG,
  logging: {
    enableConsoleLog: false, // ✅ Pas de logs en production
    enablePerformanceMetrics: true,
    logLevel: 'minimal'
  },
  privacy: {
    ...DEFAULT_SILENT_MEMORY_CONFIG.privacy,
    encryptStoredData: true, // ✅ Chiffrement obligatoire
    autoCleanupAfterDays: 7 // ✅ Nettoyage plus fréquent
  },
  performance: {
    ...DEFAULT_SILENT_MEMORY_CONFIG.performance,
    maxCacheSize: 200 // ✅ Cache plus important en prod
  }
};

/**
 * ✅ Configuration pour tests
 */
export const TEST_SILENT_MEMORY_CONFIG: SilentMemoryGlobalConfig = {
  ...DEFAULT_SILENT_MEMORY_CONFIG,
  tokenOptimization: {
    maxCasesPerQuery: 1, // ✅ Minimal pour tests rapides
    maxContextLength: 200,
    enableCompression: false // ✅ Pas de compression pour tests
  },
  logging: {
    enableConsoleLog: true,
    enablePerformanceMetrics: true,
    logLevel: 'verbose'
  },
  performance: {
    cacheEnabled: false, // ✅ Pas de cache pour tests isolés
    maxCacheSize: 10,
    backgroundProcessing: false
  }
};

/**
 * ✅ Gestionnaire de configuration centralisé
 */
class SilentMemoryConfigManager {
  private currentConfig: SilentMemoryGlobalConfig;
  private environment: 'development' | 'production' | 'test';

  constructor() {
    this.environment = this.detectEnvironment();
    this.currentConfig = this.getConfigForEnvironment();
  }

  /**
   * ✅ Détection automatique de l'environnement
   */
  private detectEnvironment(): 'development' | 'production' | 'test' {
    if (typeof process !== 'undefined') {
      if (process.env.NODE_ENV === 'test') return 'test';
      if (process.env.NODE_ENV === 'production') return 'production';
    }
    return 'development';
  }

  /**
   * ✅ Configuration selon l'environnement
   */
  private getConfigForEnvironment(): SilentMemoryGlobalConfig {
    switch (this.environment) {
      case 'production':
        return PROD_SILENT_MEMORY_CONFIG;
      case 'test':
        return TEST_SILENT_MEMORY_CONFIG;
      default:
        return DEV_SILENT_MEMORY_CONFIG;
    }
  }

  /**
   * ✅ Obtenir la configuration actuelle
   */
  getConfig(): SilentMemoryGlobalConfig {
    return { ...this.currentConfig };
  }

  /**
   * ✅ Mettre à jour la configuration
   */
  updateConfig(updates: Partial<SilentMemoryGlobalConfig>): void {
    this.currentConfig = { ...this.currentConfig, ...updates };
    
    if (this.currentConfig.logging.enableConsoleLog) {
      console.log('⚙️ [SILENT CONFIG] Configuration mise à jour:', updates);
    }
  }

  /**
   * ✅ Réinitialiser la configuration
   */
  resetConfig(): void {
    this.currentConfig = this.getConfigForEnvironment();
    
    if (this.currentConfig.logging.enableConsoleLog) {
      console.log('🔄 [SILENT CONFIG] Configuration réinitialisée pour:', this.environment);
    }
  }

  /**
   * ✅ Vérifier si le système est activé
   */
  isEnabled(): boolean {
    return this.currentConfig.enabled;
  }

  /**
   * ✅ Obtenir le mode de fonctionnement
   */
  getMode(): 'silent' | 'legacy' | 'hybrid' {
    return this.currentConfig.mode;
  }

  /**
   * ✅ Vérifier si le mode invisible est forcé
   */
  isForceInvisible(): boolean {
    return this.currentConfig.privacy.forceInvisibleMode;
  }

  /**
   * ✅ Obtenir les limites de tokens
   */
  getTokenLimits(): { maxCases: number; maxLength: number } {
    return {
      maxCases: this.currentConfig.tokenOptimization.maxCasesPerQuery,
      maxLength: this.currentConfig.tokenOptimization.maxContextLength
    };
  }

  /**
   * ✅ Vérifier si les logs sont activés
   */
  isLoggingEnabled(): boolean {
    return this.currentConfig.logging.enableConsoleLog;
  }

  /**
   * ✅ Obtenir le niveau de log
   */
  getLogLevel(): 'minimal' | 'normal' | 'verbose' {
    return this.currentConfig.logging.logLevel;
  }
}

// ✅ Instance singleton
export const silentMemoryConfigManager = new SilentMemoryConfigManager();

// ✅ Export des configurations pour usage direct
export {
  DEFAULT_SILENT_MEMORY_CONFIG,
  DEV_SILENT_MEMORY_CONFIG,
  PROD_SILENT_MEMORY_CONFIG,
  TEST_SILENT_MEMORY_CONFIG
};
