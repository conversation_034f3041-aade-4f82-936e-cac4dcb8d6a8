import React, { useEffect, useImperativeHandle, forwardRef } from 'react';
import RoonyMascot from './RoonyMascot';
import { useRoonyAnimations, type RoonyContextType } from '../hooks/useRoonyAnimations';
import type { RoonyAnimationType } from './RoonyMascot';

interface RoonyContextualMascotProps {
  /** Position du composant */
  position?: 'fixed' | 'absolute' | 'relative';
  /** Classes CSS additionnelles */
  className?: string;
  /** Taille de Roony */
  size?: number;
  /** Position personnalisée (pour position fixed/absolute) */
  customPosition?: {
    top?: string | number;
    bottom?: string | number;
    left?: string | number;
    right?: string | number;
  };
  /** Déclencher automatiquement une animation au montage */
  initialContext?: RoonyContextType;
  /** Callback quand une animation se termine */
  onAnimationComplete?: (animation: RoonyAnimationType) => void;
}

export interface RoonyContextualMascotRef {
  /** Déclencher une animation contextuelle */
  triggerContext: (context: RoonyContextType, stepIndex?: number) => void;
  /** Déclencher une animation spécifique */
  triggerAnimation: (animation: RoonyAnimationType, duration?: number) => void;
  /** Masquer Roony */
  hide: () => void;
}

/**
 * Composant Roony contextuel qui peut être placé n'importe où dans l'interface
 * et déclencher automatiquement les bonnes animations selon le contexte
 */
export const RoonyContextualMascot = forwardRef<RoonyContextualMascotRef, RoonyContextualMascotProps>(({
  position = 'fixed',
  className = '',
  size = 120,
  customPosition = { bottom: 20, right: 20 },
  initialContext,
  onAnimationComplete
}, ref) => {
  const { animationState, triggerAnimation, hideRoony, triggerContextualAnimation } = useRoonyAnimations();

  // Exposer les méthodes via la ref
  useImperativeHandle(ref, () => ({
    triggerContext: (context: RoonyContextType, stepIndex?: number) => triggerContextualAnimation(context, stepIndex),
    triggerAnimation,
    hide: hideRoony
  }), [triggerContextualAnimation, triggerAnimation, hideRoony]);

  // Déclencher l'animation initiale si spécifiée
  useEffect(() => {
    if (initialContext) {
      const timer = setTimeout(() => {
        triggerContextualAnimation(initialContext);
      }, 500); // Petit délai pour laisser le composant se monter

      return () => clearTimeout(timer);
    }
  }, [initialContext, triggerContextualAnimation]);

  // Callback quand l'animation se termine
  const handleAnimationComplete = () => {
    onAnimationComplete?.(animationState.currentAnimation);
  };

  // Ne pas rendre si Roony n'est pas visible
  if (!animationState.isVisible || animationState.currentAnimation === 'idle') {
    return null;
  }

  // Construire le style de position
  const positionStyle: React.CSSProperties = {
    position,
    zIndex: 1000,
    ...customPosition
  };

  // Si une position spécifique est définie dans l'état d'animation, l'utiliser
  if (animationState.position) {
    positionStyle.left = animationState.position.x;
    positionStyle.top = animationState.position.y;
  }

  return (
    <div
      className={`roony-contextual-mascot ${className}`}
      style={positionStyle}
    >
      <RoonyMascot
        animation={animationState.currentAnimation}
        size={size}
        position="relative"
        onAnimationComplete={handleAnimationComplete}
        entranceAnimation={getEntranceAnimation(animationState.currentAnimation)}
        exitAnimation={getExitAnimation(animationState.currentAnimation)}
      />
    </div>
  );
});

// Fonction pour déterminer l'animation d'entrée selon le type d'animation
function getEntranceAnimation(animation: RoonyAnimationType): 'fadeIn' | 'slideInLeft' | 'slideInRight' | 'bounceIn' | 'none' {
  // Supprimer toutes les animations d'entrée en slide-in - seulement fadeIn simple
  return 'fadeIn';
}

// Fonction pour déterminer l'animation de sortie selon le type d'animation
function getExitAnimation(animation: RoonyAnimationType): 'fadeOut' | 'slideOutLeft' | 'slideOutRight' | 'bounceOut' | 'none' {
  // Supprimer toutes les animations de sortie en slide-out - seulement fadeOut simple
  return 'fadeOut';
}

RoonyContextualMascot.displayName = 'RoonyContextualMascot';

export default RoonyContextualMascot;
