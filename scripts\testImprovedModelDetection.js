/**
 * Script de test pour valider le système amélioré de détection des modèles
 * Août 2025 - Avec validation par rapport aux URLs de référence OpenRouter
 */

// Simulation de l'API call pour tester (en production, utiliser le vrai service)
async function testImprovedModelDetection() {
    console.log('🧪 Test du système amélioré de détection des modèles');
    console.log('='.repeat(80));

    try {
        // Test 1: Récupération via l'API standard
        console.log('\n1. 📡 Récupération des modèles via l\'API OpenRouter');
        const response = await fetch('https://openrouter.ai/api/v1/models');
        
        if (!response.ok) {
            throw new Error(`Erreur API: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        const allModels = data.data || [];
        
        console.log(`   ✅ ${allModels.length} modèles récupérés depuis l'API`);

        // Test 2: Classification avancée
        console.log('\n2. 🔍 Classification avancée des modèles');
        
        let freeCount = 0;
        let premiumCount = 0;
        let otherCount = 0;

        const freeModels = [];
        const premiumModels = [];

        allModels.forEach(model => {
            const promptPrice = parseFloat(model.pricing.prompt || '0');
            const completionPrice = parseFloat(model.pricing.completion || '0');

            // Classification gratuite améliorée
            const isFree = model.id.includes(':free') || 
                          (promptPrice === 0 && completionPrice === 0);

            // Classification premium améliorée (max 10$ par million de tokens)
            const maxPricePerMillion = 10 / 1000000;
            const isPremium = !isFree && 
                             promptPrice > 0 && 
                             promptPrice <= maxPricePerMillion && 
                             completionPrice <= maxPricePerMillion;

            if (isFree) {
                freeCount++;
                freeModels.push(model);
            } else if (isPremium) {
                premiumCount++;
                premiumModels.push(model);
            } else {
                otherCount++;
            }
        });

        console.log(`   💝 Modèles gratuits: ${freeCount}`);
        console.log(`   🌟 Modèles premium: ${premiumCount}`);
        console.log(`   ❓ Autres modèles: ${otherCount}`);

        // Test 3: Validation par rapport aux chiffres de référence
        console.log('\n3. 📊 Validation par rapport aux chiffres de référence (Août 2025)');
        
        const expectedFree = 58;  // Référence: https://openrouter.ai/models?max_price=0
        const expectedPremium = 310; // Référence: https://openrouter.ai/models?max_price=10

        const freeAccuracy = freeCount / expectedFree;
        const premiumAccuracy = premiumCount / expectedPremium;

        console.log(`   🎯 Gratuits: ${freeCount}/${expectedFree} (${Math.round(freeAccuracy * 100)}% de précision)`);
        console.log(`   🎯 Premium: ${premiumCount}/${expectedPremium} (${Math.round(premiumAccuracy * 100)}% de précision)`);

        // Test 4: Analyse des modèles gratuits remarquables
        console.log('\n4. 🌟 Top 10 modèles gratuits les plus récents');
        
        const recentFreeModels = freeModels
            .sort((a, b) => b.created - a.created)
            .slice(0, 10);

        recentFreeModels.forEach((model, index) => {
            console.log(`   ${index + 1}. ${model.name}`);
            console.log(`      ID: ${model.id}`);
            console.log(`      Créé: ${new Date(model.created * 1000).toLocaleDateString('fr-FR')}`);
            console.log(`      Contexte: ${model.context_length?.toLocaleString() || 'N/A'} tokens`);
        });

        // Test 5: Analyse des capacités spéciales
        console.log('\n5. 🔬 Analyse des capacités spéciales');
        
        const reasoningModels = freeModels.filter(m => 
            m.id.toLowerCase().includes('deepseek-r1') || 
            m.id.toLowerCase().includes('qwq') || 
            m.id.toLowerCase().includes('thinking')
        );

        const codingModels = freeModels.filter(m => 
            m.id.toLowerCase().includes('coder') || 
            m.id.toLowerCase().includes('code') || 
            m.id.toLowerCase().includes('devstral')
        );

        const multimodalModels = freeModels.filter(m => 
            m.id.toLowerCase().includes('vision') || 
            m.id.toLowerCase().includes('vl') ||
            m.architecture?.input_modalities?.includes('image')
        );

        console.log(`   🧠 Modèles de raisonnement: ${reasoningModels.length}`);
        console.log(`   💻 Modèles de codage: ${codingModels.length}`);
        console.log(`   👁️ Modèles multimodaux: ${multimodalModels.length}`);

        // Test 6: Recommandations d'amélioration
        console.log('\n6. 💡 Recommandations pour améliorer la précision');
        
        const recommendations = [];

        if (freeAccuracy < 0.8) {
            recommendations.push(`🔍 Ajuster les critères de détection des modèles gratuits`);
        }
        if (freeAccuracy > 1.2) {
            recommendations.push(`⚠️ Critères trop permissifs pour les modèles gratuits`);
        }
        if (premiumAccuracy < 0.8) {
            recommendations.push(`🔍 Réviser la gamme de prix pour les modèles premium`);
        }
        if (premiumAccuracy > 1.2) {
            recommendations.push(`⚠️ Gamme de prix trop large pour les modèles premium`);
        }

        if (recommendations.length === 0) {
            recommendations.push(`✅ Classifications conformes aux attentes`);
        }

        recommendations.forEach(rec => console.log(`   ${rec}`));

        // Test 7: Génération de la configuration mise à jour
        console.log('\n7. ⚙️ Génération de la configuration par tâche');
        
        const taskClassification = {
            analyse: [],
            génération: [],
            validation: [],
            synthèse: []
        };

        freeModels.forEach(model => {
            const modelIdLower = model.id.toLowerCase();
            
            // Logique de classification par tâche
            if (modelIdLower.includes('deepseek-r1') || modelIdLower.includes('qwq')) {
                taskClassification.analyse.push(model.id);
                taskClassification.validation.push(model.id);
            }
            
            if (modelIdLower.includes('coder') || modelIdLower.includes('code')) {
                taskClassification.génération.push(model.id);
            }
            
            if (modelIdLower.includes('large') || modelIdLower.includes('405b')) {
                taskClassification.synthèse.push(model.id);
            }
            
            // Si pas de spécialité, ajouter à analyse par défaut
            const hasSpecialty = Object.values(taskClassification).some(arr => arr.includes(model.id));
            if (!hasSpecialty) {
                taskClassification.analyse.push(model.id);
            }
        });

        Object.entries(taskClassification).forEach(([task, models]) => {
            console.log(`   ${task}: ${models.length} modèles`);
        });

        console.log('\n✅ Tests terminés avec succès !');
        console.log('='.repeat(80));

        return {
            success: true,
            totalModels: allModels.length,
            freeModels: freeCount,
            premiumModels: premiumCount,
            accuracy: {
                free: freeAccuracy,
                premium: premiumAccuracy
            },
            taskClassification,
            recommendations
        };

    } catch (error) {
        console.error('❌ Erreur lors des tests:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Test avec données mockées pour le développement
async function testWithMockData() {
    console.log('🧪 Test avec données simulées...');
    
    const mockModels = [
        {
            id: "deepseek/deepseek-r1:free",
            name: "DeepSeek R1 Free",
            pricing: { prompt: "0", completion: "0" },
            created: Date.now() / 1000,
            context_length: 32768,
            description: "Advanced reasoning model for complex analysis"
        },
        {
            id: "qwen/qwen3-coder:free", 
            name: "Qwen 3 Coder Free",
            pricing: { prompt: "0", completion: "0" },
            created: Date.now() / 1000,
            context_length: 16384,
            description: "Specialized coding model for generation tasks"
        },
        {
            id: "openai/gpt-4o-mini",
            name: "GPT-4o Mini",
            pricing: { prompt: "0.000000150", completion: "0.000000600" },
            created: Date.now() / 1000,
            context_length: 128000,
            description: "Efficient premium model for all tasks"
        }
    ];
    
    console.log(`📦 ${mockModels.length} modèles de test`);
    
    // Classification de test
    mockModels.forEach(model => {
        const promptPrice = parseFloat(model.pricing.prompt);
        const isFree = model.id.includes(':free') || promptPrice === 0;
        const isPremium = !isFree && promptPrice > 0 && promptPrice <= 0.00001;
        
        console.log(`   ${model.name}: ${isFree ? '💝 Gratuit' : isPremium ? '🌟 Premium' : '❓ Autre'}`);
    });
}

// Export pour utilisation dans d'autres scripts
if (typeof window !== 'undefined') {
    window.testImprovedModelDetection = testImprovedModelDetection;
    window.testWithMockData = testWithMockData;
    console.log('🧪 Tests disponibles:');
    console.log('   window.testImprovedModelDetection() - Test complet');
    console.log('   window.testWithMockData() - Test avec données simulées');
} else {
    // Exécution en Node.js
    console.log('🚀 Démarrage du test de détection de modèles...');
    testImprovedModelDetection().then(result => {
        console.log('\n📋 Résumé final:');
        if (result.success) {
            console.log(`   Total: ${result.totalModels} modèles`);
            console.log(`   Gratuits: ${result.freeModels} (précision: ${Math.round(result.accuracy.free * 100)}%)`);
            console.log(`   Premium: ${result.premiumModels} (précision: ${Math.round(result.accuracy.premium * 100)}%)`);
        } else {
            console.log(`   Erreur: ${result.error}`);
        }
    }).catch(error => {
        console.error('❌ Erreur d\'exécution:', error);
    });
}

export { testImprovedModelDetection, testWithMockData };
