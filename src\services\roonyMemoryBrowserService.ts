/**
 * Service de mémoire persistante Roony - Version Navigateur
 * Compatible avec l'environnement navigateur via API HTTP
 */

export interface MemoryCase {
  contexte: string;
  analyse: string;
  resultats: string;
  timestamp: string;
  satisfaction: number;
}

export interface MemoryContext {
  cas_similaires: Array<{
    contexte: string;
    analyse: string;
    score_similarite: number;
  }>;
  apprentissages: string[];
  patterns_recurrents: string[];
  recommandations: string[];
}

class RoonyMemoryBrowserService {
  private baseUrl: string;

  constructor() {
    // URL du serveur de mémoire (pourrait être un serveur local Express)
    this.baseUrl = 'http://localhost:8888/api/memory';
  }

  /**
   * Génère le contexte mémoire pour une requête donnée
   */
  async genererContexteMemoire(requete: string): Promise<MemoryContext | null> {
    try {
      console.log('🧠 [Mémoire] Recherche de contexte pour:', requete.substring(0, 100) + '...');
      
      const response = await fetch(`${this.baseUrl}/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: requete }),
      });

      if (!response.ok) {
        console.warn('🧠 [Mémoire] Service indisponible, mode sans mémoire activé');
        return null;
      }

      const context = await response.json();
      console.log('🧠 [Mémoire] Contexte généré avec', context.cas_similaires?.length || 0, 'cas similaires');
      
      return context;
    } catch (error) {
      console.warn('🧠 [Mémoire] Erreur lors de la génération du contexte:', error);
      return null;
    }
  }

  /**
   * Enrichit un prompt avec le contexte mémoire (version intelligente)
   */
  async enrichirPromptAvecMemoire(
    prompt: string,
    contexte: string,
    conversationStage?: string
  ): Promise<string> {
    try {
      // Éviter la réinjection selon le stage de conversation
      if (this.shouldSkipMemoryInjection(conversationStage, contexte)) {
        console.log('🚫 [Mémoire] Injection évitée pour continuité conversation');
        return prompt;
      }

      const memoryContext = await this.genererContexteMemoire(contexte);

      if (!memoryContext || !memoryContext.cas_similaires.length) {
        return prompt;
      }

      // Enrichissement discret - pas de répétition massive
      const enrichedPrompt = `${prompt}

🧠 CONTEXTE MÉMOIRE DISCRET :
${this.formatMemoryContextDiscret(memoryContext)}`;

      return enrichedPrompt;
    } catch (error) {
      console.warn('🧠 [Mémoire] Erreur lors de l\'enrichissement du prompt:', error);
      return prompt;
    }
  }

  /**
   * Sauvegarde un nouveau cas d'usage
   */
  async sauvegarderNouveauCas(cas: MemoryCase): Promise<boolean> {
    try {
      console.log('🧠 [Mémoire] Sauvegarde d\'un nouveau cas...');
      
      const response = await fetch(`${this.baseUrl}/save`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cas),
      });

      const success = response.ok;
      console.log('🧠 [Mémoire] Sauvegarde', success ? 'réussie' : 'échouée');
      
      return success;
    } catch (error) {
      console.warn('🧠 [Mémoire] Erreur lors de la sauvegarde:', error);
      return false;
    }
  }

  /**
   * Détermine si l'injection mémoire doit être évitée
   */
  private shouldSkipMemoryInjection(conversationStage?: string, contexte?: string): boolean {
    // Détecter les mots-clés de continuation AVANT de vérifier le stage
    if (contexte) {
      const motsContinuation = [
        'continuer', 'continue', 'suite', 'suivant', 'étape', 'après',
        'oui', 'ok', 'd\'accord', 'parfait', 'exact', 'correct'
      ];

      const contexteLower = contexte.toLowerCase();
      const estContinuation = motsContinuation.some(mot => contexteLower.includes(mot));

      if (estContinuation) {
        return true;
      }
    }

    // Si pas de stage défini, on n'évite pas (sécurité)
    if (!conversationStage) {
      return false;
    }

    // Stages où on ÉVITE la réinjection (conversation en cours)
    const stagesAvecContexteExistant = [
      'personnage', 'role', 'objectif', 'format', 'synthesis', 'ready'
    ];

    if (stagesAvecContexteExistant.includes(conversationStage)) {
      return true;
    }

    // Par défaut, on n'évite pas
    return false;
  }

  /**
   * Formate le contexte mémoire de manière discrète
   */
  private formatMemoryContextDiscret(context: MemoryContext): string {
    if (!context.cas_similaires || context.cas_similaires.length === 0) {
      return '';
    }

    // Format discret - juste les points clés
    const casFormates = context.cas_similaires.slice(0, 2).map(cas =>
      `• ${cas.contexte.substring(0, 80)}...`
    ).join('\n');

    return `Expérience pertinente :
${casFormates}

(Utilise subtilement, sans répéter le contexte)`;
  }

  /**
   * Formate le contexte mémoire pour l'affichage
   */
  private formatMemoryContext(context: MemoryContext): string {
    let formatted = '';

    if (context.cas_similaires.length > 0) {
      formatted += '\n📚 CAS SIMILAIRES IDENTIFIÉS :\n';
      context.cas_similaires.forEach((cas, index) => {
        formatted += `${index + 1}. [Similarité: ${Math.round(cas.score_similarite * 100)}%]\n`;
        formatted += `   Contexte: ${cas.contexte.substring(0, 150)}...\n`;
        formatted += `   Analyse: ${cas.analyse.substring(0, 150)}...\n\n`;
      });
    }

    if (context.apprentissages.length > 0) {
      formatted += '\n💡 APPRENTISSAGES CLÉS :\n';
      context.apprentissages.forEach((apprentissage, index) => {
        formatted += `${index + 1}. ${apprentissage}\n`;
      });
    }

    if (context.patterns_recurrents.length > 0) {
      formatted += '\n🔄 PATTERNS RÉCURRENTS :\n';
      context.patterns_recurrents.forEach((pattern, index) => {
        formatted += `${index + 1}. ${pattern}\n`;
      });
    }

    if (context.recommandations.length > 0) {
      formatted += '\n🎯 RECOMMANDATIONS :\n';
      context.recommandations.forEach((rec, index) => {
        formatted += `${index + 1}. ${rec}\n`;
      });
    }

    return formatted;
  }

  /**
   * Vérifie si le service mémoire est disponible
   */
  async isServiceAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/health`, {
        method: 'GET',
        timeout: 2000
      } as RequestInit);
      return response.ok;
    } catch {
      return false;
    }
  }
}

// Instance singleton
export const roonyMemoryBrowserService = new RoonyMemoryBrowserService();
