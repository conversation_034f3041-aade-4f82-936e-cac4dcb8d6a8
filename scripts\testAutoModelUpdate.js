/**
 * Script de test pour vérifier le système de détection automatique des modèles
 * Août 2025 - Studio Agentique Roony
 */

import { autoModelUpdateService } from '../services/autoModelUpdateService.js';

async function testAutoModelUpdate() {
    console.log('🧪 Début des tests du système de détection automatique des modèles');
    console.log('='.repeat(70));

    try {
        // Test 1: Vérifier le statut actuel
        console.log('\n1. 📊 Statut actuel du système');
        const status = autoModelUpdateService.getUpdateStatus();
        console.log(`   ✅ Dernière mise à jour: ${status.lastUpdate.toLocaleString('fr-FR')}`);
        console.log(`   ⏰ Prochaine mise à jour: ${status.nextUpdate.toLocaleString('fr-FR')}`);
        console.log(`   📅 Jours restants: ${status.daysUntilNextUpdate}`);
        console.log(`   🤖 Modèles en cache: ${status.modelsCount}`);

        // Test 2: Mise à jour forcée
        console.log('\n2. 🔄 Test de mise à jour forcée');
        const report = await autoModelUpdateService.performUpdate(true);
        
        console.log(`   ✅ Mise à jour terminée`);
        console.log(`   📈 Total modèles: ${report.totalModels}`);
        console.log(`   💝 Modèles gratuits: ${report.freeModels}`);
        console.log(`   🌟 Modèles premium: ${report.premiumModels}`);
        console.log(`   🆕 Nouveaux gratuits: ${report.newFreeModels.length}`);
        console.log(`   🆕 Nouveaux premium: ${report.newPremiumModels.length}`);
        
        if (report.removedModels.length > 0) {
            console.log(`   🗑️ Modèles supprimés: ${report.removedModels.length}`);
        }

        // Test 3: Vérifier la classification par tâche
        console.log('\n3. 📋 Classification par tâche');
        const tasks = ['analyse', 'génération', 'validation', 'synthèse'];
        
        for (const task of tasks) {
            const freeModels = autoModelUpdateService.getFreeModelsForTask(task);
            const premiumModels = autoModelUpdateService.getPremiumModelsForTask(task);
            
            console.log(`   ${task.padEnd(12)}: ${freeModels.length} gratuits, ${premiumModels.length} premium`);
            
            // Afficher quelques exemples
            if (freeModels.length > 0) {
                console.log(`      💝 Exemples gratuits: ${freeModels.slice(0, 3).map(m => m.name.substring(0, 25)).join(', ')}...`);
            }
            if (premiumModels.length > 0) {
                console.log(`      🌟 Exemples premium: ${premiumModels.slice(0, 2).map(m => m.name.substring(0, 25)).join(', ')}...`);
            }
        }

        // Test 4: Génération de configuration mise à jour
        console.log('\n4. ⚙️ Génération de configuration mise à jour');
        const updatedConfig = autoModelUpdateService.generateUpdatedConstants();
        
        console.log('   ✅ Configuration générée:');
        Object.entries(updatedConfig.freeModels).forEach(([task, models]) => {
            console.log(`      ${task}: ${models.length} modèles gratuits`);
        });
        Object.entries(updatedConfig.premiumModels).forEach(([task, models]) => {
            console.log(`      ${task}: ${models.length} modèles premium`);
        });

        // Test 5: Nouveaux modèles remarquables
        console.log('\n5. 🌟 Nouveaux modèles remarquables');
        
        if (report.newFreeModels.length > 0) {
            console.log('   💝 Nouveaux modèles gratuits:');
            report.newFreeModels.slice(0, 5).forEach(model => {
                console.log(`      • ${model.name}`);
                console.log(`        ID: ${model.id}`);
                console.log(`        Contexte: ${model.context_length.toLocaleString()} tokens`);
                console.log(`        Créé: ${new Date(model.created * 1000).toLocaleDateString('fr-FR')}`);
            });
        }

        if (report.newPremiumModels.length > 0) {
            console.log('   🌟 Nouveaux modèles premium:');
            report.newPremiumModels.slice(0, 3).forEach(model => {
                const promptPrice = parseFloat(model.pricing.prompt) * 1000000;
                const completionPrice = parseFloat(model.pricing.completion) * 1000000;
                
                console.log(`      • ${model.name}`);
                console.log(`        ID: ${model.id}`);
                console.log(`        Prix: ${promptPrice.toFixed(3)}$ / ${completionPrice.toFixed(3)}$ par million de tokens`);
                console.log(`        Contexte: ${model.context_length.toLocaleString()} tokens`);
            });
        }

        console.log('\n✅ Tests terminés avec succès !');
        console.log('='.repeat(70));

        return {
            success: true,
            report,
            updatedConfig
        };

    } catch (error) {
        console.error('❌ Erreur lors des tests:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Fonction utilitaire pour comparer avec les modèles actuels
function compareWithCurrentModels(updatedConfig) {
    console.log('\n🔍 Comparaison avec la configuration actuelle');
    
    // Cette fonction pourrait être étendue pour comparer
    // avec les constantes actuelles et identifier les changements
    console.log('   ℹ️ Comparaison détaillée à implémenter...');
}

// Exécution si appelé directement
if (typeof window !== 'undefined') {
    // Dans le navigateur, attacher à window pour les tests manuels
    window.testAutoModelUpdate = testAutoModelUpdate;
    console.log('🧪 Fonction de test disponible: window.testAutoModelUpdate()');
} else {
    // En Node.js, exécuter directement
    testAutoModelUpdate().then(result => {
        if (result.success) {
            console.log('\n📊 Résumé final:');
            console.log(`   Total: ${result.report.totalModels} modèles`);
            console.log(`   Gratuits: ${result.report.freeModels}`);
            console.log(`   Premium: ${result.report.premiumModels}`);
            console.log(`   Nouveaux: ${result.report.newFreeModels.length + result.report.newPremiumModels.length}`);
        }
    });
}

export { testAutoModelUpdate };
