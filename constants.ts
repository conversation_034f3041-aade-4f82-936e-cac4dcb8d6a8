import type { Step } from './types';

export const WORKFLOW_STEPS: Step[] = [
  {
    id: 1,
    title: "Définition du Problème",
    description: "Clarifier le problème, ses contraintes et ses objectifs.",
    techniques: ["CSP", "ToT", "Role-Playing"],
    task: "analyse",
  },
  {
    id: 2,
    title: "Diagnostic Initial",
    description: "Identifier les causes profondes et les facteurs influents.",
    techniques: ["Analyse Pareto", "GoT", "5 Pourquoi"],
    task: "analyse",
  },
  {
    id: 3,
    title: "Exploration Arborescente",
    description: "Générer et évaluer plusieurs scénarios et pistes de solution.",
    techniques: ["Tree-of-Thoughts", "Monte Carlo Search", "Scaffolding"],
    task: "analyse",
  },
  {
    id: 4,
    title: "Génération de Solutions",
    description: "Proposer des solutions innovantes basées sur les scénarios.",
    techniques: ["Algorithmes Génétiques", "Adversarial Prompting", "Few-Shot"],
    task: "génération",
  },
  {
    id: 5,
    title: "Validation Éthique et Sociale",
    description: "Évaluer l'impact des solutions au regard des ODD et critères éthiques.",
    techniques: ["Ethical Prompting", "Counterfactual", "Bias Detection"],
    task: "validation",
  },
  {
    id: 6,
    title: "Simulation Prédictive",
    description: "Tester la robustesse des solutions face à des scénarios extrêmes.",
    techniques: ["Monte Carlo", "Digital Twin", "TDA"],
    task: "validation",
  },
  {
    id: 7,
    title: "Prototypage Adaptatif",
    description: "Créer un prototype minimal viable (MVP) de la solution.",
    techniques: ["ReAct", "Greedy Search", "Hill Climbing"],
    task: "génération",
  },
  {
    id: 8,
    title: "Déploiement Contextuel",
    description: "Adapter la solution aux spécificités géographiques et culturelles.",
    techniques: ["Voronoi Tessellation", "Dynamic Prompting", "KNN"],
    task: "analyse",
  },
  {
    id: 9,
    title: "Suivi Dynamique",
    description: "Mettre en place un système de monitorage en temps réel.",
    techniques: ["IoT Integration", "Process Mining", "SPC"],
    task: "analyse",
  },
  {
    id: 10,
    title: "Boucle d'Amélioration Continue",
    description: "Ajuster progressivement la solution en fonction des retours.",
    techniques: ["Kaizen", "Feedback-Driven Prompting", "PDCA"],
    task: "synthèse",
  },
  {
    id: 11,
    title: "Capitalisation Cognitive",
    description: "Archiver les apprentissages clés dans une base de connaissances.",
    techniques: ["Chain-of-Knowledge (CoK)", "Knowledge-Enriched"],
    task: "synthèse",
  },
  {
    id: 12,
    title: "Validation Transversale",
    description: "Croiser les résultats avec différentes approches pour garantir la robustesse.",
    techniques: ["Self-Consistency", "Ensemble Prompting", "Cross-Modal"],
    task: "validation",
  },
    {
    id: 13,
    title: "Communication Stratégique",
    description: "Présenter les résultats de manière claire et adaptée aux parties prenantes.",
    techniques: ["Role-Playing Prompting", "Contrastive Prompting"],
    task: "synthèse",
  },
  {
    id: 14,
    title: "Synthèse et Plan d'Action",
    description: "Compiler toutes les informations en un plan d'action.",
    techniques: ["Action Plan", "Summary"],
    task: "synthèse",
  },
  {
    id: 15,
    title: "Génération du Prompt Final",
    description: "Créer le prompt final optimisé et sa méta-analyse.",
    techniques: ["Final Prompt Generation", "Meta-Analysis"],
    task: "génération",
  }
];

// Modèles OpenRouter gratuits 2025 - Mise à jour automatique (55 modèles)
// Optimisé pour le raisonnement agentique avec rotation intelligente
export const OPENROUTER_MODELS = {
  "analyse": [
    // Modèles de raisonnement avancé - 28 modèles
    "openai/gpt-oss-20b:free",
    "z-ai/glm-4.5-air:free",
    "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
    "google/gemma-3n-e2b-it:free",
    "tngtech/deepseek-r1t2-chimera:free",
    "moonshotai/kimi-dev-72b:free",
    "deepseek/deepseek-r1-0528-qwen3-8b:free",
    "deepseek/deepseek-r1-0528:free",
    "qwen/qwen3-4b:free",
    "qwen/qwen3-14b:free",
    "qwen/qwen3-235b-a22b:free",
    "tngtech/deepseek-r1t-chimera:free",
    "arliai/qwq-32b-arliai-rpr-v1:free",
    "moonshotai/kimi-vl-a3b-thinking:free",
    "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",
    "featherless/qwerky-72b:free",
    "mistralai/mistral-small-3.1-24b-instruct:free",
    "google/gemma-3-4b-it:free",
    "google/gemma-3-12b-it:free",
    "google/gemma-3-27b-it:free",
    "qwen/qwq-32b:free",
    "nousresearch/deephermes-3-llama-3-8b-preview:free",
    "mistralai/mistral-small-24b-instruct-2501:free",
    "deepseek/deepseek-r1-distill-qwen-14b:free",
    "deepseek/deepseek-r1-distill-llama-70b:free",
    "deepseek/deepseek-r1:free",
    "meta-llama/llama-3.2-11b-vision-instruct:free",
    "meta-llama/llama-3.2-3b-instruct:free"
  ],
  "génération": [
    // Modèles créatifs et de génération - 20 modèles
    "qwen/qwen3-coder:free",
    "moonshotai/kimi-k2:free",
    "tencent/hunyuan-a13b-instruct:free",
    "mistralai/mistral-small-3.2-24b-instruct:free",
    "sarvamai/sarvam-m:free",
    "mistralai/devstral-small-2505:free",
    "qwen/qwen3-30b-a3b:free",
    "qwen/qwen3-8b:free",
    "microsoft/mai-ds-r1:free",
    "agentica-org/deepcoder-14b-preview:free",
    "google/gemini-2.5-pro-exp-03-25",
    "qwen/qwen2.5-vl-32b-instruct:free",
    "rekaai/reka-flash-3:free",
    "cognitivecomputations/dolphin3.0-r1-mistral-24b:free",
    "cognitivecomputations/dolphin3.0-mistral-24b:free",
    "google/gemini-2.0-flash-exp:free",
    "qwen/qwen-2.5-coder-32b-instruct:free",
    "qwen/qwen-2.5-72b-instruct:free",
    "mistralai/mistral-nemo:free",
    "google/gemma-2-9b-it:free"
  ],
  "validation": [
    // Modèles de validation et vérification - 2 modèles
    "google/gemma-3n-e4b-it:free",
    "qwen/qwen2.5-vl-72b-instruct:free"
  ],
  "synthèse": [
    // Modèles de synthèse et finalisation - 5 modèles
    "shisa-ai/shisa-v2-llama3.3-70b:free",
    "deepseek/deepseek-chat-v3-0324:free",
    "meta-llama/llama-3.3-70b-instruct:free",
    "meta-llama/llama-3.1-405b-instruct:free",
    "mistralai/mistral-7b-instruct:free"
  ]
};

// URLs pour les API OpenRouter
export const OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions";
export const OPENROUTER_MODELS_API = "https://openrouter.ai/api/v1/models";

// Configuration pour la détection automatique des modèles (AMÉLIORÉE - Août 2025)
export const MODEL_DETECTION_CONFIG = {
  // Intervalle de mise à jour des modèles (en millisecondes) - 1 semaine
  UPDATE_INTERVAL: 7 * 24 * 60 * 60 * 1000,
  // Clé de stockage local pour la cache des modèles (legacy)
  CACHE_KEY: "openrouter_models_cache",
  // Clé pour la dernière mise à jour (legacy)
  LAST_UPDATE_KEY: "openrouter_models_last_update",
  // Nombre maximum de tentatives par modèle
  MAX_RETRIES: 3,
  // Délai entre les tentatives (en millisecondes)
  RETRY_DELAY: 1000,
  // Configuration pour le nouveau service de mise à jour automatique
  AUTO_UPDATE: {
    // Vérifie quotidiennement, met à jour hebdomadairement
    CHECK_INTERVAL: 24 * 60 * 60 * 1000, // 24 heures
    UPDATE_INTERVAL: 7 * 24 * 60 * 60 * 1000, // 7 jours
    // Seuil minimum de nouveaux modèles pour déclencher une notification
    NEW_MODELS_NOTIFICATION_THRESHOLD: 5,
    // Activer les notifications automatiques de nouveaux modèles
    ENABLE_AUTO_NOTIFICATIONS: true,
    // Sauvegarde automatique de la configuration mise à jour
    AUTO_SAVE_CONFIG: true
  }
};

// Configuration pour la validation de langue française avec traduction automatique
export const LANGUAGE_VALIDATION_CONFIG = {
  // Nombre maximum de tentatives si réponse non française (DÉSACTIVÉ - on traduit maintenant)
  MAX_LANGUAGE_RETRIES: 0,
  // Seuil minimum de confiance pour accepter une réponse française
  MIN_FRENCH_CONFIDENCE: 60,
  // Active la validation stricte de langue (avec traduction automatique)
  ENABLE_STRICT_VALIDATION: true,
  // Seuil d'alerte pour les modèles problématiques (% de réponses françaises)
  PROBLEMATIC_MODEL_THRESHOLD: 80,
  // Nombre minimum de tentatives avant de considérer un modèle comme problématique
  MIN_ATTEMPTS_FOR_EVALUATION: 3,
  // Active la traduction automatique pour les réponses non françaises
  ENABLE_AUTO_TRANSLATION: true
};

export const DATA_SOURCES = [
    {
      "id": "mcp_context7",
      "name": "MCP Server Context7",
      "type": "database",
      "description": "Base de données principale pour les contextes projets, les métriques de performance et les données historiques."
    },
    {
      "id": "web_search",
      "name": "Web Search API",
      "type": "api",
      "description": "API pour la recherche d'informations générales, de tendances et de données factuelles récentes sur le web."
    }
];

// Configuration pour le mode Premium OpenRouter (AMÉLIORÉE - Août 2025)
export const PREMIUM_CONFIG = {
  // URL pour récupérer les modèles premium avec filtre de prix
  MODELS_API_URL: "https://openrouter.ai/api/v1/models",
  // Filtres pour les modèles premium (prix entre 0.1$ et 1$ par million de tokens)
  PRICE_FILTER: {
    MIN_PRICE: 0.1,
    MAX_PRICE: 1.0
  },
  // Clé de stockage pour l'authentification Premium
  AUTH_STORAGE_KEY: "roony_premium_auth",
  // Clé de stockage pour les modèles Premium en cache
  MODELS_CACHE_KEY: "roony_premium_models",
  // Durée de cache des modèles (24 heures)
  MODELS_CACHE_DURATION: 24 * 60 * 60 * 1000,
  // Limite de crédits d'alerte
  LOW_CREDITS_THRESHOLD: 5.0,
  // Configuration pour la détection automatique des modèles Premium
  AUTO_UPDATE: {
    // Intervalle de vérification des nouveaux modèles Premium (quotidien)
    CHECK_INTERVAL: 24 * 60 * 60 * 1000,
    // Seuil de prix maximum acceptable (dollars par million de tokens)
    MAX_ACCEPTABLE_PRICE: 2.0,
    // Modèles premium recommandés par performance
    PERFORMANCE_TIERS: {
      HIGH: ['openai/gpt-4o-mini', 'anthropic/claude-3.5-haiku'],
      MEDIUM: ['google/gemini-flash-1.5', 'mistralai/mistral-7b-instruct'],
      BUDGET: ['meta-llama/llama-3.1-8b-instruct']
    }
  }
};

// Modèles Premium recommandés par catégorie de tâche
export const PREMIUM_MODELS_BY_TASK = {
  "analyse": [
    "openai/gpt-4o-mini",
    "anthropic/claude-3.5-haiku",
    "google/gemini-flash-1.5",
    "meta-llama/llama-3.1-8b-instruct"
  ],
  "génération": [
    "openai/gpt-4o-mini", 
    "anthropic/claude-3.5-haiku",
    "google/gemini-flash-1.5",
    "mistralai/mistral-7b-instruct"
  ],
  "validation": [
    "openai/gpt-4o-mini",
    "anthropic/claude-3.5-haiku",
    "google/gemini-flash-1.5"
  ],
  "synthèse": [
    "openai/gpt-4o-mini",
    "anthropic/claude-3.5-haiku", 
    "google/gemini-flash-1.5",
    "meta-llama/llama-3.1-70b-instruct"
  ]
};