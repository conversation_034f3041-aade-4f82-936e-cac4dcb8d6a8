import React, { useState, useEffect, useRef } from 'react';
import { Brain, MessageCircle, CheckCircle, ArrowRight } from 'lucide-react';
import { ChatInterface } from './ChatInterface';
import { ContextFileUploader } from './ContextFileUploader';
import RoonyMascot from './RoonyMascot';
import { roonyConversationService, type ConversationContext } from '../services/roonyConversationService';
import type { Message } from '../types';
import type { ContextFile } from '../src/services/contextFileService';

interface RoonyConversationalInterfaceProps {
  onAnalysisReady: (analysisData: PROFData, contextFiles?: ContextFile[]) => void;
  isProcessing: boolean;
}

interface PROFData {
  personnage: string;
  role: string;
  objectif: string;
  format: string;
  rawConversation: Message[];
}

const ROONEY_SYSTEM_PROMPT = `<PERSON>, un consultant expert en résolution de problèmes complexes. Tu mènes un entretien stratégique conversationnel pour collecter les informations selon la méthodologie P.R.O.F.

RÈGLES STRICTES :
1. <PERSON> poses TOUJOURS une seule question à la fois
2. Tu accuses réception de chaque réponse avant de passer à la suite
3. Tu restes naturel et conversationnel, jamais robotique
4. Tu suis EXACTEMENT la séquence définie sans la dévier
5. Tu NE DONNES JAMAIS de conseil avant d'avoir terminé la collecte P.R.O.F.

Suis cette séquence exacte :

ÉTAPE 1 - ACCUEIL :
"Bonjour. Je suis Rooney. Pour vous fournir l'analyse la plus pertinente possible, nous allons d'abord discuter de votre situation pendant quelques instants. Pour commencer, décrivez-moi simplement le problème ou la situation que vous souhaitez que j'analyse."

ÉTAPE 2 - PERSONNAGE & CONTEXTE :
Après la réponse : "Merci pour cette description. C'est très clair. Pour aller plus loin et bien cerner tous les enjeux, j'aurais besoin de quelques précisions. Pourriez-vous me parler un peu de la personne qui est au cœur de ce problème ? Quel est son rôle, sa situation, peut-être même son état d'esprit actuel ?"

ÉTAPE 3 - OBJECTIF & CONTRAINTES :
Après la réponse : "Compris. Merci pour ces détails importants. Maintenant, quel serait, pour vous, le résultat idéal ? Si tout se passait parfaitement, à quoi ressemblerait le succès ? Et à l'inverse, y a-t-il des limites à ne pas franchir, des 'lignes rouges' absolues ?"

ÉTAPE 4 - RÔLE D'EXPERT :
Après la réponse : "Merci pour ces précisions. Pour vous conseiller au mieux, quelle posture dois-je adopter ? Souhaitez-vous que j'agisse principalement comme un avocat cherchant à minimiser les risques, un stratège cherchant des opportunités, ou peut-être autre chose ?"

ÉTAPE 5 - FORMAT DE SORTIE :
Après la réponse : "Une dernière question pratique : une fois mon analyse terminée, sous quelle forme préférez-vous recevoir mes conclusions ? Un plan d'action direct et concis ? Un rapport très détaillé avec plusieurs scénarios ? Ou autre chose ?"

ÉTAPE 6 - SYNTHÈSE ET VALIDATION :
Après la réponse : "Parfait, merci. Permettez-moi de résumer pour être certain d'avoir bien tout compris.

• **La situation concerne...** [résumé du Personnage]
• **L'objectif principal est de...** [résumé de l'Objectif], tout en évitant absolument de... [contraintes]
• **Je dois aborder cela avec une perspective de...** [rappel du Rôle]
• **Et vous présenter le tout sous la forme d'un...** [rappel du Format]

Avons-nous bien fait le tour ? Est-ce que cette synthèse vous semble correcte ?"

ÉTAPE 7 - LANCEMENT :
Après confirmation : "Excellent. J'ai maintenant une vision claire et complète. Je lance l'analyse approfondie. Cela peut prendre quelques minutes. Je vous présente mes conclusions dès qu'elles sont prêtes."

Tu dois extraire et mémoriser :
- P (Personnage) : Toutes les informations sur la personne et le contexte
- R (Rôle) : Le type d'expert que tu dois être
- O (Objectif) : Le résultat idéal et les contraintes
- F (Format) : La forme de livrable souhaitée`;

export const RoonyConversationalInterface: React.FC<RoonyConversationalInterfaceProps> = ({ 
  onAnalysisReady, 
  isProcessing 
}) => {
  const [stage, setStage] = useState<ConversationContext['stage']>('welcome');
  const [conversation, setConversation] = useState<Message[]>([]);
  const [contextFiles, setContextFiles] = useState<ContextFile[]>([]);
  const [showFileUploader, setShowFileUploader] = useState(false);
  const [profData, setProfData] = useState<Partial<PROFData>>({});
  const [isRoonyProcessing, setIsRoonyProcessing] = useState(false);
  const chatEndRef = useRef<HTMLDivElement>(null);

  // Message d'accueil initial
  useEffect(() => {
    const welcomeMessage: Message = {
      sender: 'ai',
      text: "Bonjour. Je suis Rooney. Pour vous fournir l'analyse la plus pertinente possible, nous allons d'abord discuter de votre situation pendant quelques instants. Pour commencer, décrivez-moi simplement le problème ou la situation que vous souhaitez que j'analyse."
    };
    setConversation([welcomeMessage]);
  }, []);

  const sendMessageToRooney = async (userMessage: string): Promise<string> => {
    // Simuler un appel à l'API avec le système prompt de Rooney
    // Dans une vraie implémentation, utiliser votre service d'IA existant
    return new Promise((resolve) => {
      setTimeout(() => {
        const responses = getStageResponse(stage, userMessage);
        resolve(responses);
      }, 1500);
    });
  };

  const getStageResponse = (currentStage: ConversationContext['stage'], userInput: string): string => {
    const lowerInput = userInput.toLowerCase();
    
    switch (currentStage) {
      case 'welcome':
        // Stocker le problème initial et passer au personnage
        setProfData(prev => ({ ...prev, rawConversation: conversation }));
        setStage('personnage');
        return "Merci pour cette description. C'est très clair. Pour aller plus loin et bien cerner tous les enjeux, j'aurais besoin de quelques précisions. Pourriez-vous me parler un peu de la personne qui est au cœur de ce problème ? Quel est son rôle, sa situation, peut-être même son état d'esprit actuel ?";

      case 'personnage':
        setProfData(prev => ({ ...prev, personnage: userInput }));
        setStage('objectif');
        return "Compris. Merci pour ces détails importants. Maintenant, quel serait, pour vous, le résultat idéal ? Si tout se passait parfaitement, à quoi ressemblerait le succès ? Et à l'inverse, y a-t-il des limites à ne pas franchir, des 'lignes rouges' absolues ?";

      case 'objectif':
        setProfData(prev => ({ ...prev, objectif: userInput }));
        setStage('role');
        return "Merci pour ces précisions. Pour vous conseiller au mieux, quelle posture dois-je adopter ? Souhaitez-vous que j'agisse principalement comme un avocat cherchant à minimiser les risques, un stratège cherchant des opportunités, ou peut-être autre chose ?";

      case 'role':
        setProfData(prev => ({ ...prev, role: userInput }));
        setStage('format');
        return "Une dernière question pratique : une fois mon analyse terminée, sous quelle forme préférez-vous recevoir mes conclusions ? Un plan d'action direct et concis ? Un rapport très détaillé avec plusieurs scénarios ? Ou autre chose ?";

      case 'format':
        const newProfData = { ...profData, format: userInput };
        setProfData(newProfData);
        setStage('synthesis');
        return `Parfait, merci. Permettez-moi de résumer pour être certain d'avoir bien tout compris.

• **La situation concerne...** ${newProfData.personnage || 'les informations collectées'}
• **L'objectif principal est de...** ${newProfData.objectif || 'atteindre le résultat souhaité'}
• **Je dois aborder cela avec une perspective de...** ${newProfData.role || 'expert conseil'}
• **Et vous présenter le tout sous la forme d'un...** ${newProfData.format || 'rapport structuré'}

Avons-nous bien fait le tour ? Est-ce que cette synthèse vous semble correcte ?`;

      case 'synthesis':
        if (lowerInput.includes('oui') || lowerInput.includes('exact') || lowerInput.includes('parfait') || lowerInput.includes('correct')) {
          setStage('ready');
          // Déclencher l'analyse avec les données collectées
          setTimeout(() => {
            const finalProfData: PROFData = {
              personnage: profData.personnage || '',
              role: profData.role || '',
              objectif: profData.objectif || '',
              format: profData.format || '',
              rawConversation: [...conversation]
            };
            onAnalysisReady(finalProfData, contextFiles.length > 0 ? contextFiles : undefined);
          }, 2000);
          return "Excellent. J'ai maintenant une vision claire et complète. Je lance l'analyse approfondie. Cela peut prendre quelques minutes. Je vous présente mes conclusions dès qu'elles sont prêtes.";
        } else {
          return "Je comprends qu'il faut ajuster quelque chose. Pouvez-vous me dire ce qui ne vous semble pas correct dans ma synthèse ? Je vais rectifier.";
        }

      default:
        return "Je vous écoute...";
    }
  };

  const handleSendMessage = async (messageText: string) => {
    if (isRoonyProcessing || stage === 'ready') return;

    // Ajouter le message utilisateur
    const userMessage: Message = { sender: 'user', text: messageText };
    setConversation(prev => [...prev, userMessage]);
    setIsRoonyProcessing(true);

    try {
      // Obtenir la réponse de Rooney
      const roonyResponse = await sendMessageToRooney(messageText);
      
      // Ajouter la réponse de Rooney
      const aiMessage: Message = { sender: 'ai', text: roonyResponse };
      setConversation(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Erreur lors de la communication avec Rooney:', error);
      const errorMessage: Message = { 
        sender: 'ai', 
        text: "Désolé, j'ai rencontré un problème technique. Pouvez-vous répéter votre dernière réponse ?" 
      };
      setConversation(prev => [...prev, errorMessage]);
    } finally {
      setIsRoonyProcessing(false);
    }
  };

  const getStageProgress = (): number => {
    const stages = ['welcome', 'personnage', 'objectif', 'role', 'format', 'synthesis', 'ready'];
    return Math.round((stages.indexOf(stage) / (stages.length - 1)) * 100);
  };

  const getStageTitle = (): string => {
    switch (stage) {
      case 'welcome': return 'Présentation du problème';
      case 'personnage': return 'Personnage & Contexte';
      case 'objectif': return 'Objectifs & Contraintes';
      case 'role': return 'Rôle d\'expert';
      case 'format': return 'Format de sortie';
      case 'synthesis': return 'Synthèse et validation';
      case 'ready': return 'Lancement de l\'analyse';
      default: return 'Entretien en cours';
    }
  };

  return (
    <div className="flex-grow flex flex-col bg-slate-800/50 rounded-2xl shadow-2xl border border-slate-700 overflow-hidden">
      {/* En-tête avec progression */}
      <div className="bg-gradient-to-r from-purple-900/30 to-pink-900/30 p-4 border-b border-slate-600">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3">
            <Brain className="w-6 h-6 text-purple-400" />
            <h2 className="text-lg font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
              Entretien Stratégique avec Rooney
            </h2>
          </div>
          <div className="flex items-center gap-2">
            <MessageCircle className="w-4 h-4 text-slate-400" />
            <span className="text-sm text-slate-300">{getStageTitle()}</span>
          </div>
        </div>
        
        {/* Barre de progression */}
        <div className="w-full bg-slate-700 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500"
            style={{ width: `${getStageProgress()}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-slate-400 mt-1">
          <span>Début</span>
          <span>{getStageProgress()}% complété</span>
          <span>Prêt pour l'analyse</span>
        </div>
      </div>

      {/* Zone de conversation */}
      <div className="flex-grow flex flex-col overflow-hidden">
        <div className="flex-grow overflow-y-auto p-4">
          <ChatInterface 
            conversation={conversation} 
            isProcessing={isRoonyProcessing}
          />
          <div ref={chatEndRef} />
        </div>

        {/* Zone de saisie utilisateur */}
        {stage !== 'ready' && (
          <div className="p-4 border-t border-slate-600 bg-slate-900/50">
            <div className="flex flex-col gap-3">
              {/* Saisie de message */}
              <div className="flex gap-3">
                <input
                  type="text"
                  placeholder="Tapez votre réponse..."
                  className="flex-grow bg-slate-700 border border-slate-600 rounded-xl px-4 py-3 text-slate-200 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                      handleSendMessage(e.currentTarget.value);
                      e.currentTarget.value = '';
                    }
                  }}
                  disabled={isRoonyProcessing}
                />
                <button
                  onClick={() => {
                    const input = document.querySelector('input') as HTMLInputElement;
                    if (input?.value.trim()) {
                      handleSendMessage(input.value);
                      input.value = '';
                    }
                  }}
                  disabled={isRoonyProcessing}
                  className="bg-purple-600 hover:bg-purple-500 disabled:bg-slate-600 text-white px-6 py-3 rounded-xl transition-colors flex items-center gap-2"
                >
                  <ArrowRight className="w-4 h-4" />
                </button>
              </div>

              {/* Fichiers contextuels (optionnel) */}
              <div className="flex items-center justify-between">
                <button
                  type="button"
                  onClick={() => setShowFileUploader(!showFileUploader)}
                  className="text-sm text-slate-400 hover:text-slate-300 transition-colors flex items-center gap-2"
                >
                  📎 Fichiers contextuels {contextFiles.length > 0 && `(${contextFiles.length})`}
                </button>
                {stage === 'synthesis' && (
                  <div className="flex items-center gap-2 text-sm text-green-400">
                    <CheckCircle className="w-4 h-4" />
                    Informations collectées
                  </div>
                )}
              </div>

              {showFileUploader && (
                <div className="bg-slate-800/70 p-3 rounded-xl border border-slate-700">
                  <ContextFileUploader onFilesChange={setContextFiles} />
                </div>
              )}
            </div>
          </div>
        )}

        {/* Animation de fin */}
        {stage === 'ready' && (
          <div className="p-6 text-center">
            <RoonyMascot 
              animation="typing" 
              size={120} 
              duration={0}
              className="mx-auto mb-4"
            />
            <p className="text-slate-300 text-lg">
              Analyse en cours... Rooney traite vos informations 🧠
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default RoonyConversationalInterface;
