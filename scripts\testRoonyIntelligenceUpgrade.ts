/**
 * <PERSON><PERSON>t de Test - Rooney v4.2 Intelligence Framework P.R.O.F.
 * Valide le bon fonctionnement des améliorations d'intelligence
 */

import type { ConversationContext } from '../services/roonyConversationService';
import type { Step } from '../types';
import { roonyIntelligenceAPI, executeEnhancedRoonyAnalysis } from '../src/services/roonyIntelligenceAPI';

// Données de test pour simuler un contexte P.R.O.F. complet
const testConversationContext: ConversationContext = {
  stage: 'ready',
  collectedData: {
    personnage: "Un entrepreneur de 35 ans qui dirige une startup technologique en difficulté financière, stressé mais déterminé à sauver son entreprise et ses 12 employés.",
    objectif: "Restructurer l'entreprise pour éviter la faillite tout en préservant l'emploi de l'équipe. Lignes rouges : pas de licenciements massifs, pas de vente à un concurrent direct.",
    role: "Stratège d'entreprise spécialisé en restructuration",
    format: "Plan d'action détaillé avec timeline sur 6 mois"
  },
  conversation: [
    { sender: 'ai', text: 'Bon<PERSON>r, je suis Rooney...' },
    { sender: 'user', text: 'Mon entreprise est en difficulté...' }
  ]
};

const testProblemDescription = `
Notre startup technologique traverse une crise financière majeure. Nous avons 3 mois de trésorerie restante, 
nos ventes ont chuté de 40% depuis 6 mois, et nous avons des difficultés à lever des fonds. 
L'équipe de 12 personnes est inquiète, mais nous avons encore quelques clients fidèles et 
une technologie prometteuse. Comment restructurer pour survivre ?
`;

// Étapes d'analyse simulées
const testSteps: Step[] = [
  {
    id: 1,
    title: "Analyse Financière",
    description: "Évaluer la situation financière actuelle et identifier les sources de problèmes",
    techniques: ["analyse de trésorerie", "audit financier"],
    task: "analyse"
  },
  {
    id: 2,
    title: "Analyse des Parties Prenantes",
    description: "Identifier tous les acteurs concernés et leurs intérêts",
    techniques: ["mapping stakeholders", "analyse des influences"],
    task: "analyse"
  },
  {
    id: 3,
    title: "Élaboration des Solutions",
    description: "Développer des options de restructuration viables",
    techniques: ["brainstorming structuré", "analyse des scénarios"],
    task: "génération"
  },
  {
    id: 4,
    title: "Plan d'Action",
    description: "Construire un plan détaillé avec timeline et responsabilités",
    techniques: ["planification stratégique", "gestion de projet"],
    task: "synthèse"
  }
];

async function testRoonyIntelligenceUpgrade() {
  console.log('🧪 === TEST ROONEY V4.2 - INTELLIGENCE FRAMEWORK P.R.O.F. ===\n');

  try {
    // Test 1: Validation du contexte de mission
    console.log('📋 Test 1: Validation du Contexte de Mission');
    const readiness = roonyIntelligenceAPI.validateMissionReadiness(testConversationContext);
    
    console.log(`✅ Contexte prêt: ${readiness.isReady}`);
    console.log(`📊 Score de préparation: ${readiness.readinessScore}%`);
    if (readiness.missionSummary) {
      console.log('📝 Résumé de mission:');
      console.log(readiness.missionSummary);
    }
    console.log('');

    // Test 2: Aperçu du contexte de mission
    console.log('👀 Test 2: Aperçu du Contexte de Mission');
    const preview = roonyIntelligenceAPI.getMissionContextPreview(testConversationContext);
    
    if (preview.hasContext && preview.preview) {
      console.log('✅ Contexte détecté:');
      console.log(`   Personnage: ${preview.preview.personnage}`);
      console.log(`   Objectif: ${preview.preview.objectif}`);
      console.log(`   Rôle Agent: ${preview.preview.roleAgent}`);
      console.log(`   Format: ${preview.preview.formatSortie}`);
      console.log(`   Contraintes: ${preview.preview.contraintes}`);
    }
    console.log('');

    // Test 3: Analyse complète avec intelligence améliorée
    console.log('🧠 Test 3: Analyse Complète avec Intelligence Context-Aware');
    console.log('⏳ Exécution en cours...\n');
    
    const analysisResult = await executeEnhancedRoonyAnalysis(
      testConversationContext,
      testProblemDescription,
      testSteps
    );

    if (analysisResult.success && analysisResult.data) {
      const { data } = analysisResult;
      
      console.log(`✅ Analyse réussie (Mode: ${data.analysisType})`);
      console.log(`🎯 Niveau d'intelligence: ${data.intelligenceLevel}`);
      console.log('');
      
      console.log('📊 Métriques de Cohérence:');
      console.log(`   Alignement global: ${data.coherenceMetrics.globalAlignment}%`);
      console.log(`   Conformité mission: ${data.coherenceMetrics.missionCompliance}%`);
      console.log(`   Respect contraintes: ${data.coherenceMetrics.constraintAdherence}%`);
      console.log('');
      
      console.log('🔍 Résultats par Étapes:');
      data.stepResults.forEach((step, index) => {
        console.log(`   Étape ${index + 1}: ${step.stepTitle}`);
        console.log(`   Context-Aware: ${step.isContextAware ? '✅' : '❌'}`);
        console.log(`   Cohérence: ${step.coherenceScore || 'N/A'}%`);
        console.log(`   Résultat: ${step.result.slice(0, 100)}...`);
        console.log('');
      });
      
      console.log('📋 LIVRABLE FINAL:');
      console.log('=' + '='.repeat(50));
      console.log(data.finalDeliverable);
      console.log('=' + '='.repeat(50));
      
    } else {
      console.log('❌ Échec de l\'analyse:');
      console.log(`   Erreur: ${analysisResult.error}`);
      console.log(`   Fallback utilisé: ${analysisResult.fallbackUsed ? 'Oui' : 'Non'}`);
    }

    // Affichage des métriques de performance
    if (analysisResult.debugInfo) {
      console.log('');
      console.log('⚡ Métriques de Performance:');
      console.log(`   Temps d'exécution: ${analysisResult.debugInfo.performanceMetrics.executionTime}ms`);
      console.log(`   Étapes complétées: ${analysisResult.debugInfo.performanceMetrics.stepsCompleted}/${analysisResult.debugInfo.performanceMetrics.totalSteps}`);
      console.log(`   Mode d'analyse: ${analysisResult.debugInfo.analysisMode}`);
    }

  } catch (error) {
    console.error('💥 Erreur fatale lors du test:', error);
  }

  console.log('\n🎉 === FIN DU TEST ===');
}

// Exporter la fonction de test pour utilisation externe
export { testRoonyIntelligenceUpgrade };

// Exécution automatique si le script est lancé directement
if (require.main === module) {
  testRoonyIntelligenceUpgrade();
}
