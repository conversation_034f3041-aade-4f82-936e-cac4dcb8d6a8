/**
 * Service de validation de langue française
 * Vérifie que les réponses des modèles IA sont bien en français
 */

// Mots-clés français courants pour la détection de langue
const FRENCH_KEYWORDS = [
    // Articles
    'le', 'la', 'les', 'un', 'une', 'des', 'du', 'de', 'dans', 'avec', 'pour', 'sur', 'par',
    // Pronoms
    'je', 'tu', 'il', 'elle', 'nous', 'vous', 'ils', 'elles', 'ce', 'cette', 'ces',
    // Verbes auxiliaires et courants
    'être', 'avoir', 'faire', 'aller', 'venir', 'voir', 'savoir', 'pouvoir', 'vouloir', 'devoir',
    'est', 'sont', 'était', 'étaient', 'sera', 'seront', 'ai', 'as', 'a', 'avons', 'avez', 'ont',
    // Mots de liaison
    'et', 'ou', 'mais', 'donc', 'car', 'ni', 'or', 'ainsi', 'alors', 'cependant', 'néanmoins',
    // Prépositions
    'à', 'en', 'vers', 'chez', 'sous', 'entre', 'pendant', 'depuis', 'jusqu', 'avant', 'après',
    // Adverbes
    'très', 'plus', 'moins', 'bien', 'mal', 'beaucoup', 'peu', 'assez', 'trop', 'encore', 'déjà',
    // Mots interrogatifs
    'que', 'qui', 'quoi', 'où', 'quand', 'comment', 'pourquoi', 'combien',
    // Négations
    'ne', 'pas', 'plus', 'jamais', 'rien', 'personne', 'aucun', 'aucune'
];

// Mots-clés anglais courants (pour détecter les réponses en anglais)
const ENGLISH_KEYWORDS = [
    'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
    'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
    'do', 'does', 'did', 'will', 'would', 'could', 'should', 'can', 'may', 'might',
    'this', 'that', 'these', 'those', 'what', 'where', 'when', 'why', 'how',
    'not', 'no', 'yes', 'very', 'much', 'many', 'some', 'any', 'all', 'each'
];

// Patterns français spécifiques
const FRENCH_PATTERNS = [
    /\b(qu'|c'|d'|l'|n'|s'|t'|j'|m')\w+/gi, // Contractions françaises
    /\b\w+tion\b/gi, // Mots se terminant par -tion
    /\b\w+ment\b/gi, // Adverbes en -ment
    /\b\w+eur\b/gi, // Mots se terminant par -eur
    /\b\w+eux\b/gi, // Adjectifs en -eux
    /\b\w+ais\b/gi, // Terminaisons françaises -ais
    /\b\w+ez\b/gi, // Verbes à la 2ème personne du pluriel
    /\bç\w+/gi, // Mots avec cédille
    /\b\w*[àâäéèêëïîôöùûüÿ]\w*/gi // Mots avec accents français
];

// Patterns anglais spécifiques
const ENGLISH_PATTERNS = [
    /\b\w+ing\b/gi, // Gérondifs anglais
    /\b\w+ed\b/gi, // Participes passés anglais
    /\b\w+ly\b/gi, // Adverbes anglais en -ly
    /\b\w+tion\b/gi, // Mots anglais en -tion (aussi présent en français mais avec d'autres indices)
];

export interface LanguageValidationResult {
    isValid: boolean;
    confidence: number;
    detectedLanguage: 'french' | 'english' | 'mixed' | 'unknown';
    frenchScore: number;
    englishScore: number;
    issues: string[];
    suggestions: string[];
}

interface LanguageStats {
    totalValidations: number;
    frenchResponses: number;
    englishResponses: number;
    mixedResponses: number;
    unknownResponses: number;
    averageConfidence: number;
    modelStats: Record<string, {
        total: number;
        french: number;
        english: number;
        mixed: number;
        unknown: number;
    }>;
}

class LanguageValidationService {
    private stats: LanguageStats = {
        totalValidations: 0,
        frenchResponses: 0,
        englishResponses: 0,
        mixedResponses: 0,
        unknownResponses: 0,
        averageConfidence: 0,
        modelStats: {}
    };

    /**
     * Valide qu'une réponse est en français
     */
    public validateFrenchResponse(text: string, modelUsed?: string): LanguageValidationResult {
        const result = this.analyzeLanguage(text);
        
        // Mettre à jour les statistiques
        this.updateStats(result, modelUsed);
        
        return result;
    }

    /**
     * Analyse la langue d'un texte
     */
    private analyzeLanguage(text: string): LanguageValidationResult {
        if (!text || text.trim().length === 0) {
            return {
                isValid: false,
                confidence: 0,
                detectedLanguage: 'unknown',
                frenchScore: 0,
                englishScore: 0,
                issues: ['Texte vide ou invalide'],
                suggestions: ['Vérifier que le modèle a bien généré une réponse']
            };
        }

        const cleanText = text.toLowerCase().replace(/[^\w\s'àâäéèêëïîôöùûüÿç]/g, ' ');
        const words = cleanText.split(/\s+/).filter(word => word.length > 1);
        
        if (words.length === 0) {
            return {
                isValid: false,
                confidence: 0,
                detectedLanguage: 'unknown',
                frenchScore: 0,
                englishScore: 0,
                issues: ['Aucun mot significatif détecté'],
                suggestions: ['Vérifier la qualité de la réponse générée']
            };
        }

        // Calcul des scores
        const frenchScore = this.calculateFrenchScore(text, words);
        const englishScore = this.calculateEnglishScore(text, words);
        
        // Détermination de la langue
        const { detectedLanguage, confidence } = this.determineLanguage(frenchScore, englishScore);
        
        // Validation et suggestions
        const { isValid, issues, suggestions } = this.generateValidationResult(
            detectedLanguage, 
            confidence, 
            frenchScore, 
            englishScore
        );

        return {
            isValid,
            confidence,
            detectedLanguage,
            frenchScore,
            englishScore,
            issues,
            suggestions
        };
    }

    /**
     * Calcule le score français d'un texte
     */
    private calculateFrenchScore(text: string, words: string[]): number {
        let score = 0;
        const totalWords = words.length;

        // Score basé sur les mots-clés français
        const frenchWordCount = words.filter(word => 
            FRENCH_KEYWORDS.includes(word)
        ).length;
        score += (frenchWordCount / totalWords) * 40;

        // Score basé sur les patterns français
        let patternMatches = 0;
        FRENCH_PATTERNS.forEach(pattern => {
            const matches = text.match(pattern);
            if (matches) {
                patternMatches += matches.length;
            }
        });
        score += Math.min((patternMatches / totalWords) * 30, 30);

        // Bonus pour les accents français
        const accentMatches = text.match(/[àâäéèêëïîôöùûüÿç]/g);
        if (accentMatches) {
            score += Math.min((accentMatches.length / text.length) * 100 * 20, 20);
        }

        // Bonus pour les contractions françaises
        const contractionMatches = text.match(/\b(qu'|c'|d'|l'|n'|s'|t'|j'|m')\w+/gi);
        if (contractionMatches) {
            score += Math.min((contractionMatches.length / totalWords) * 10, 10);
        }

        return Math.min(score, 100);
    }

    /**
     * Calcule le score anglais d'un texte
     */
    private calculateEnglishScore(text: string, words: string[]): number {
        let score = 0;
        const totalWords = words.length;

        // Score basé sur les mots-clés anglais
        const englishWordCount = words.filter(word => 
            ENGLISH_KEYWORDS.includes(word)
        ).length;
        score += (englishWordCount / totalWords) * 40;

        // Score basé sur les patterns anglais
        let patternMatches = 0;
        ENGLISH_PATTERNS.forEach(pattern => {
            const matches = text.match(pattern);
            if (matches) {
                patternMatches += matches.length;
            }
        });
        score += Math.min((patternMatches / totalWords) * 60, 60);

        return Math.min(score, 100);
    }

    /**
     * Détermine la langue principale et le niveau de confiance
     */
    private determineLanguage(frenchScore: number, englishScore: number): {
        detectedLanguage: 'french' | 'english' | 'mixed' | 'unknown';
        confidence: number;
    } {
        const scoreDifference = Math.abs(frenchScore - englishScore);
        
        if (frenchScore < 20 && englishScore < 20) {
            return { detectedLanguage: 'unknown', confidence: 0 };
        }
        
        if (scoreDifference < 15) {
            return { 
                detectedLanguage: 'mixed', 
                confidence: Math.max(frenchScore, englishScore) 
            };
        }
        
        if (frenchScore > englishScore) {
            return { 
                detectedLanguage: 'french', 
                confidence: frenchScore 
            };
        } else {
            return { 
                detectedLanguage: 'english', 
                confidence: englishScore 
            };
        }
    }

    /**
     * Génère le résultat de validation avec issues et suggestions
     */
    private generateValidationResult(
        detectedLanguage: 'french' | 'english' | 'mixed' | 'unknown',
        confidence: number,
        frenchScore: number,
        englishScore: number
    ): { isValid: boolean; issues: string[]; suggestions: string[] } {
        const issues: string[] = [];
        const suggestions: string[] = [];
        
        let isValid = false;
        
        switch (detectedLanguage) {
            case 'french':
                if (confidence >= 60) {
                    isValid = true;
                } else {
                    issues.push(`Confiance faible pour le français (${confidence.toFixed(1)}%)`);
                    suggestions.push('Ajouter plus d\'instructions explicites pour forcer le français');
                }
                break;
                
            case 'english':
                issues.push(`Réponse détectée en anglais (score: ${englishScore.toFixed(1)}%)`);
                suggestions.push('Ajouter "RÉPONDEZ UNIQUEMENT EN FRANÇAIS" dans le prompt système');
                suggestions.push('Utiliser des modèles plus performants en français');
                break;
                
            case 'mixed':
                issues.push(`Langue mixte détectée (FR: ${frenchScore.toFixed(1)}%, EN: ${englishScore.toFixed(1)}%)`);
                suggestions.push('Renforcer les instructions de langue dans le prompt');
                suggestions.push('Ajouter une validation post-génération avec retry');
                break;
                
            case 'unknown':
                issues.push('Langue non identifiée ou texte insuffisant');
                suggestions.push('Vérifier la qualité de la génération du modèle');
                suggestions.push('Augmenter la longueur minimale des réponses');
                break;
        }
        
        return { isValid, issues, suggestions };
    }

    /**
     * Met à jour les statistiques
     */
    private updateStats(result: LanguageValidationResult, modelUsed?: string): void {
        this.stats.totalValidations++;
        
        // Mise à jour des compteurs globaux
        switch (result.detectedLanguage) {
            case 'french':
                this.stats.frenchResponses++;
                break;
            case 'english':
                this.stats.englishResponses++;
                break;
            case 'mixed':
                this.stats.mixedResponses++;
                break;
            case 'unknown':
                this.stats.unknownResponses++;
                break;
        }
        
        // Mise à jour de la confiance moyenne
        this.stats.averageConfidence = (
            (this.stats.averageConfidence * (this.stats.totalValidations - 1) + result.confidence) / 
            this.stats.totalValidations
        );
        
        // Mise à jour des statistiques par modèle
        if (modelUsed) {
            if (!this.stats.modelStats[modelUsed]) {
                this.stats.modelStats[modelUsed] = {
                    total: 0,
                    french: 0,
                    english: 0,
                    mixed: 0,
                    unknown: 0
                };
            }
            
            const modelStat = this.stats.modelStats[modelUsed];
            modelStat.total++;
            
            switch (result.detectedLanguage) {
                case 'french':
                    modelStat.french++;
                    break;
                case 'english':
                    modelStat.english++;
                    break;
                case 'mixed':
                    modelStat.mixed++;
                    break;
                case 'unknown':
                    modelStat.unknown++;
                    break;
            }
        }
    }

    /**
     * Obtient les statistiques de validation
     */
    public getStats(): LanguageStats {
        return { ...this.stats };
    }

    /**
     * Obtient les modèles les plus problématiques
     */
    public getProblematicModels(): Array<{
        modelId: string;
        total: number;
        frenchPercentage: number;
        issues: number;
    }> {
        return Object.entries(this.stats.modelStats)
            .map(([modelId, stats]) => ({
                modelId,
                total: stats.total,
                frenchPercentage: (stats.french / stats.total) * 100,
                issues: stats.english + stats.mixed + stats.unknown
            }))
            .filter(model => model.frenchPercentage < 80 || model.issues > 2)
            .sort((a, b) => a.frenchPercentage - b.frenchPercentage);
    }

    /**
     * Remet à zéro les statistiques
     */
    public resetStats(): void {
        this.stats = {
            totalValidations: 0,
            frenchResponses: 0,
            englishResponses: 0,
            mixedResponses: 0,
            unknownResponses: 0,
            averageConfidence: 0,
            modelStats: {}
        };
    }

    /**
     * Génère un rapport de conformité linguistique
     */
    public generateComplianceReport(): string {
        const stats = this.getStats();
        const problematicModels = this.getProblematicModels();
        
        const frenchPercentage = stats.totalValidations > 0 
            ? (stats.frenchResponses / stats.totalValidations) * 100 
            : 0;
        
        let report = `# 📊 Rapport de Conformité Linguistique\n\n`;
        report += `## Statistiques Globales\n`;
        report += `- **Total validations**: ${stats.totalValidations}\n`;
        report += `- **Réponses françaises**: ${stats.frenchResponses} (${frenchPercentage.toFixed(1)}%)\n`;
        report += `- **Réponses anglaises**: ${stats.englishResponses} (${((stats.englishResponses / stats.totalValidations) * 100).toFixed(1)}%)\n`;
        report += `- **Réponses mixtes**: ${stats.mixedResponses} (${((stats.mixedResponses / stats.totalValidations) * 100).toFixed(1)}%)\n`;
        report += `- **Confiance moyenne**: ${stats.averageConfidence.toFixed(1)}%\n\n`;
        
        if (problematicModels.length > 0) {
            report += `## ⚠️ Modèles Problématiques\n`;
            problematicModels.forEach(model => {
                report += `- **${model.modelId}**: ${model.frenchPercentage.toFixed(1)}% français (${model.issues} problèmes sur ${model.total} tentatives)\n`;
            });
            report += `\n`;
        }
        
        report += `## 🎯 Recommandations\n`;
        if (frenchPercentage < 90) {
            report += `- Renforcer les instructions de langue dans les prompts système\n`;
            report += `- Ajouter une validation post-génération avec retry automatique\n`;
        }
        if (problematicModels.length > 0) {
            report += `- Exclure temporairement les modèles les plus problématiques\n`;
            report += `- Tester des prompts spécifiques pour les modèles non conformes\n`;
        }
        
        return report;
    }
}

// Instance singleton du service
export const languageValidationService = new LanguageValidationService();

// Export des types pour utilisation externe
export type { LanguageStats };
