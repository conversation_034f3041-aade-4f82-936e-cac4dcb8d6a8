import React, { useState, useEffect } from 'react';
import { translationService } from '../services/translationService';

interface TranslationMonitorProps {
    className?: string;
}

const TranslationMonitor: React.FC<TranslationMonitorProps> = ({ className = '' }) => {
    const [stats, setStats] = useState<any>(null);
    const [isExpanded, setIsExpanded] = useState(false);
    const [showReport, setShowReport] = useState(false);

    // Mise à jour des statistiques toutes les 5 secondes
    useEffect(() => {
        const updateStats = () => {
            const currentStats = translationService.getStats();
            setStats(currentStats);
        };

        updateStats();
        const interval = setInterval(updateStats, 5000);

        return () => clearInterval(interval);
    }, []);

    if (!stats || stats.totalTranslations === 0) {
        return null;
    }

    // Déterminer la couleur de l'indicateur de succès (thème sombre)
    const getSuccessColor = (percentage: number) => {
        if (percentage >= 90) return 'text-emerald-400 bg-emerald-900/20 border-emerald-500/30';
        if (percentage >= 70) return 'text-yellow-400 bg-yellow-900/20 border-yellow-500/30';
        return 'text-red-400 bg-red-900/20 border-red-500/30';
    };

    const successColor = getSuccessColor(stats.successRate);

    return (
        <div className={`bg-slate-800/50 border border-slate-700 rounded-lg shadow-lg ${className}`}>
            {/* En-tête compact */}
            <div 
                className="flex items-center justify-between p-3 cursor-pointer hover:bg-slate-700/50 transition-colors"
                onClick={() => setIsExpanded(!isExpanded)}
            >
                <div className="flex items-center space-x-3">
                    <div className={`px-2 py-1 rounded-full text-xs font-medium border ${successColor}`}>
                        🔄 {stats.successRate.toFixed(1)}%
                    </div>
                    <span className="text-sm font-medium text-gray-200">
                        Traduction Auto
                    </span>
                    {stats.failedTranslations > 0 && (
                        <span className="bg-red-900/30 text-red-400 border border-red-500/30 text-xs px-2 py-1 rounded-full">
                            {stats.failedTranslations} échec{stats.failedTranslations > 1 ? 's' : ''}
                        </span>
                    )}
                </div>
                <div className="flex items-center space-x-2">
                    <span className="text-xs text-slate-400">
                        {stats.totalTranslations} traduction{stats.totalTranslations > 1 ? 's' : ''}
                    </span>
                    <svg 
                        className={`w-4 h-4 text-slate-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                    >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                </div>
            </div>

            {/* Contenu détaillé */}
            {isExpanded && (
                <div className="border-t border-slate-600 p-4 space-y-4">
                    {/* Statistiques globales */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center">
                            <div className="text-2xl font-bold text-emerald-400">{stats.successfulTranslations}</div>
                            <div className="text-xs text-slate-400">Réussies</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-red-400">{stats.failedTranslations}</div>
                            <div className="text-xs text-slate-400">Échouées</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-indigo-400">{stats.averageTranslationTime.toFixed(0)}ms</div>
                            <div className="text-xs text-slate-400">Temps moy.</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-slate-300">{stats.totalTranslations}</div>
                            <div className="text-xs text-slate-400">Total</div>
                        </div>
                    </div>

                    {/* Taux de succès */}
                    <div className="bg-slate-700/50 rounded-lg p-3 border border-slate-600/50">
                        <div className="flex justify-between items-center mb-2">
                            <span className="text-sm font-medium text-gray-200">Taux de succès</span>
                            <span className="text-sm font-bold text-gray-100">
                                {stats.successRate.toFixed(1)}%
                            </span>
                        </div>
                        <div className="w-full bg-slate-600/50 rounded-full h-2">
                            <div 
                                className="bg-emerald-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${Math.min(stats.successRate, 100)}%` }}
                            ></div>
                        </div>
                    </div>

                    {/* Performance */}
                    {stats.averageTranslationTime > 0 && (
                        <div className="bg-indigo-900/20 border border-indigo-500/30 rounded-lg p-3">
                            <h4 className="text-sm font-medium text-indigo-400 mb-2">
                                ⚡ Performance
                            </h4>
                            <div className="text-xs text-slate-300">
                                <div className="flex justify-between">
                                    <span>Temps moyen:</span>
                                    <span className="font-mono">{stats.averageTranslationTime.toFixed(0)}ms</span>
                                </div>
                                <div className="flex justify-between mt-1">
                                    <span>Statut:</span>
                                    <span className={`font-medium ${
                                        stats.averageTranslationTime < 3000 ? 'text-emerald-400' : 
                                        stats.averageTranslationTime < 8000 ? 'text-yellow-400' : 'text-red-400'
                                    }`}>
                                        {stats.averageTranslationTime < 3000 ? 'Rapide' : 
                                         stats.averageTranslationTime < 8000 ? 'Normal' : 'Lent'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Actions */}
                    <div className="flex flex-wrap gap-2 pt-2 border-t border-slate-600">
                        <button
                            onClick={() => setShowReport(!showReport)}
                            className="px-3 py-1 text-xs bg-indigo-900/30 text-indigo-400 border border-indigo-500/30 rounded-full hover:bg-indigo-900/50 transition-colors"
                        >
                            {showReport ? 'Masquer' : 'Voir'} le rapport détaillé
                        </button>
                        <button
                            onClick={() => {
                                translationService.resetStats();
                                setStats(translationService.getStats());
                            }}
                            className="px-3 py-1 text-xs bg-slate-700/50 text-slate-300 border border-slate-600/50 rounded-full hover:bg-slate-700 transition-colors"
                        >
                            Réinitialiser les stats
                        </button>
                        <button
                            onClick={() => {
                                const report = translationService.generateTranslationReport();
                                navigator.clipboard.writeText(report);
                                // Vous pourriez ajouter une notification toast ici
                            }}
                            className="px-3 py-1 text-xs bg-emerald-900/30 text-emerald-400 border border-emerald-500/30 rounded-full hover:bg-emerald-900/50 transition-colors"
                        >
                            Copier le rapport
                        </button>
                    </div>

                    {/* Rapport détaillé */}
                    {showReport && (
                        <div className="bg-slate-700/30 border border-slate-600/50 rounded-lg p-4 mt-4">
                            <h4 className="text-sm font-medium text-gray-200 mb-3">
                                📊 Rapport de Traduction Détaillé
                            </h4>
                            <div className="text-xs text-slate-300 space-y-2">
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <strong>Traductions totales:</strong> {stats.totalTranslations}
                                    </div>
                                    <div>
                                        <strong>Taux de succès:</strong> {stats.successRate.toFixed(2)}%
                                    </div>
                                    <div>
                                        <strong>Traductions réussies:</strong> {stats.successfulTranslations}
                                    </div>
                                    <div>
                                        <strong>Temps moyen:</strong> {stats.averageTranslationTime.toFixed(0)}ms
                                    </div>
                                </div>
                                
                                <div className="mt-4 p-2 bg-slate-800/50 rounded border border-slate-600/30">
                                    <div className="text-xs text-slate-400">
                                        <strong>Avantages de la traduction automatique:</strong>
                                    </div>
                                    <ul className="text-xs text-slate-300 mt-1 space-y-1">
                                        <li>• Utilisation de tous les modèles LLM disponibles</li>
                                        <li>• Réponses toujours en français pour l'utilisateur</li>
                                        <li>• Pas de rejet de modèles performants</li>
                                        <li>• Amélioration continue de la qualité</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default TranslationMonitor;
