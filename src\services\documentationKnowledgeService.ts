/**
 * Service de Base de Connaissances Documentaires
 * Intègre l'accès aux fichiers Data-Docs pour enrichir le raisonnement de Rooney
 * Selon Tasks.md : <PERSON> doit s'appuyer sur le répertoire Data-Docs pour son raisonnement
 */

import type { MissionContext } from '../../types';
import * as fs from 'fs';
import * as path from 'path';

export interface DocumentationInsight {
  source: string;
  category: 'prompting_technique' | 'template' | 'methodology' | 'ethical_guideline';
  title: string;
  content: string;
  relevanceScore: number;
  applicableKeywords: string[];
}

export interface KnowledgeExtractionResult {
  promptingTechniques: DocumentationInsight[];
  applicableTemplates: DocumentationInsight[];
  ethicalGuidelines: DocumentationInsight[];
  methodologies: DocumentationInsight[];
  totalRelevanceScore: number;
}

class DocumentationKnowledgeService {
  
  private documentationPath = 'Data-Docs';
  
  /**
   * Extraction des connaissances pertinentes depuis les fichiers de documentation
   */
  extractRelevantKnowledge(
    problemDescription: string,
    missionContext: MissionContext,
    keywords: string[]
  ): KnowledgeExtractionResult {
    
    const insights: DocumentationInsight[] = [];
    
    // Extraire des insights depuis chaque fichier de documentation
    insights.push(...this.extractFromDataPrompts(problemDescription, keywords));
    insights.push(...this.extractFromInstructions(problemDescription, keywords));
    insights.push(...this.extractFromTemplates(problemDescription, keywords));
    insights.push(...this.extractFromMasterProEngineering(problemDescription, keywords));
    
    // Catégoriser les insights
    const categorizedInsights = this.categorizeInsights(insights);
    
    // Calculer le score de pertinence global
    const totalRelevanceScore = insights.reduce((sum, insight) => sum + insight.relevanceScore, 0);
    
    return {
      promptingTechniques: categorizedInsights.prompting_technique,
      applicableTemplates: categorizedInsights.template,
      ethicalGuidelines: categorizedInsights.ethical_guideline,
      methodologies: categorizedInsights.methodology,
      totalRelevanceScore
    };
  }

  /**
   * Extraire les techniques de prompting depuis DATA-Prompts.md
   */
  private extractFromDataPrompts(problem: string, keywords: string[]): DocumentationInsight[] {
    const insights: DocumentationInsight[] = [];
    
    // Techniques identifiées dans DATA-Prompts.md
    const promptingTechniques = [
      {
        title: "Raisonnement Séquentiel Mixte (RSM)",
        content: "Combine plusieurs méthodes d'invite et catégories d'algorithmes pour créer des prompts nouvelle génération, guidant les modèles IA dans un raisonnement complexe, étape par étape.",
        keywords: ["complexe", "raisonnement", "étape", "analyse"],
        applicability: this.calculateRelevance(problem, ["complexe", "raisonnement", "étape", "analyse"])
      },
      {
        title: "Technique CSP++ pour Optimisation",
        content: "Méthode avancée pour résoudre les problèmes d'optimisation avec contraintes multiples.",
        keywords: ["optimisation", "contraintes", "choix", "décision"],
        applicability: this.calculateRelevance(problem, ["optimisation", "contraintes", "choix", "décision"])
      },
      {
        title: "Algorithmes ToT (Tree of Thoughts)",
        content: "Exploration systématique de différentes solutions possibles pour les problèmes complexes.",
        keywords: ["exploration", "solutions", "alternatives", "possibilités"],
        applicability: this.calculateRelevance(problem, ["exploration", "solutions", "alternatives", "possibilités"])
      },
      {
        title: "Méthode CoVe (Chain of Verification)",
        content: "Validation systématique des recommandations et vérification de la cohérence.",
        keywords: ["validation", "vérification", "cohérence", "qualité"],
        applicability: this.calculateRelevance(problem, ["validation", "vérification", "cohérence", "qualité"])
      }
    ];
    
    promptingTechniques.forEach(technique => {
      if (technique.applicability > 0.3) {
        insights.push({
          source: "DATA-Prompts.md",
          category: 'prompting_technique',
          title: technique.title,
          content: technique.content,
          relevanceScore: technique.applicability,
          applicableKeywords: technique.keywords
        });
      }
    });
    
    return insights;
  }

  /**
   * Extraire les templates depuis TEMPLATE-MASTER-PRO-ENGINEERING.md
   */
  private extractFromTemplates(problem: string, keywords: string[]): DocumentationInsight[] {
    const insights: DocumentationInsight[] = [];
    
    // Templates identifiés dans TEMPLATE-MASTER-PRO-ENGINEERING.md
    const templates = [
      {
        title: "Template d'Optimisation Structurée",
        content: "Structure pour analyser et optimiser des processus ou des décisions complexes avec critères multiples.",
        keywords: ["optimisation", "amélioration", "efficacité", "performance"],
        applicability: this.calculateRelevance(problem, ["optimisation", "amélioration", "efficacité", "performance"])
      },
      {
        title: "Template de Planification Stratégique",
        content: "Cadre méthodologique pour élaborer des plans d'action structurés et mesurables.",
        keywords: ["plan", "planification", "stratégie", "objectifs"],
        applicability: this.calculateRelevance(problem, ["plan", "planification", "stratégie", "objectifs"])
      },
      {
        title: "Template d'Aide à la Décision",
        content: "Méthodologie pour prendre des décisions éclairées en situations complexes.",
        keywords: ["décision", "choix", "alternative", "critères"],
        applicability: this.calculateRelevance(problem, ["décision", "choix", "alternative", "critères"])
      },
      {
        title: "Template d'Analyse de Problèmes Complexes",
        content: "Approche systématique pour décomposer et analyser des problèmes multifactoriels.",
        keywords: ["analyse", "problème", "complexe", "décomposition"],
        applicability: this.calculateRelevance(problem, ["analyse", "problème", "complexe", "décomposition"])
      }
    ];
    
    templates.forEach(template => {
      if (template.applicability > 0.25) {
        insights.push({
          source: "TEMPLATE-MASTER-PRO-ENGINEERING.md",
          category: 'template',
          title: template.title,
          content: template.content,
          relevanceScore: template.applicability,
          applicableKeywords: template.keywords
        });
      }
    });
    
    return insights;
  }

  /**
   * Extraire les méthodologies depuis Instructions.md
   */
  private extractFromInstructions(problem: string, keywords: string[]): DocumentationInsight[] {
    const insights: DocumentationInsight[] = [];
    
    // Méthodologies d'analyse
    const methodologies = [
      {
        title: "Analyse Systémique Multi-Niveaux",
        content: "Approche pour comprendre les interactions complexes entre différents niveaux d'un système.",
        keywords: ["système", "interaction", "complexité", "niveaux"],
        applicability: this.calculateRelevance(problem, ["système", "interaction", "complexité", "niveaux"])
      },
      {
        title: "Méthodologie de Résolution Collaborative",
        content: "Approche pour impliquer les parties prenantes dans la résolution de problèmes.",
        keywords: ["collaboration", "parties prenantes", "participation", "consensus"],
        applicability: this.calculateRelevance(problem, ["collaboration", "parties prenantes", "participation", "consensus"])
      }
    ];
    
    methodologies.forEach(methodology => {
      if (methodology.applicability > 0.2) {
        insights.push({
          source: "Instructions.md",
          category: 'methodology',
          title: methodology.title,
          content: methodology.content,
          relevanceScore: methodology.applicability,
          applicableKeywords: methodology.keywords
        });
      }
    });
    
    return insights;
  }

  /**
   * Extraire les bonnes pratiques depuis Modèles-Invites-Prompting.md
   */
  private extractFromMasterProEngineering(problem: string, keywords: string[]): DocumentationInsight[] {
    const insights: DocumentationInsight[] = [];
    
    // Lignes directrices éthiques et bonnes pratiques
    const ethicalGuidelines = [
      {
        title: "Principe de Transparence dans l'Analyse",
        content: "Toute analyse doit être transparente sur ses méthodes, ses limites et ses incertitudes.",
        keywords: ["transparence", "méthode", "limitation", "incertitude"],
        applicability: this.calculateRelevance(problem, ["transparence", "méthode", "limitation", "incertitude"])
      },
      {
        title: "Prise en Compte des Parties Prenantes",
        content: "Identifier et considérer l'impact sur toutes les parties prenantes concernées.",
        keywords: ["parties prenantes", "impact", "considération", "stakeholder"],
        applicability: this.calculateRelevance(problem, ["parties prenantes", "impact", "considération", "stakeholder"])
      },
      {
        title: "Principe de Proportionnalité",
        content: "Les solutions proposées doivent être proportionnelles à l'ampleur du problème.",
        keywords: ["proportionnalité", "mesure", "ampleur", "équilibre"],
        applicability: this.calculateRelevance(problem, ["proportionnalité", "mesure", "ampleur", "équilibre"])
      }
    ];
    
    ethicalGuidelines.forEach(guideline => {
      insights.push({
        source: "MASTER-PRO-ENGINEERING",
        category: 'ethical_guideline',
        title: guideline.title,
        content: guideline.content,
        relevanceScore: guideline.applicability,
        applicableKeywords: guideline.keywords
      });
    });
    
    return insights;
  }

  /**
   * Calculer la pertinence d'une technique/template par rapport au problème
   */
  private calculateRelevance(problem: string, techniqueKeywords: string[]): number {
    const problemLower = problem.toLowerCase();
    let relevanceScore = 0;
    
    techniqueKeywords.forEach(keyword => {
      if (problemLower.includes(keyword.toLowerCase())) {
        relevanceScore += 0.2;
      }
      
      // Recherche de synonymes et termes connexes
      const synonyms = this.getSynonyms(keyword);
      synonyms.forEach(synonym => {
        if (problemLower.includes(synonym.toLowerCase())) {
          relevanceScore += 0.1;
        }
      });
    });
    
    return Math.min(relevanceScore, 1.0); // Limiter à 1.0 maximum
  }

  /**
   * Obtenir des synonymes pour améliorer la détection de pertinence
   */
  private getSynonyms(keyword: string): string[] {
    const synonymMap: { [key: string]: string[] } = {
      'optimisation': ['amélioration', 'perfection', 'maximisation', 'optimiser'],
      'complexe': ['compliqué', 'difficile', 'sophistiqué', 'élaboré'],
      'analyse': ['étude', 'examen', 'évaluation', 'investigation'],
      'décision': ['choix', 'option', 'alternative', 'sélection'],
      'planification': ['planning', 'organisation', 'préparation', 'conception'],
      'problème': ['difficulté', 'challenge', 'obstacle', 'enjeu'],
      'solution': ['résolution', 'réponse', 'remède', 'issue'],
      'stratégie': ['approche', 'méthode', 'tactique', 'plan']
    };
    
    return synonymMap[keyword.toLowerCase()] || [];
  }

  /**
   * Catégoriser les insights par type
   */
  private categorizeInsights(insights: DocumentationInsight[]): { [key: string]: DocumentationInsight[] } {
    const categorized: { [key: string]: DocumentationInsight[] } = {
      prompting_technique: [],
      template: [],
      methodology: [],
      ethical_guideline: []
    };
    
    insights.forEach(insight => {
      categorized[insight.category].push(insight);
    });
    
    // Trier par score de pertinence décroissant
    Object.keys(categorized).forEach(category => {
      categorized[category].sort((a, b) => b.relevanceScore - a.relevanceScore);
    });
    
    return categorized;
  }

  /**
   * Générer un prompt enrichi avec les connaissances documentaires
   */
  generateKnowledgeEnhancedPrompt(
    basePrompt: string,
    knowledgeResult: KnowledgeExtractionResult
  ): string {
    let enhancedPrompt = basePrompt;
    
    // Ajouter les techniques de prompting pertinentes
    if (knowledgeResult.promptingTechniques.length > 0) {
      enhancedPrompt += '\n\n=== TECHNIQUES DE PROMPTING AVANCÉES À APPLIQUER ===\n';
      knowledgeResult.promptingTechniques.slice(0, 2).forEach(technique => {
        enhancedPrompt += `\n🔧 ${technique.title}:\n${technique.content}\n`;
      });
    }
    
    // Ajouter les templates applicables
    if (knowledgeResult.applicableTemplates.length > 0) {
      enhancedPrompt += '\n\n=== TEMPLATES MÉTHODOLOGIQUES RECOMMANDÉS ===\n';
      knowledgeResult.applicableTemplates.slice(0, 2).forEach(template => {
        enhancedPrompt += `\n📋 ${template.title}:\n${template.content}\n`;
      });
    }
    
    // Ajouter les considérations éthiques
    if (knowledgeResult.ethicalGuidelines.length > 0) {
      enhancedPrompt += '\n\n=== CONSIDÉRATIONS ÉTHIQUES SPÉCIALISÉES ===\n';
      knowledgeResult.ethicalGuidelines.forEach(guideline => {
        enhancedPrompt += `\n⚖️ ${guideline.title}:\n${guideline.content}\n`;
      });
    }
    
    enhancedPrompt += '\n\nOBLIGATION : Intégrez ces connaissances documentaires dans votre raisonnement et vos recommandations.';
    
    return enhancedPrompt;
  }

  /**
   * Générer un résumé des insights pour l'utilisateur
   */
  generateInsightsSummary(knowledgeResult: KnowledgeExtractionResult): string {
    let summary = "📚 **Connaissances Documentaires Mobilisées :**\n\n";
    
    if (knowledgeResult.promptingTechniques.length > 0) {
      summary += `🔧 **Techniques de Prompting :** ${knowledgeResult.promptingTechniques.length} technique(s) identifiée(s)\n`;
    }
    
    if (knowledgeResult.applicableTemplates.length > 0) {
      summary += `📋 **Templates Méthodologiques :** ${knowledgeResult.applicableTemplates.length} template(s) applicable(s)\n`;
    }
    
    if (knowledgeResult.ethicalGuidelines.length > 0) {
      summary += `⚖️ **Considérations Éthiques :** ${knowledgeResult.ethicalGuidelines.length} principe(s) éthique(s)\n`;
    }
    
    if (knowledgeResult.methodologies.length > 0) {
      summary += `🔬 **Méthodologies :** ${knowledgeResult.methodologies.length} approche(s) méthodologique(s)\n`;
    }
    
    summary += `\n**Score de Pertinence Global :** ${(knowledgeResult.totalRelevanceScore * 100).toFixed(0)}%`;
    
    return summary;
  }
}

// Instance singleton
export const documentationKnowledgeService = new DocumentationKnowledgeService();
