/**
 * Script de test pour vérifier les correctifs de scroll
 * Simule les conditions qui causaient le blocage du scroll
 */

console.log('🔧 Test des correctifs de blocage scroll - Studio Agentique Roony');

// Test 1: Vérification CSS initial
function testInitialCSS() {
    console.log('\n📋 Test 1: Vérification CSS initial');
    
    const bodyOverflow = getComputedStyle(document.body).overflow;
    const htmlOverflow = getComputedStyle(document.documentElement).overflow;
    
    console.log(`Body overflow: ${bodyOverflow}`);
    console.log(`HTML overflow: ${htmlOverflow}`);
    
    const bodyOverflowY = getComputedStyle(document.body).overflowY;
    const htmlOverflowY = getComputedStyle(document.documentElement).overflowY;
    
    console.log(`Body overflow-y: ${bodyOverflowY}`);
    console.log(`HTML overflow-y: ${htmlOverflowY}`);
    
    if (bodyOverflowY === 'hidden' || htmlOverflowY === 'hidden') {
        console.error('❌ ÉCHEC: Scroll vertical bloqué dans le CSS');
        return false;
    }
    
    console.log('✅ SUCCÈS: CSS initial correct');
    return true;
}

// Test 2: Simulation de blocage et récupération
function testScrollBlockingRecovery() {
    console.log('\n📋 Test 2: Simulation blocage et récupération');
    
    // Simuler un blocage de scroll
    console.log('🔴 Simulation du blocage...');
    document.body.style.overflow = 'hidden';
    
    setTimeout(() => {
        const isBlocked = document.body.style.overflow === 'hidden';
        console.log(`État après blocage: ${isBlocked ? 'BLOQUÉ' : 'LIBRE'}`);
        
        // Le service de protection devrait corriger automatiquement
        setTimeout(() => {
            const isStillBlocked = document.body.style.overflow === 'hidden';
            if (isStillBlocked) {
                console.error('❌ ÉCHEC: Le scroll est toujours bloqué après 3 secondes');
                return false;
            } else {
                console.log('✅ SUCCÈS: Récupération automatique du scroll');
                return true;
            }
        }, 3000);
    }, 100);
}

// Test 3: Vérification des éléments scrollables
function testScrollableElements() {
    console.log('\n📋 Test 3: Vérification éléments scrollables');
    
    const chatInterface = document.querySelector('.overflow-y-auto');
    const thinkingSpace = document.querySelector('[class*="ThinkingSpace"]');
    
    if (chatInterface) {
        const style = getComputedStyle(chatInterface);
        console.log(`ChatInterface overflow-y: ${style.overflowY}`);
        
        if (style.overflowY === 'auto' || style.overflowY === 'scroll') {
            console.log('✅ ChatInterface scrollable');
        } else {
            console.error('❌ ChatInterface non scrollable');
        }
    }
    
    // Tester le scroll programmatique
    try {
        window.scrollTo({ top: 100, behavior: 'smooth' });
        console.log('✅ Scroll programmatique fonctionne');
    } catch (error) {
        console.error('❌ Erreur scroll programmatique:', error);
    }
}

// Test 4: Simulation d'erreur API
function testApiErrorRecovery() {
    console.log('\n📋 Test 4: Simulation erreur API');
    
    // Simuler le comportement d'une erreur API
    document.body.style.overflow = 'hidden';
    console.log('🔴 Simulation d\'erreur API avec blocage scroll');
    
    // Simuler la récupération via le service de protection
    if (window.scrollProtectionService) {
        window.scrollProtectionService.handleApiError();
        console.log('🔧 Service de protection appelé');
        
        setTimeout(() => {
            const isRestored = document.body.style.overflow !== 'hidden';
            if (isRestored) {
                console.log('✅ SUCCÈS: Scroll restauré après erreur API');
            } else {
                console.error('❌ ÉCHEC: Scroll non restauré après erreur API');
            }
        }, 500);
    } else {
        console.warn('⚠️ Service de protection non disponible dans les tests');
    }
}

// Test 5: Vérification des animations GSAP
function testGSAPAnimations() {
    console.log('\n📋 Test 5: Vérification animations GSAP');
    
    // Vérifier que GSAP est disponible
    if (typeof gsap !== 'undefined') {
        console.log('✅ GSAP disponible');
        
        // Créer un élément test
        const testDiv = document.createElement('div');
        testDiv.style.position = 'fixed';
        testDiv.style.top = '-100px';
        testDiv.style.opacity = '0';
        document.body.appendChild(testDiv);
        
        // Tester une animation GSAP
        gsap.to(testDiv, {
            opacity: 1,
            duration: 0.1,
            onComplete: () => {
                // Vérifier que le scroll n'est pas bloqué après l'animation
                const isScrollBlocked = document.body.style.overflow === 'hidden';
                if (isScrollBlocked) {
                    console.error('❌ ÉCHEC: Animation GSAP a bloqué le scroll');
                } else {
                    console.log('✅ SUCCÈS: Animation GSAP sans blocage scroll');
                }
                
                // Nettoyer
                document.body.removeChild(testDiv);
            }
        });
    } else {
        console.warn('⚠️ GSAP non disponible pour les tests');
    }
}

// Exécution des tests
function runAllTests() {
    console.log('🚀 Début des tests de blocage scroll\n');
    
    const tests = [
        testInitialCSS,
        testScrollableElements,
        testApiErrorRecovery,
        testGSAPAnimations
    ];
    
    tests.forEach((test, index) => {
        try {
            test();
        } catch (error) {
            console.error(`❌ Erreur dans le test ${index + 1}:`, error);
        }
    });
    
    // Test de récupération en dernier (asynchrone)
    setTimeout(testScrollBlockingRecovery, 1000);
    
    console.log('\n🏁 Tests terminés. Vérifiez les résultats ci-dessus.');
}

// Exposer les fonctions pour tests manuels
window.scrollTests = {
    runAll: runAllTests,
    testCSS: testInitialCSS,
    testRecovery: testScrollBlockingRecovery,
    testElements: testScrollableElements,
    testApiError: testApiErrorRecovery,
    testGSAP: testGSAPAnimations
};

// Exécution automatique après chargement
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
} else {
    // DOM déjà chargé
    setTimeout(runAllTests, 1000);
}

export default window.scrollTests;
