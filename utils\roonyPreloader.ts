/**
 * Utilitaire pour précharger les animations GIF de Roony
 * Améliore les performances en chargeant les images en arrière-plan
 */

// URLs des animations Roony
const ROONY_ANIMATION_URLS = [
  'https://res.cloudinary.com/dte5zykne/image/upload/v1756018014/Rory-1_mv9r9e.gif', // greeting
  'https://res.cloudinary.com/dte5zykne/image/upload/v1756018023/Rory-8_mkgqpz.gif', // idea
  'https://res.cloudinary.com/dte5zykne/image/upload/v1756018014/Rory-2_tnbjew.gif', // pointing-up
  'https://res.cloudinary.com/dte5zykne/image/upload/v1756018013/Rory-4_ignnks.gif', // pointing-right
  'https://res.cloudinary.com/dte5zykne/image/upload/v1756018012/Rory-3_hhtgop.gif', // disagreement
  'https://res.cloudinary.com/dte5zykne/image/upload/v1756018012/Rory-6_tcwihe.gif', // typing
  'https://res.cloudinary.com/dte5zykne/image/upload/v1756018012/Rory-11_qvdvx0.gif', // slide-point
  'https://res.cloudinary.com/dte5zykne/image/upload/v1756018012/Rory-12_xnnlqi.gif', // slide-thumbs
  'https://res.cloudinary.com/dte5zykne/image/upload/v1756018011/Rory-5_vdgcre.gif'  // proud
] as const;

interface PreloadResult {
  url: string;
  loaded: boolean;
  error?: string;
}

class RoonyPreloader {
  private preloadedImages: Map<string, HTMLImageElement> = new Map();
  private loadingPromises: Map<string, Promise<HTMLImageElement>> = new Map();
  private loadedCount = 0;
  private totalCount = ROONY_ANIMATION_URLS.length;

  /**
   * Précharge une image spécifique
   */
  private preloadImage(url: string): Promise<HTMLImageElement> {
    // Si déjà en cours de chargement, retourner la promesse existante
    if (this.loadingPromises.has(url)) {
      return this.loadingPromises.get(url)!;
    }

    // Si déjà chargée, retourner l'image
    if (this.preloadedImages.has(url)) {
      return Promise.resolve(this.preloadedImages.get(url)!);
    }

    const promise = new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        this.preloadedImages.set(url, img);
        this.loadingPromises.delete(url);
        this.loadedCount++;
        console.log(`✅ Animation Roony préchargée: ${this.loadedCount}/${this.totalCount}`);
        resolve(img);
      };

      img.onerror = (error) => {
        this.loadingPromises.delete(url);
        console.error(`❌ Erreur préchargement animation Roony: ${url}`, error);
        reject(new Error(`Impossible de charger l'animation: ${url}`));
      };

      // Commencer le chargement
      img.src = url;
    });

    this.loadingPromises.set(url, promise);
    return promise;
  }

  /**
   * Précharge toutes les animations de Roony
   */
  public async preloadAllAnimations(): Promise<PreloadResult[]> {
    console.log('🎬 Début du préchargement des animations Roony...');
    
    const results: PreloadResult[] = [];
    
    const promises = ROONY_ANIMATION_URLS.map(async (url) => {
      try {
        await this.preloadImage(url);
        return { url, loaded: true };
      } catch (error) {
        return { 
          url, 
          loaded: false, 
          error: error instanceof Error ? error.message : 'Erreur inconnue' 
        };
      }
    });

    const loadResults = await Promise.allSettled(promises);
    
    loadResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        results.push({
          url: ROONY_ANIMATION_URLS[index],
          loaded: false,
          error: result.reason?.message || 'Erreur de chargement'
        });
      }
    });

    const successCount = results.filter(r => r.loaded).length;
    console.log(`🎬 Préchargement terminé: ${successCount}/${this.totalCount} animations chargées`);

    return results;
  }

  /**
   * Précharge les animations prioritaires (les plus utilisées)
   */
  public async preloadPriorityAnimations(): Promise<void> {
    const priorityUrls = [
      ROONY_ANIMATION_URLS[0], // greeting
      ROONY_ANIMATION_URLS[1], // idea
      ROONY_ANIMATION_URLS[5], // typing
      ROONY_ANIMATION_URLS[7], // slide-thumbs
      ROONY_ANIMATION_URLS[8]  // proud
    ];

    console.log('⚡ Préchargement des animations prioritaires...');
    
    const promises = priorityUrls.map(url => this.preloadImage(url).catch(console.error));
    await Promise.allSettled(promises);
    
    console.log('⚡ Animations prioritaires préchargées');
  }

  /**
   * Vérifie si une animation est déjà préchargée
   */
  public isPreloaded(url: string): boolean {
    return this.preloadedImages.has(url);
  }

  /**
   * Obtient le pourcentage de chargement
   */
  public getLoadingProgress(): number {
    return Math.round((this.loadedCount / this.totalCount) * 100);
  }

  /**
   * Obtient le nombre d'animations chargées
   */
  public getLoadedCount(): number {
    return this.loadedCount;
  }

  /**
   * Obtient le nombre total d'animations
   */
  public getTotalCount(): number {
    return this.totalCount;
  }

  /**
   * Nettoie le cache (utile pour les tests ou le rechargement)
   */
  public clearCache(): void {
    this.preloadedImages.clear();
    this.loadingPromises.clear();
    this.loadedCount = 0;
    console.log('🧹 Cache des animations Roony nettoyé');
  }
}

// Instance singleton
export const roonyPreloader = new RoonyPreloader();

// Hook pour utiliser le préchargeur dans les composants React
export const useRoonyPreloader = () => {
  return {
    preloadAll: () => roonyPreloader.preloadAllAnimations(),
    preloadPriority: () => roonyPreloader.preloadPriorityAnimations(),
    isPreloaded: (url: string) => roonyPreloader.isPreloaded(url),
    getProgress: () => roonyPreloader.getLoadingProgress(),
    getLoadedCount: () => roonyPreloader.getLoadedCount(),
    getTotalCount: () => roonyPreloader.getTotalCount(),
    clearCache: () => roonyPreloader.clearCache()
  };
};

export default roonyPreloader;
