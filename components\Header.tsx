import React, { useState } from 'react';

interface HeaderProps {
  usageMode?: 'free' | 'premium';
  onModeToggle?: () => void;
  onPremiumAuth?: () => void;
  showModeButton?: boolean;
}

const RoonyLogo: React.FC = () => (
    <img
        src="/logo-header.png"
        alt="Roony Logo"
        className="h-8 w-8 mr-3 object-contain"
        style={{ filter: 'brightness(0) invert(1)' }} // Convertit le logo noir en blanc pour le thème sombre
    />
);

export const Header: React.FC<HeaderProps> = ({
  usageMode = 'free',
  onModeToggle,
  onPremiumAuth,
  showModeButton = false
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <header className="bg-slate-900/60 backdrop-blur-sm border-b border-slate-800 sticky top-0 z-10">
      <div className="container mx-auto px-4 py-3 flex items-center justify-between">
        <div className="flex items-center">
          <RoonyLogo />
          <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-indigo-500">
            Roony - A votre service
          </h1>
        </div>
        
        {showModeButton && (
          <div className="flex items-center gap-3">
            {usageMode === 'premium' ? (
              // Mode Premium actif - Bouton avec indicateur de statut
              <div className="flex items-center gap-2">
                <button
                  onClick={onModeToggle}
                  onMouseEnter={() => setShowTooltip(true)}
                  onMouseLeave={() => setShowTooltip(false)}
                  className="flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-lg hover:from-purple-700 hover:to-indigo-700 relative"
                >
                  <span className="text-lg">🌟</span>
                  <span className="text-sm">Premium</span>
                </button>

                {showTooltip && (
                  <div className="absolute top-full right-0 mt-2 bg-slate-800 text-white text-xs rounded-lg py-2 px-3 whitespace-nowrap z-20 border border-slate-600">
                    Mode Premium actif - Modèles puissants
                    <div className="absolute -top-1 right-4 w-2 h-2 bg-slate-800 border-l border-t border-slate-600 rotate-45"></div>
                  </div>
                )}

                <div className="text-xs text-slate-400 hidden md:block">
                  Modèles premium actifs
                </div>
              </div>
            ) : (
              // Mode Gratuit - Bouton simple pour activer Premium
              <button
                onClick={onPremiumAuth || onModeToggle}
                onMouseEnter={() => setShowTooltip(true)}
                onMouseLeave={() => setShowTooltip(false)}
                className="flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 bg-slate-700/50 text-slate-300 hover:bg-slate-600/50 border border-slate-600 hover:border-purple-500/50 relative"
              >
                <span className="text-lg">💝</span>
                <span className="text-sm">Activer Premium</span>
                <span className="text-xs opacity-75">🔒</span>
              </button>
            )}

            {showTooltip && usageMode === 'free' && (
              <div className="absolute top-full right-0 mt-2 bg-slate-800 text-white text-xs rounded-lg py-2 px-3 whitespace-nowrap z-20 border border-slate-600">
                Cliquer pour vous connecter au mode Premium
                <div className="absolute -top-1 right-4 w-2 h-2 bg-slate-800 border-l border-t border-slate-600 rotate-45"></div>
              </div>
            )}
          </div>
        )}
      </div>
    </header>
  );
};