import { OPENROUTER_MODELS_API, MODEL_DETECTION_CONFIG, OPENROUTER_MODELS } from '../constants';
import { autoModelUpdateService } from './autoModelUpdateService';
import type { Step } from '../types';

// Interface pour les modèles OpenRouter
interface OpenRouterModel {
    id: string;
    name: string;
    pricing: {
        prompt: string;
        completion: string;
    };
    context_length: number;
    per_request_limits?: {
        prompt_tokens?: string;
        completion_tokens?: string;
    };
}

interface ModelStats {
    totalModels: number;
    freeModels: number;
    lastUpdate: number;
    availableByTask: Record<string, string[]>;
    modelsByCategory: {
        analyse: number;
        génération: number;
        validation: number;
        synthèse: number;
    };
    topModels: {
        reasoning: string[];
        coding: string[];
        multimodal: string[];
        large: string[];
    };
}

/**
 * Service de détection automatique des modèles OpenRouter
 * Gère la mise à jour hebdomadaire et la rotation intelligente des modèles
 */
export class ModelDetectionService {
    private static instance: ModelDetectionService;
    private modelsCache: OpenRouterModel[] = [];
    private lastUpdate = 0;
    private updatePromise: Promise<void> | null = null;
    private autoUpdateInterval: NodeJS.Timeout | null = null;
    private updateNotificationCallback: ((message: string) => void) | null = null;

    private constructor() {
        this.loadFromLocalStorage();
        this.startAutoUpdateScheduler();
    }

    public static getInstance(): ModelDetectionService {
        if (!ModelDetectionService.instance) {
            ModelDetectionService.instance = new ModelDetectionService();
        }
        return ModelDetectionService.instance;
    }

    /**
     * Charge la cache depuis le localStorage
     */
    private loadFromLocalStorage(): void {
        try {
            const cachedModels = localStorage.getItem(MODEL_DETECTION_CONFIG.CACHE_KEY);
            const lastUpdateStr = localStorage.getItem(MODEL_DETECTION_CONFIG.LAST_UPDATE_KEY);
            
            if (cachedModels && lastUpdateStr) {
                this.modelsCache = JSON.parse(cachedModels);
                this.lastUpdate = parseInt(lastUpdateStr, 10);
                console.log(`📦 Cache des modèles chargée: ${this.modelsCache.length} modèles`);
            }
        } catch (error) {
            console.warn('Erreur lors du chargement de la cache des modèles:', error);
        }
    }

    /**
     * Sauvegarde la cache dans le localStorage
     */
    private saveToLocalStorage(): void {
        try {
            localStorage.setItem(MODEL_DETECTION_CONFIG.CACHE_KEY, JSON.stringify(this.modelsCache));
            localStorage.setItem(MODEL_DETECTION_CONFIG.LAST_UPDATE_KEY, this.lastUpdate.toString());
        } catch (error) {
            console.warn('Erreur lors de la sauvegarde de la cache des modèles:', error);
        }
    }

    /**
     * Récupère la liste des modèles depuis l'API OpenRouter
     */
    private async fetchModelsFromAPI(): Promise<OpenRouterModel[]> {
        try {
            console.log('🔍 Récupération des modèles depuis OpenRouter API...');
            const response = await fetch(OPENROUTER_MODELS_API);
            
            if (!response.ok) {
                throw new Error(`Erreur API: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            const models = data.data || [];
            
            console.log(`✅ ${models.length} modèles récupérés depuis l'API`);
            return models;
        } catch (error) {
            console.error('❌ Erreur lors de la récupération des modèles:', error);
            return [];
        }
    }

    /**
     * Vérifie si un modèle est gratuit
     */
    private isModelFree(model: OpenRouterModel): boolean {
        // Vérifier si le modèle contient ":free" dans l'ID
        if (model.id.includes(':free')) {
            return true;
        }
        
        // Vérifier si les prix sont à 0
        const promptPrice = parseFloat(model.pricing.prompt || '0');
        const completionPrice = parseFloat(model.pricing.completion || '0');
        
        return promptPrice === 0 && completionPrice === 0;
    }

    /**
     * Met à jour la cache des modèles si nécessaire
     * Utilise maintenant le service de mise à jour automatique pour une meilleure cohérence
     */
    public async updateModelsIfNeeded(): Promise<void> {
        const now = Date.now();
        const shouldUpdate = this.modelsCache.length === 0 || 
                           (now - this.lastUpdate) > MODEL_DETECTION_CONFIG.UPDATE_INTERVAL;

        if (!shouldUpdate) {
            return;
        }

        // Éviter les mises à jour simultanées
        if (this.updatePromise) {
            return this.updatePromise;
        }

        // Utiliser le service de mise à jour automatique pour une approche unifiée
        this.updatePromise = this.performUpdateWithAutoService();
        await this.updatePromise;
        this.updatePromise = null;
    }

    /**
     * Effectue la mise à jour via le service automatique (nouvelle approche)
     */
    private async performUpdateWithAutoService(): Promise<void> {
        try {
            console.log('🔄 Mise à jour via le service automatique unifié...');
            
            // Déclencher une mise à jour du service automatique
            const report = await autoModelUpdateService.performUpdate(true);
            
            // Synchroniser la cache locale avec les données du service automatique
            const status = autoModelUpdateService.getUpdateStatus();
            this.lastUpdate = status.lastUpdate.getTime();
            
            // Charger les modèles gratuits depuis le service automatique
            const freeModels = ['analyse', 'génération', 'validation', 'synthèse']
                .flatMap(task => autoModelUpdateService.getFreeModelsForTask(task as Step['task']));
            
            this.modelsCache = freeModels;
            this.saveToLocalStorage();
            
            console.log(`✅ Synchronisation avec le service automatique: ${report.totalModels} modèles total, ${report.freeModels} gratuits`);
            
        } catch (error) {
            console.warn('⚠️ Échec de la mise à jour automatique, fallback vers l\'ancienne méthode...');
            await this.performUpdate();
        }
    }

    /**
     * Effectue la mise à jour des modèles
     */
    private async performUpdate(): Promise<void> {
        try {
            const models = await this.fetchModelsFromAPI();
            
            if (models.length > 0) {
                this.modelsCache = models;
                this.lastUpdate = Date.now();
                this.saveToLocalStorage();
                
                const freeModels = models.filter(m => this.isModelFree(m));
                console.log(`🎉 Cache mise à jour: ${models.length} modèles total, ${freeModels.length} gratuits`);
                
                // Log des nouveaux modèles gratuits détectés
                this.logNewFreeModels(freeModels);
            }
        } catch (error) {
            console.error('❌ Erreur lors de la mise à jour des modèles:', error);
        }
    }

    /**
     * Log des nouveaux modèles gratuits détectés
     */
    private logNewFreeModels(freeModels: OpenRouterModel[]): void {
        const currentFreeIds = new Set(Object.values(OPENROUTER_MODELS).flat());
        const newFreeModels = freeModels.filter(m => !currentFreeIds.has(m.id));
        
        if (newFreeModels.length > 0) {
            console.log('🆕 Nouveaux modèles gratuits détectés:');
            newFreeModels.forEach(model => {
                console.log(`  - ${model.id} (${model.name})`);
            });
        }
    }

    /**
     * Vérifie si un modèle spécifique est disponible et gratuit
     */
    public isModelAvailable(modelId: string): boolean {
        if (this.modelsCache.length === 0) {
            return true; // Fallback si pas de cache
        }
        
        const model = this.modelsCache.find(m => m.id === modelId);
        return model ? this.isModelFree(model) : false;
    }

    /**
     * Obtient les statistiques des modèles avec analyse détaillée
     */
    public getModelStats(): ModelStats {
        const freeModels = this.modelsCache.filter(m => this.isModelFree(m));

        const availableByTask: Record<string, string[]> = {};
        Object.entries(OPENROUTER_MODELS).forEach(([task, models]) => {
            availableByTask[task] = models.filter(modelId => this.isModelAvailable(modelId));
        });

        // Compter les modèles par catégorie
        const modelsByCategory = {
            analyse: OPENROUTER_MODELS.analyse.length,
            génération: OPENROUTER_MODELS.génération.length,
            validation: OPENROUTER_MODELS.validation.length,
            synthèse: OPENROUTER_MODELS.synthèse.length
        };

        // Identifier les modèles spécialisés
        const topModels = {
            reasoning: OPENROUTER_MODELS.analyse.filter(id =>
                id.includes('deepseek-r1') || id.includes('qwq') || id.includes('thinking')
            ).slice(0, 5),
            coding: OPENROUTER_MODELS.génération.filter(id =>
                id.includes('coder') || id.includes('code') || id.includes('devstral')
            ).slice(0, 5),
            multimodal: [...OPENROUTER_MODELS.analyse, ...OPENROUTER_MODELS.validation].filter(id =>
                id.includes('vision') || id.includes('vl') || id.includes('multimodal')
            ).slice(0, 3),
            large: Object.values(OPENROUTER_MODELS).flat().filter(id =>
                id.includes('405b') || id.includes('235b') || id.includes('253b') || id.includes('72b')
            ).slice(0, 5)
        };

        return {
            totalModels: this.modelsCache.length,
            freeModels: freeModels.length,
            lastUpdate: this.lastUpdate,
            availableByTask,
            modelsByCategory,
            topModels
        };
    }

    /**
     * Obtient tous les modèles gratuits disponibles
     */
    public getFreeModels(): OpenRouterModel[] {
        return this.modelsCache.filter(m => this.isModelFree(m));
    }

    /**
     * Sélectionne le meilleur modèle pour un contexte donné
     */
    public getBestModelForContext(task: Step['task'], context?: {
        complexity?: 'simple' | 'medium' | 'complex';
        requiresReasoning?: boolean;
        requiresCoding?: boolean;
        requiresMultimodal?: boolean;
        preferLarge?: boolean;
    }): string {
        const availableModels = OPENROUTER_MODELS[task].filter(modelId =>
            this.isModelAvailable(modelId)
        );

        if (availableModels.length === 0) {
            console.warn(`⚠️ Aucun modèle disponible pour la tâche: ${task}`);
            return OPENROUTER_MODELS[task][0]; // Fallback
        }

        // Si pas de contexte spécifique, rotation aléatoire
        if (!context) {
            return availableModels[Math.floor(Math.random() * availableModels.length)];
        }

        // Sélection intelligente basée sur le contexte
        let preferredModels = availableModels;

        // Modèles de raisonnement pour les tâches complexes
        if (context.requiresReasoning || context.complexity === 'complex') {
            const reasoningModels = preferredModels.filter(id =>
                id.includes('deepseek-r1') || id.includes('qwq') || id.includes('thinking') ||
                id.includes('nemotron') || id.includes('235b')
            );
            if (reasoningModels.length > 0) preferredModels = reasoningModels;
        }

        // Modèles de code pour les tâches de programmation
        if (context.requiresCoding) {
            const codingModels = preferredModels.filter(id =>
                id.includes('coder') || id.includes('code') || id.includes('devstral') ||
                id.includes('deepcoder')
            );
            if (codingModels.length > 0) preferredModels = codingModels;
        }

        // Modèles multimodaux
        if (context.requiresMultimodal) {
            const multimodalModels = preferredModels.filter(id =>
                id.includes('vision') || id.includes('vl') || id.includes('kimi-vl')
            );
            if (multimodalModels.length > 0) preferredModels = multimodalModels;
        }

        // Modèles larges pour les tâches complexes
        if (context.preferLarge || context.complexity === 'complex') {
            const largeModels = preferredModels.filter(id =>
                id.includes('405b') || id.includes('235b') || id.includes('253b') ||
                id.includes('72b') || id.includes('70b')
            );
            if (largeModels.length > 0) preferredModels = largeModels;
        }

        // Sélection finale avec rotation
        const selectedModel = preferredModels[Math.floor(Math.random() * preferredModels.length)];
        console.log(`🎯 Modèle sélectionné pour ${task} (contexte: ${JSON.stringify(context)}): ${selectedModel}`);

        return selectedModel;
    }

    /**
     * Démarre le planificateur de mise à jour automatique (24h)
     */
    private startAutoUpdateScheduler(): void {
        // Vérifier immédiatement si une mise à jour est nécessaire
        this.checkAndPerformAutoUpdate();

        // Planifier les vérifications toutes les heures
        this.autoUpdateInterval = setInterval(() => {
            this.checkAndPerformAutoUpdate();
        }, 60 * 60 * 1000); // Toutes les heures

        console.log('🕐 Planificateur de mise à jour automatique démarré (vérification toutes les heures)');
    }

    /**
     * Vérifie et effectue une mise à jour automatique si nécessaire
     */
    private async checkAndPerformAutoUpdate(): Promise<void> {
        const now = Date.now();
        const timeSinceLastUpdate = now - this.lastUpdate;
        const twentyFourHours = 24 * 60 * 60 * 1000;

        // Si plus de 24h depuis la dernière mise à jour
        if (this.lastUpdate > 0 && timeSinceLastUpdate >= twentyFourHours) {
            console.log('🔄 Mise à jour automatique des modèles (24h écoulées)...');

            try {
                const oldFreeCount = this.modelsCache.filter(m => this.isModelFree(m)).length;
                await this.performUpdate();
                const newFreeCount = this.modelsCache.filter(m => this.isModelFree(m)).length;

                // Notifier l'utilisateur de la mise à jour
                const message = `✅ Modèles IA mis à jour automatiquement ! ${newFreeCount} modèles gratuits disponibles ${newFreeCount > oldFreeCount ? `(+${newFreeCount - oldFreeCount} nouveaux)` : ''}`;
                this.notifyUpdate(message);

            } catch (error) {
                console.error('❌ Erreur lors de la mise à jour automatique:', error);
                this.notifyUpdate('⚠️ Erreur lors de la mise à jour automatique des modèles');
            }
        }
    }

    /**
     * Enregistre un callback pour les notifications de mise à jour
     */
    public setUpdateNotificationCallback(callback: (message: string) => void): void {
        this.updateNotificationCallback = callback;
    }

    /**
     * Notifie l'utilisateur d'une mise à jour
     */
    private notifyUpdate(message: string): void {
        if (this.updateNotificationCallback) {
            this.updateNotificationCallback(message);
        }
        console.log(`📢 ${message}`);
    }

    /**
     * Obtient le temps restant avant la prochaine mise à jour automatique
     */
    public getTimeUntilNextAutoUpdate(): { hours: number; minutes: number } {
        if (this.lastUpdate === 0) {
            return { hours: 0, minutes: 0 };
        }

        const now = Date.now();
        const timeSinceLastUpdate = now - this.lastUpdate;
        const twentyFourHours = 24 * 60 * 60 * 1000;
        const timeUntilNext = Math.max(0, twentyFourHours - timeSinceLastUpdate);

        const hours = Math.floor(timeUntilNext / (60 * 60 * 1000));
        const minutes = Math.floor((timeUntilNext % (60 * 60 * 1000)) / (60 * 1000));

        return { hours, minutes };
    }

    /**
     * Force une mise à jour immédiate des modèles
     */
    public async forceUpdate(): Promise<void> {
        this.lastUpdate = 0; // Force la mise à jour
        await this.updateModelsIfNeeded();
    }

    /**
     * Nettoie les ressources (pour les tests ou le démontage)
     */
    public cleanup(): void {
        if (this.autoUpdateInterval) {
            clearInterval(this.autoUpdateInterval);
            this.autoUpdateInterval = null;
        }
    }
}

// Instance singleton
export const modelDetectionService = ModelDetectionService.getInstance();
