/**
 * Commutateur de mode pour basculer entre le mode gratuit et Premium
 */

import React, { useState, useEffect } from 'react';
import { premiumAuthService } from '../services/premiumAuthService';
import { premiumApiService } from '../services/premiumApiService';
import type { AuthenticationState } from '../types';

interface ModeToggleProps {
  currentMode: 'free' | 'premium';
  onModeChange: (mode: 'free' | 'premium') => void;
  disabled?: boolean;
}

const ModeToggle: React.FC<ModeToggleProps> = ({ currentMode, onModeChange, disabled = false }) => {
  const [authState, setAuthState] = useState<AuthenticationState>({
    user: { isAuthenticated: false, plan: 'free' },
    isLoading: false
  });

  useEffect(() => {
    const unsubscribe = premiumAuthService.onAuthChange(setAuthState);
    return unsubscribe;
  }, []);

  const premiumStatus = premiumApiService.getPremiumStatus();
  const isPremiumAvailable = premiumStatus.isAvailable;

  const handleModeChange = (mode: 'free' | 'premium') => {
    if (disabled) return;

    if (mode === 'premium' && !authState.user.isAuthenticated) {
      // Si Premium n'est pas authentifié, déclencher l'authentification
      console.log('🔒 Mode Premium nécessite une authentification - ouverture du modal');
      // Déclencher l'ouverture du modal d'authentification Premium
      onModeChange('premium'); // Cela devrait déclencher l'ouverture du modal dans App.tsx
      return;
    }

    onModeChange(mode);
  };

  return (
    <div className="mode-toggle-container">
      <div className="mode-toggle-header">
        <h4>🎯 Mode d'utilisation</h4>
        <div className="mode-status">
          {currentMode === 'premium' && isPremiumAvailable && (
            <span className="premium-active">🌟 Premium actif</span>
          )}
          {currentMode === 'free' && (
            <span className="free-active">💝 Mode gratuit</span>
          )}
        </div>
      </div>

      <div className="toggle-buttons">
        <button
          className={`mode-btn free-btn ${currentMode === 'free' ? 'active' : ''}`}
          onClick={() => handleModeChange('free')}
          disabled={disabled}
        >
          <div className="mode-icon">💝</div>
          <div className="mode-info">
            <div className="mode-name">Mode Gratuit</div>
            <div className="mode-desc">Modèles gratuits OpenRouter</div>
          </div>
        </button>

        <button
          className={`mode-btn premium-btn ${currentMode === 'premium' ? 'active' : ''} ${!authState.user.isAuthenticated ? 'unavailable' : ''}`}
          onClick={() => handleModeChange('premium')}
          disabled={disabled}
          title={!authState.user.isAuthenticated ? 'Cliquez pour vous connecter au mode Premium' : 'Mode Premium actif'}
        >
          <div className="mode-icon">🌟</div>
          <div className="mode-info">
            <div className="mode-name">Mode Premium</div>
            <div className="mode-desc">
              {authState.user.isAuthenticated ? (
                `${premiumStatus.modelsCount} modèles premium`
              ) : (
                'Connexion requise'
              )}
            </div>
          </div>
          {!authState.user.isAuthenticated && (
            <div className="unlock-badge">🔒</div>
          )}
        </button>
      </div>

      {currentMode === 'premium' && authState.user.isAuthenticated && (
        <div className="premium-details">
          <div className="premium-info-grid">
            <div className="info-item">
              <span className="info-label">Crédits:</span>
              <span className={`info-value ${premiumStatus.creditsStatus.isLow ? 'warning' : ''}`}>
                {premiumStatus.creditsStatus.credits.toFixed(2)}$
              </span>
            </div>
            <div className="info-item">
              <span className="info-label">Modèles:</span>
              <span className="info-value">{premiumStatus.modelsCount}</span>
            </div>
          </div>
        </div>
      )}

      <style>{`
        .mode-toggle-container {
          background: rgba(30, 41, 59, 0.7);
          border-radius: 12px;
          padding: 20px;
          margin: 16px 0;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
          border: 1px solid rgba(100, 116, 139, 0.3);
          backdrop-filter: blur(10px);
        }

        .mode-toggle-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
        }

        .mode-toggle-header h4 {
          margin: 0;
          color: #f1f5f9;
          font-size: 1.1em;
          font-weight: 600;
        }

        .mode-status {
          font-size: 0.9em;
          font-weight: 500;
        }

        .premium-active {
          color: #38ef7d;
        }

        .free-active {
          color: #a78bfa;
        }

        .toggle-buttons {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 12px;
          margin-bottom: 16px;
        }

        .mode-btn {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 16px;
          border: 2px solid rgba(100, 116, 139, 0.4);
          border-radius: 10px;
          background: rgba(51, 65, 85, 0.6);
          cursor: pointer;
          transition: all 0.2s ease;
          text-align: left;
          position: relative;
          color: #e2e8f0;
        }

        .mode-btn:hover:not(:disabled) {
          border-color: rgba(100, 116, 139, 0.6);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
          background: rgba(51, 65, 85, 0.8);
        }

        .mode-btn.active {
          border-color: #11998e;
          background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
          color: white;
          box-shadow: 0 8px 24px rgba(17, 153, 142, 0.3);
        }

        .mode-btn.free-btn.active {
          border-color: #667eea;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
        }

        .mode-btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .mode-btn.unavailable {
          opacity: 0.4;
          background: rgba(51, 65, 85, 0.3);
          color: #64748b;
        }

        .mode-icon {
          font-size: 1.5em;
          flex-shrink: 0;
        }

        .mode-info {
          flex: 1;
        }

        .mode-name {
          font-weight: 600;
          font-size: 1em;
          margin-bottom: 4px;
        }

        .mode-desc {
          font-size: 0.85em;
          opacity: 0.8;
        }

        .mode-btn.active .mode-desc {
          opacity: 0.9;
        }

        .unlock-badge {
          position: absolute;
          top: 8px;
          right: 8px;
          font-size: 0.8em;
          opacity: 0.7;
        }

        .premium-details {
          background: rgba(51, 65, 85, 0.6);
          border-radius: 8px;
          padding: 12px;
          border: 1px solid rgba(100, 116, 139, 0.3);
          backdrop-filter: blur(5px);
        }

        .premium-info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
        }

        .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .info-label {
          font-size: 0.9em;
          color: #cbd5e1;
          font-weight: 500;
        }

        .info-value {
          font-size: 0.9em;
          font-weight: 600;
          color: #f1f5f9;
        }

        .info-value.warning {
          color: #fbbf24;
        }

        @media (max-width: 768px) {
          .toggle-buttons {
            grid-template-columns: 1fr;
          }
          
          .premium-info-grid {
            grid-template-columns: 1fr;
            gap: 8px;
          }
        }
      `}</style>
    </div>
  );
};

export default ModeToggle;
