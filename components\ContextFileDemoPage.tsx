import React, { useState } from 'react';
import { ContextFileUploader } from './ContextFileUploader';
import { contextFileService, type ContextFile } from '../src/services/contextFileService';

export const ContextFileDemoPage: React.FC = () => {
  const [contextFiles, setContextFiles] = useState<ContextFile[]>([]);
  const [globalContext, setGlobalContext] = useState<string>('');

  const handleFilesChange = (files: ContextFile[]) => {
    setContextFiles(files);
    
    // Générer le contexte global pour visualisation
    const context = contextFileService.generateGlobalContext();
    setGlobalContext(context);
  };

  const clearAllFiles = () => {
    contextFileService.clearAllContextFiles();
    setContextFiles([]);
    setGlobalContext('');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            📎 Gestionnaire de Fichiers Contextuels
          </h1>
          <p className="text-slate-300 text-lg">
            Téléchargez des documents pour enrichir l'analyse de l'Expert-Conseil Roony
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Colonne gauche - Téléchargement */}
          <div className="space-y-6">
            <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-600">
              <h2 className="text-2xl font-semibold text-white mb-4">
                🔄 Téléchargement de Fichiers
              </h2>
              <ContextFileUploader onFilesChange={handleFilesChange} />
            </div>

            {/* Statistiques */}
            {contextFiles.length > 0 && (
              <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-600">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">📊 Statistiques</h3>
                  <button
                    onClick={clearAllFiles}
                    className="bg-red-600 hover:bg-red-500 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    Tout effacer
                  </button>
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  {(() => {
                    const stats = contextFileService.getFileStats();
                    return (
                      <>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-indigo-400">{stats.totalFiles}</div>
                          <div className="text-sm text-slate-400">Fichiers</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-emerald-400">
                            {(stats.totalSize / 1024 / 1024).toFixed(2)} MB
                          </div>
                          <div className="text-sm text-slate-400">Taille totale</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-400">
                            {Object.keys(stats.typeBreakdown).length}
                          </div>
                          <div className="text-sm text-slate-400">Types différents</div>
                        </div>
                      </>
                    );
                  })()}
                </div>

                <div className="mt-4 pt-4 border-t border-slate-600">
                  <h4 className="text-sm font-medium text-slate-300 mb-2">Répartition par type :</h4>
                  <div className="flex flex-wrap gap-2">
                    {(() => {
                      const stats = contextFileService.getFileStats();
                      return Object.entries(stats.typeBreakdown).map(([type, count]) => (
                        <span
                          key={type}
                          className="bg-slate-700 text-slate-200 px-3 py-1 rounded-full text-xs"
                        >
                          {type}: {count}
                        </span>
                      ));
                    })()}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Colonne droite - Aperçu du contexte */}
          <div className="space-y-6">
            <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-600">
              <h2 className="text-2xl font-semibold text-white mb-4">
                👁️ Aperçu du Contexte Global
              </h2>
              
              {globalContext ? (
                <div className="space-y-4">
                  <div className="bg-slate-700/50 rounded-lg p-4 max-h-96 overflow-y-auto">
                    <pre className="text-sm text-slate-300 whitespace-pre-wrap font-mono">
                      {globalContext}
                    </pre>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm text-slate-400">
                    <span>📝</span>
                    <span>Ce contexte sera automatiquement inclus dans les prompts expert-conseil</span>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12 text-slate-400">
                  <div className="text-6xl mb-4">📄</div>
                  <p className="text-lg">Aucun fichier téléchargé</p>
                  <p className="text-sm mt-2">
                    Téléchargez des fichiers pour voir le contexte qui sera envoyé à l'IA
                  </p>
                </div>
              )}
            </div>

            {/* Guide d'utilisation */}
            <div className="bg-gradient-to-br from-indigo-900/30 to-purple-900/30 rounded-xl p-6 border border-indigo-500/30">
              <h3 className="text-lg font-semibold text-white mb-4">💡 Guide d'utilisation</h3>
              
              <div className="space-y-3 text-sm text-slate-300">
                <div className="flex items-start gap-3">
                  <span className="text-emerald-400 mt-1">✓</span>
                  <div>
                    <strong>Fichiers supportés :</strong> Images (JPG, PNG, GIF), Documents (PDF, DOC), Texte (TXT, MD, JSON)
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <span className="text-emerald-400 mt-1">✓</span>
                  <div>
                    <strong>Taille limite :</strong> 100MB par fichier
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <span className="text-emerald-400 mt-1">✓</span>
                  <div>
                    <strong>Extraction automatique :</strong> Le texte est extrait des images et documents
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <span className="text-emerald-400 mt-1">✓</span>
                  <div>
                    <strong>Contexte intelligent :</strong> Les fichiers enrichissent automatiquement les analyses
                  </div>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-indigo-500/30">
                <p className="text-xs text-indigo-300">
                  💬 <strong>Astuce :</strong> Plus vous fournissez de contexte, plus les conseils de Roony seront précis et personnalisés !
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
