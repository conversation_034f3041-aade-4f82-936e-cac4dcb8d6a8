/**
 * Service de Base de Connaissances Fondamentales
 * Contient les principes de droit, règles administratives et certitudes de base
 * que Rooney doit affirmer AVANT toute analyse
 */

export interface FundamentalPrinciple {
  domain: string;
  principle: string;
  description: string;
  applicableContexts: string[];
  certaintyLevel: 'ABSOLUTE' | 'HIGH' | 'CONTEXTUAL';
}

export interface KnowledgeDomain {
  name: string;
  principles: FundamentalPrinciple[];
  commonScenarios: string[];
  typicalSolutions: string[];
}

class KnowledgeBaseService {
  private knowledgeDomains: Map<string, KnowledgeDomain> = new Map();

  constructor() {
    this.initializeKnowledgeBase();
  }

  /**
   * Initialise la base de connaissances avec les domaines fondamentaux
   */
  private initializeKnowledgeBase(): void {
    // Domaine : Droit des étrangers et immigration
    this.knowledgeDomains.set('immigration', {
      name: 'Droit des étrangers et immigration',
      principles: [
        {
          domain: 'immigration',
          principle: 'Droit au travail des membres de famille UE',
          description: 'Le conjoint d\'un citoyen de l\'UE a automatiquement le droit de travailler en France',
          applicableContexts: ['passeport talent', 'membre famille UE', 'conjoint européen'],
          certaintyLevel: 'ABSOLUTE'
        },
        {
          domain: 'immigration',
          principle: 'Indépendance des procédures administratives',
          description: 'Un litige avec l\'URSSAF n\'empêche pas le renouvellement d\'un titre de séjour si le litige est documenté et contesté',
          applicableContexts: ['renouvellement titre', 'litige urssaf', 'changement statut'],
          certaintyLevel: 'HIGH'
        },
        {
          domain: 'immigration',
          principle: 'Continuité de l\'activité pendant instruction',
          description: 'Pendant l\'instruction d\'une demande de changement de statut, l\'activité peut généralement continuer',
          applicableContexts: ['changement statut', 'renouvellement', 'instruction préfecture'],
          certaintyLevel: 'CONTEXTUAL'
        }
      ],
      commonScenarios: [
        'Changement de statut étudiant vers salarié',
        'Renouvellement avec litige URSSAF en cours',
        'Conjoint UE demandant autorisation de travail',
        'Entrepreneur étranger créant une entreprise'
      ],
      typicalSolutions: [
        'Constituer un dossier explicatif détaillé',
        'Joindre les preuves de contestation du litige',
        'Demander un récépissé autorisant le travail',
        'Préparer les justificatifs de revenus alternatifs'
      ]
    });

    // Domaine : Droit social et URSSAF
    this.knowledgeDomains.set('social', {
      name: 'Droit social et URSSAF',
      principles: [
        {
          domain: 'social',
          principle: 'Présomption d\'innocence en matière de cotisations',
          description: 'Un litige contesté n\'équivaut pas à une fraude avérée tant qu\'il n\'y a pas de décision définitive',
          applicableContexts: ['litige urssaf', 'redressement', 'contrôle urssaf'],
          certaintyLevel: 'ABSOLUTE'
        },
        {
          domain: 'social',
          principle: 'Droit de contestation et recours',
          description: 'Tout entrepreneur a le droit de contester un redressement URSSAF et de faire valoir ses arguments',
          applicableContexts: ['redressement urssaf', 'mise en demeure', 'contrôle'],
          certaintyLevel: 'ABSOLUTE'
        },
        {
          domain: 'social',
          principle: 'Séparation des procédures administratives',
          description: 'Les difficultés avec l\'URSSAF ne doivent pas automatiquement impacter les autres démarches administratives',
          applicableContexts: ['préfecture', 'autres administrations', 'titre séjour'],
          certaintyLevel: 'HIGH'
        }
      ],
      commonScenarios: [
        'Redressement URSSAF contesté',
        'Contrôle avec désaccord sur les cotisations',
        'Mise en demeure pendant litige',
        'Impact sur autres démarches administratives'
      ],
      typicalSolutions: [
        'Contester formellement dans les délais',
        'Constituer un dossier de preuves solide',
        'Demander un échéancier si nécessaire',
        'Séparer les procédures administratives'
      ]
    });

    // Domaine : Droit des entreprises
    this.knowledgeDomains.set('entreprise', {
      name: 'Droit des entreprises',
      principles: [
        {
          domain: 'entreprise',
          principle: 'Liberté d\'entreprendre',
          description: 'Tout résident légal en France a le droit de créer et développer une activité économique',
          applicableContexts: ['création entreprise', 'développement activité', 'auto-entrepreneur'],
          certaintyLevel: 'ABSOLUTE'
        },
        {
          domain: 'entreprise',
          principle: 'Protection des entrepreneurs de bonne foi',
          description: 'Les erreurs administratives de bonne foi ne doivent pas compromettre la viabilité d\'une entreprise',
          applicableContexts: ['erreurs déclaratives', 'bonne foi', 'régularisation'],
          certaintyLevel: 'HIGH'
        }
      ],
      commonScenarios: [
        'Création d\'entreprise par un étranger',
        'Régularisation de situation administrative',
        'Développement d\'activité existante'
      ],
      typicalSolutions: [
        'Vérifier les autorisations nécessaires',
        'Régulariser les situations en suspens',
        'Optimiser le statut juridique'
      ]
    });
  }

  /**
   * Récupère les principes fondamentaux applicables à un contexte donné
   */
  getApplicablePrinciples(context: string, keywords: string[]): FundamentalPrinciple[] {
    const applicablePrinciples: FundamentalPrinciple[] = [];

    for (const domain of this.knowledgeDomains.values()) {
      for (const principle of domain.principles) {
        // Vérifier si le principe s'applique au contexte
        const isApplicable = principle.applicableContexts.some(ctx => 
          keywords.some(keyword => 
            ctx.toLowerCase().includes(keyword.toLowerCase()) ||
            keyword.toLowerCase().includes(ctx.toLowerCase())
          )
        );

        if (isApplicable) {
          applicablePrinciples.push(principle);
        }
      }
    }

    // Trier par niveau de certitude (ABSOLUTE > HIGH > CONTEXTUAL)
    return applicablePrinciples.sort((a, b) => {
      const certaintyOrder = { 'ABSOLUTE': 3, 'HIGH': 2, 'CONTEXTUAL': 1 };
      return certaintyOrder[b.certaintyLevel] - certaintyOrder[a.certaintyLevel];
    });
  }

  /**
   * Génère une phrase d'affirmation des certitudes pour un contexte donné
   */
  generateCertaintyStatement(context: string, keywords: string[]): string {
    const principles = this.getApplicablePrinciples(context, keywords);
    
    if (principles.length === 0) {
      return "Commençons par poser les bases de votre situation...";
    }

    const absolutePrinciples = principles.filter(p => p.certaintyLevel === 'ABSOLUTE');
    const highPrinciples = principles.filter(p => p.certaintyLevel === 'HIGH');

    let statement = "Ce qui est acquis dans votre situation :\n\n";

    if (absolutePrinciples.length > 0) {
      statement += "✅ **Certitudes absolues :**\n";
      absolutePrinciples.forEach(principle => {
        statement += `- ${principle.description}\n`;
      });
      statement += "\n";
    }

    if (highPrinciples.length > 0) {
      statement += "✅ **Principes établis :**\n";
      highPrinciples.forEach(principle => {
        statement += `- ${principle.description}\n`;
      });
      statement += "\n";
    }

    return statement + "Ces bases étant posées, analysons maintenant votre situation spécifique...";
  }

  /**
   * Récupère les scénarios typiques pour un domaine
   */
  getTypicalScenarios(domain: string): string[] {
    const domainData = this.knowledgeDomains.get(domain);
    return domainData ? domainData.commonScenarios : [];
  }

  /**
   * Récupère les solutions typiques pour un domaine
   */
  getTypicalSolutions(domain: string): string[] {
    const domainData = this.knowledgeDomains.get(domain);
    return domainData ? domainData.typicalSolutions : [];
  }

  /**
   * Identifie le domaine principal d'un problème basé sur les mots-clés
   */
  identifyPrimaryDomain(keywords: string[]): string {
    const domainScores = new Map<string, number>();

    for (const [domainKey, domain] of this.knowledgeDomains.entries()) {
      let score = 0;
      
      // Score basé sur les principes applicables
      for (const principle of domain.principles) {
        for (const context of principle.applicableContexts) {
          if (keywords.some(keyword => 
            context.toLowerCase().includes(keyword.toLowerCase()) ||
            keyword.toLowerCase().includes(context.toLowerCase())
          )) {
            score += principle.certaintyLevel === 'ABSOLUTE' ? 3 : 
                    principle.certaintyLevel === 'HIGH' ? 2 : 1;
          }
        }
      }

      // Score basé sur les scénarios communs
      for (const scenario of domain.commonScenarios) {
        if (keywords.some(keyword => 
          scenario.toLowerCase().includes(keyword.toLowerCase())
        )) {
          score += 1;
        }
      }

      domainScores.set(domainKey, score);
    }

    // Retourner le domaine avec le score le plus élevé
    let maxScore = 0;
    let primaryDomain = 'general';
    
    for (const [domain, score] of domainScores.entries()) {
      if (score > maxScore) {
        maxScore = score;
        primaryDomain = domain;
      }
    }

    return primaryDomain;
  }

  /**
   * Ajoute un nouveau principe à la base de connaissances
   */
  addPrinciple(domain: string, principle: FundamentalPrinciple): void {
    const domainData = this.knowledgeDomains.get(domain);
    if (domainData) {
      domainData.principles.push(principle);
    }
  }

  /**
   * Récupère tous les domaines disponibles
   */
  getAllDomains(): string[] {
    return Array.from(this.knowledgeDomains.keys());
  }
}

// Instance singleton
export const knowledgeBaseService = new KnowledgeBaseService();
