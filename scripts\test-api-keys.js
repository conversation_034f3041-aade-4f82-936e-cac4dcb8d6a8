/**
 * Script de test pour vérifier le système de clés API
 */

import { apiKeyManager } from '../services/apiKeyManager.js';

console.log('🧪 === TEST DU SYSTÈME DE CLÉS API ===\n');

// Test 1: Diagnostic
console.log('1. 🔍 DIAGNOSTIC INITIAL:');
apiKeyManager.diagnoseApiKeys();

// Test 2: Initialisation forcée
console.log('\n2. 🔧 INITIALISATION FORCÉE:');
apiKeyManager.initializeAllKeyStats();

// Test 3: Statistiques complètes
console.log('\n3. 📊 STATISTIQUES COMPLÈTES:');
const stats = apiKeyManager.getStats();
console.log(`Total de clés dans les statistiques: ${Object.keys(stats).length}`);

Object.entries(stats).forEach(([name, stat], index) => {
    console.log(`${index + 1}. ${name}:`);
    console.log(`   - Requêtes: ${stat.totalRequests}`);
    console.log(`   - Succès: ${stat.successfulRequests}`);
    console.log(`   - Échecs: ${stat.failedRequests}`);
    console.log(`   - Bloquée: ${stat.isBlacklisted ? 'Oui' : 'Non'}`);
});

// Test 4: Test de sélection de clés
console.log('\n4. 🎯 TEST DE SÉLECTION DE CLÉS:');
for (let i = 0; i < 5; i++) {
    const selectedKey = apiKeyManager.getNextApiKey();
    console.log(`Sélection ${i + 1}: ${selectedKey.keyId}`);
}

console.log('\n✅ === FIN DES TESTS ===');
