/**
 * Script de test pour le nouveau système Expert-Conseil de Rooney
 * Teste les 5 principes fondamentaux d'amélioration
 */

import { expertConsultantService } from './services/expertConsultantService';
import { knowledgeBaseService } from './services/knowledgeBaseService';
import { hypothesisGeneratorService } from './services/hypothesisGeneratorService';
import { problemDecompositionService } from './services/problemDecompositionService';
import { actionPlanFormatterService } from './services/actionPlanFormatterService';
import { WORKFLOW_STEPS } from '../constants';

// Cas de test typiques
const testCases = [
  {
    name: "Cas Immigration - Renouvellement avec litige URSSAF",
    userInput: "Je dois renouveler mon titre de séjour mais j'ai un litige en cours avec l'URSSAF. Je suis conjoint d'un citoyen français et j'ai une entreprise. Est-ce que le litige va bloquer mon renouvellement ?",
    stepIndex: 0
  },
  {
    name: "Cas Entrepreneur - Changement de statut",
    userInput: "Je suis actuellement étudiant et je veux changer de statut pour créer mon entreprise. J'ai déjà commencé quelques activités mais je ne sais pas si c'est légal.",
    stepIndex: 1
  },
  {
    name: "Cas Complexe - Multiple entités",
    userInput: "Ma situation est compliquée : j'ai des problèmes avec l'URSSAF, la préfecture me demande des documents, et Pôle Emploi ne comprend pas mon statut. Je ne sais plus par où commencer.",
    stepIndex: 2
  }
];

console.log("🧪 TEST DU NOUVEAU SYSTÈME EXPERT-CONSEIL ROONEY");
console.log("=" .repeat(60));

testCases.forEach((testCase, index) => {
  console.log(`\n📋 TEST ${index + 1}: ${testCase.name}`);
  console.log("-".repeat(50));
  
  try {
    // Test du service de base de connaissances
    console.log("\n🧠 1. TEST BASE DE CONNAISSANCES:");
    const keywords = testCase.userInput.toLowerCase().split(' ').filter(word => word.length > 3);
    const domain = knowledgeBaseService.identifyPrimaryDomain(keywords);
    const principles = knowledgeBaseService.getApplicablePrinciples(testCase.userInput, keywords);
    const certaintyStatement = knowledgeBaseService.generateCertaintyStatement(testCase.userInput, keywords);
    
    console.log(`   Domaine identifié: ${domain}`);
    console.log(`   Principes applicables: ${principles.length}`);
    console.log(`   Certitudes: ${certaintyStatement.substring(0, 100)}...`);
    
    // Test du générateur d'hypothèses
    console.log("\n🔮 2. TEST GÉNÉRATEUR D'HYPOTHÈSES:");
    const hypothesisContext = {
      userInput: testCase.userInput,
      domain,
      keywords,
      stepType: 'analyse' as const
    };
    const hypotheses = hypothesisGeneratorService.generateHypotheses(hypothesisContext);
    
    console.log(`   Hypothèse principale: ${hypotheses.primaryHypothesis.scenario}`);
    console.log(`   Probabilité: ${hypotheses.primaryHypothesis.probability}`);
    console.log(`   Alternatives: ${hypotheses.alternativeHypotheses.length}`);
    console.log(`   Confiance: ${hypotheses.confidenceLevel}%`);
    
    // Test de décomposition des problèmes
    console.log("\n🔍 3. TEST DÉCOMPOSITION DES PROBLÈMES:");
    const decomposition = problemDecompositionService.decomposeProblem(testCase.userInput, domain, keywords);
    
    console.log(`   Entités identifiées: ${decomposition.entities.map(e => e.name).join(', ')}`);
    console.log(`   Objectif principal: ${decomposition.objectives.primary}`);
    console.log(`   Objectifs secondaires: ${decomposition.objectives.secondary.length}`);
    console.log(`   Risques identifiés: ${decomposition.objectives.risks.length}`);
    
    // Test du formateur de plan d'action
    console.log("\n🎯 4. TEST FORMATEUR DE PLAN D'ACTION:");
    const step = WORKFLOW_STEPS[testCase.stepIndex];
    const actionPlan = actionPlanFormatterService.formatExpertConsultation(
      testCase.userInput,
      domain,
      step.title,
      { hypotheses, decomposition }
    );
    
    console.log(`   Droits confirmés: ${actionPlan.confirmedRights.length}`);
    console.log(`   Risques évalués: ${actionPlan.riskAssessment.length}`);
    console.log(`   Étapes d'action: ${actionPlan.actionSteps.length}`);
    console.log(`   Ouverture empathique: ${actionPlan.empathyOpening.substring(0, 80)}...`);
    
    // Test du prompt système complet
    console.log("\n🤖 5. TEST PROMPT SYSTÈME EXPERT:");
    const expertPrompt = expertConsultantService.generateExpertSystemPrompt(testCase.stepIndex, testCase.userInput, step);
    
    console.log(`   Longueur du prompt: ${expertPrompt.length} caractères`);
    console.log(`   Contient "hypothèse": ${expertPrompt.toLowerCase().includes('hypothèse')}`);
    console.log(`   Contient "certitudes": ${expertPrompt.toLowerCase().includes('certitudes')}`);
    console.log(`   Contient "plan d'action": ${expertPrompt.toLowerCase().includes('plan d\'action')}`);
    
    // Test de l'analyse complète
    console.log("\n🎭 6. TEST ANALYSE COMPLÈTE:");
    const completeAnalysis = expertConsultantService.generateCompleteAnalysis(testCase.userInput, step.title);
    
    console.log(`   Longueur de l'analyse: ${completeAnalysis.length} caractères`);
    console.log(`   Structure empathique: ${completeAnalysis.includes('🤝')}`);
    console.log(`   Droits confirmés: ${completeAnalysis.includes('✅')}`);
    console.log(`   Plan d'action: ${completeAnalysis.includes('🎯')}`);
    
    console.log("\n✅ Test réussi !");
    
  } catch (error) {
    console.error(`\n❌ Erreur dans le test: ${error}`);
  }
});

console.log("\n" + "=".repeat(60));
console.log("🎉 TESTS TERMINÉS - NOUVEAU SYSTÈME EXPERT-CONSEIL OPÉRATIONNEL !");
console.log("\nTransformation réussie :");
console.log("✅ Principe N°1 - L'Hypothèse Prime sur la Question");
console.log("✅ Principe N°2 - Dissocier et Hiérarchiser");
console.log("✅ Principe N°3 - Mobiliser la Connaissance Fondamentale");
console.log("✅ Principe N°4 - Structurer comme un Plan d'Action");
console.log("✅ Principe N°5 - Comportement Humain Naturel");
console.log("\n🚀 Rooney est maintenant un véritable EXPERT-CONSEIL PROACTIF !");
