import React, { useState } from 'react';

interface UserGuidanceProps {
  currentStep: number;
  totalSteps: number;
  isProcessing: boolean;
}

const STEP_GUIDANCE = {
  0: {
    title: "🎯 Définition du Problème",
    tips: [
      "✍️ Décrivez votre défi avec précision et détails",
      "🏢 Mentionnez votre contexte d'entreprise et contraintes",
      "🎯 Précisez vos objectifs et résultats attendus",
      "💬 Roony va vous poser des questions pour mieux comprendre"
    ]
  },
  1: {
    title: "🔍 Analyse Approfondie",
    tips: [
      "🤖 Roony analyse votre problème sous tous les angles",
      "🔑 Il identifie les enjeux clés et opportunités",
      "📝 Préparez-vous à préciser certains points importants",
      "💡 Vos réponses enrichissent l'analyse"
    ]
  },
  2: {
    title: "💡 Génération d'Idées",
    tips: [
      "🌟 Roony va proposer plusieurs approches créatives",
      "👍 Exprimez vos préférences et réactions",
      "❓ N'hésitez pas à demander des clarifications",
      "🔄 Vous pouvez orienter la direction des solutions"
    ]
  },
  3: {
    title: "⚖️ Évaluation des Solutions",
    tips: [
      "📊 Les solutions sont comparées et évaluées",
      "⭐ Vous pouvez influencer les critères d'évaluation",
      "🎯 Exprimez clairement vos priorités et contraintes",
      "✅ Validez les approches qui vous conviennent"
    ]
  },
  default: {
    title: "🚀 Progression du Workflow",
    tips: [
      "💪 Restez actif et engagé dans le processus",
      "🎯 Vos retours améliorent la solution finale",
      "❓ Posez toutes vos questions à Roony",
      "🤝 Collaborez activement pour le meilleur résultat"
    ]
  }
};

export const UserGuidance: React.FC<UserGuidanceProps> = ({
  currentStep,
  totalSteps,
  isProcessing
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const guidance = STEP_GUIDANCE[currentStep as keyof typeof STEP_GUIDANCE] || STEP_GUIDANCE.default;

  return (
    <div className="bg-gradient-to-r from-blue-900/20 to-indigo-900/20 rounded-lg border border-blue-500/30 overflow-hidden">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full p-2 flex items-center justify-between hover:bg-blue-900/10 transition-colors"
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500/30 to-indigo-500/30 rounded-full flex items-center justify-center border border-blue-400/30">
            <svg className="w-4 h-4 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="text-left flex-1">
            <div className="text-sm font-semibold text-blue-200">🤖 Guide Roony</div>
            <div className="text-xs text-blue-300">{guidance.title}</div>
          </div>
          <div className="text-xs text-blue-400/70 hidden sm:block">
            Cliquez pour voir les conseils
          </div>
        </div>

        <svg
          className={`w-3 h-3 text-blue-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isExpanded && (
        <div className="px-4 pb-4 border-t border-blue-500/20">
          <div className="bg-gradient-to-br from-blue-900/20 to-indigo-900/20 rounded-lg p-4 mt-3 border border-blue-500/30">
            <div className="mb-3">
              <h4 className="text-sm font-semibold text-blue-200 mb-2">💡 Conseils pour cette étape :</h4>
            </div>
            <ul className="space-y-2">
              {guidance.tips.map((tip, index) => (
                <li key={index} className="text-sm text-blue-100 flex items-start gap-3 p-2 bg-blue-800/20 rounded-md border border-blue-600/20">
                  <span className="text-blue-400 mt-0.5 text-sm flex-shrink-0">{tip.split(' ')[0]}</span>
                  <span className="flex-1">{tip.substring(tip.indexOf(' ') + 1)}</span>
                </li>
              ))}
            </ul>

            {isProcessing && (
              <div className="mt-4 pt-3 border-t border-blue-500/30">
                <div className="flex items-center gap-2 text-sm text-blue-200 bg-blue-800/30 p-3 rounded-lg">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                  <span className="font-medium">🤖 Roony traite votre demande...</span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
