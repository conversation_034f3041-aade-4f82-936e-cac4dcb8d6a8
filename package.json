{"name": "roony-studio-agent<PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "gen-deps": "node .Dev/Code_Base-Index/generate_dependency_map.cjs", "memory-server": "node memory-server.js", "test-memory": "node test-memory-system.js", "dev-with-memory": "concurrently \"npm run memory-server\" \"npm run dev\"", "setup-memory": "python indexer_memoire.py && npm run test-memory"}, "dependencies": {"@google/genai": "^1.15.0", "cors": "^2.8.5", "express": "^5.1.0", "gsap": "^3.13.0", "lucide-react": "^0.542.0", "node-fetch": "^3.3.2", "react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.1.11", "@types/react-dom": "^19.1.8", "@typescript-eslint/eslint-plugin": "^8.41.0", "@typescript-eslint/parser": "^8.41.0", "concurrently": "^9.2.1", "eslint": "^9.34.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "typescript": "~5.8.2", "vite": "^6.2.0"}}