#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Complet du Système de Mémoire Persistante de Roony
=======================================================

Ce script teste l'intégration complète du système de mémoire
persistante avec plusieurs scénarios réalistes.
"""

import sys
import json
import logging
from pathlib import Path

# Ajout du chemin pour l'import des modules
sys.path.append('.')

from recherche_memoire import MemoireSearcher

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_scenario_complet():
    """
    Test complet avec plusieurs scénarios réalistes
    """
    logger.info("🧪 DÉBUT DU TEST COMPLET - MÉMOIRE PERSISTANTE ROONY")
    logger.info("=" * 60)
    
    try:
        # Initialisation du searcher
        searcher = MemoireSearcher()
        
        # Scénarios de test
        scenarios = [
            {
                "nom": "Optimisation de Performance",
                "question": "Mon application web a des problèmes de performance, comment puis-je l'optimiser ?",
                "attendu": "Devrait trouver le cas d'optimisation workflow"
            },
            {
                "nom": "Intégration Système Legacy", 
                "question": "Je dois intégrer de l'IA dans un vieux système COBOL bancaire",
                "attendu": "Devrait trouver le cas d'intégration AI legacy"
            },
            {
                "nom": "Architecture Agentique",
                "question": "Comment construire une architecture agentique locale performante ?",
                "attendu": "Devrait trouver le cas d'architecture agentique"
            },
            {
                "nom": "Question Sans Correspondance",
                "question": "Comment faire cuire des pâtes parfaitement ?",
                "attendu": "Devrait ne rien trouver ou trouver des correspondances faibles"
            }
        ]
        
        resultats_globaux = []
        
        for i, scenario in enumerate(scenarios, 1):
            logger.info(f"\\n🎯 SCÉNARIO {i}: {scenario['nom']}")
            logger.info(f"Question: {scenario['question']}")
            logger.info(f"Attendu: {scenario['attendu']}")
            logger.info("-" * 40)
            
            # Recherche avec détails
            resultats = searcher.rechercher_avec_details(scenario['question'], 2)
            
            # Analyse des résultats
            if resultats['nombre_resultats'] > 0:
                logger.info(f"✅ {resultats['nombre_resultats']} cas trouvés:")
                
                for j, cas in enumerate(resultats['cas_details'], 1):
                    logger.info(f"   {j}. {cas['id']}: {cas['problem_summary'][:80]}...")
                    
                    # Affichage des métriques de pertinence
                    if 'resultats_mesures' in cas:
                        satisfaction = cas['resultats_mesures'].get('satisfaction_utilisateur', 0)
                        logger.info(f"      💯 Satisfaction: {satisfaction}%")
                
                # Génération du contexte formaté
                contexte = searcher.formater_contexte_memoire(resultats['cas_details'])
                logger.info(f"📄 Contexte généré: {len(contexte)} caractères")
                
                # Sauvegarde du résultat pour analyse
                resultats_globaux.append({
                    "scenario": scenario['nom'],
                    "question": scenario['question'],
                    "nombre_resultats": resultats['nombre_resultats'],
                    "cas_trouves": [cas['id'] for cas in resultats['cas_details']],
                    "contexte_longueur": len(contexte),
                    "succes": True
                })
                
            else:
                logger.info("ℹ️ Aucun cas pertinent trouvé")
                resultats_globaux.append({
                    "scenario": scenario['nom'],
                    "question": scenario['question'],
                    "nombre_resultats": 0,
                    "cas_trouves": [],
                    "contexte_longueur": 0,
                    "succes": True
                })
        
        # Rapport final
        logger.info("\\n" + "=" * 60)
        logger.info("📊 RAPPORT FINAL DU TEST")
        logger.info("=" * 60)
        
        scenarios_reussis = len([r for r in resultats_globaux if r['succes']])
        total_cas_trouves = sum(r['nombre_resultats'] for r in resultats_globaux)
        
        logger.info(f"✅ Scénarios testés avec succès: {scenarios_reussis}/{len(scenarios)}")
        logger.info(f"🔍 Total cas trouvés: {total_cas_trouves}")
        
        # Analyse de pertinence
        scenarios_avec_resultats = [r for r in resultats_globaux if r['nombre_resultats'] > 0]
        logger.info(f"📈 Taux de réponse: {len(scenarios_avec_resultats)}/{len(scenarios)} ({len(scenarios_avec_resultats)/len(scenarios)*100:.1f}%)")
        
        # Sauvegarde du rapport
        rapport_path = Path('test_memoire_rapport.json')
        with open(rapport_path, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': '2025-08-28T22:00:00Z',
                'scenarios_testes': len(scenarios),
                'scenarios_reussis': scenarios_reussis,
                'total_cas_trouves': total_cas_trouves,
                'taux_reponse': len(scenarios_avec_resultats)/len(scenarios)*100,
                'details': resultats_globaux
            }, f, ensure_ascii=False, indent=2)
        
        logger.info(f"💾 Rapport sauvegardé: {rapport_path}")
        
        if scenarios_reussis == len(scenarios):
            logger.info("🎉 TOUS LES TESTS RÉUSSIS - SYSTÈME MÉMOIRE OPÉRATIONNEL!")
            return True
        else:
            logger.warning(f"⚠️ {len(scenarios) - scenarios_reussis} test(s) échoué(s)")
            return False
            
    except Exception as e:
        logger.error(f"💥 ERREUR CRITIQUE: {e}")
        return False

def test_injection_contexte():
    """
    Test spécifique de l'injection de contexte
    """
    logger.info("\\n🎭 TEST D'INJECTION DE CONTEXTE")
    logger.info("-" * 40)
    
    try:
        searcher = MemoireSearcher()
        
        question_test = "J'ai besoin d'optimiser les performances de mon système"
        
        # Recherche complète pour injection
        contexte = searcher.recherche_complete_pour_injection(question_test, 2)
        
        if contexte:
            logger.info("✅ Contexte d'injection généré avec succès")
            logger.info(f"📏 Longueur: {len(contexte)} caractères")
            
            # Simulation d'injection dans un prompt
            prompt_base = "Tu es Roony, un expert en résolution de problèmes complexes."
            prompt_enrichi = f"{contexte}\\n\\n{prompt_base}\\n\\nNouvelle question: {question_test}"
            
            logger.info(f"🎯 Prompt final: {len(prompt_enrichi)} caractères")
            logger.info("✅ Injection de contexte testée avec succès")
            
            return True
        else:
            logger.info("ℹ️ Aucun contexte généré (normal si pas de cas pertinents)")
            return True
            
    except Exception as e:
        logger.error(f"💥 Erreur test injection: {e}")
        return False

def test_statistiques_memoire():
    """
    Test des statistiques de la mémoire
    """
    logger.info("\\n📊 TEST DES STATISTIQUES MÉMOIRE")
    logger.info("-" * 40)
    
    try:
        searcher = MemoireSearcher()
        
        # Statistiques de la collection
        info_collection = searcher.collection.count()
        logger.info(f"📈 Nombre de cas indexés: {info_collection}")
        
        # Test de quelques requêtes pour voir la distribution
        requetes_test = [
            "performance",
            "optimisation", 
            "intégration",
            "architecture",
            "système"
        ]
        
        for requete in requetes_test:
            resultats = searcher.chercher_cas_similaires(requete, 1)
            logger.info(f"🔍 '{requete}': {len(resultats)} résultat(s)")
        
        logger.info("✅ Statistiques mémoire validées")
        return True
        
    except Exception as e:
        logger.error(f"💥 Erreur statistiques: {e}")
        return False

def main():
    """
    Point d'entrée principal des tests
    """
    logger.info("🚀 SUITE DE TESTS COMPLÈTE - MÉMOIRE PERSISTANTE ROONY")
    logger.info("Version 1.0 - Intégration critique")
    logger.info("=" * 70)
    
    tests_reussis = 0
    tests_total = 3
    
    # Test 1: Scénarios complets
    if test_scenario_complet():
        tests_reussis += 1
        logger.info("✅ Test 1/3 RÉUSSI")
    else:
        logger.error("❌ Test 1/3 ÉCHOUÉ")
    
    # Test 2: Injection de contexte
    if test_injection_contexte():
        tests_reussis += 1
        logger.info("✅ Test 2/3 RÉUSSI")
    else:
        logger.error("❌ Test 2/3 ÉCHOUÉ")
    
    # Test 3: Statistiques
    if test_statistiques_memoire():
        tests_reussis += 1
        logger.info("✅ Test 3/3 RÉUSSI")
    else:
        logger.error("❌ Test 3/3 ÉCHOUÉ")
    
    # Résultat final
    logger.info("\\n" + "=" * 70)
    logger.info("🏁 RÉSULTAT FINAL DES TESTS")
    logger.info("=" * 70)
    logger.info(f"Tests réussis: {tests_reussis}/{tests_total}")
    
    if tests_reussis == tests_total:
        logger.info("🎉 SUCCÈS TOTAL - SYSTÈME MÉMOIRE PERSISTANTE OPÉRATIONNEL!")
        logger.info("🚀 Roony est maintenant équipé d'une mémoire persistante fonctionnelle!")
        sys.exit(0)
    else:
        logger.error(f"⚠️ {tests_total - tests_reussis} test(s) échoué(s)")
        logger.error("🔧 Vérifiez les logs ci-dessus pour diagnostiquer les problèmes")
        sys.exit(1)

if __name__ == "__main__":
    main()
