/**
 * Script de test pour vérifier les corrections du mode Premium
 * À exécuter dans la console du navigateur après le chargement de l'application
 */

console.log('🧪 === TEST DES CORRECTIONS MODE PREMIUM ===\n');

// Test 1: Vérifier que les services sont disponibles
console.log('📦 Test 1: Vérification des services...');
try {
  if (typeof window.premiumDebug !== 'undefined') {
    console.log('✅ Premium Debug Helper: Disponible');
  } else {
    console.log('❌ Premium Debug Helper: Non disponible');
  }
} catch (error) {
  console.log('❌ Erreur lors du test des services:', error);
}

// Test 2: Lancer le diagnostic automatique
console.log('\n🔍 Test 2: Diagnostic automatique...');
if (typeof window.premiumDebug !== 'undefined') {
  window.premiumDebug.runFullDiagnostic()
    .then(() => {
      console.log('✅ Diagnostic terminé avec succès');
    })
    .catch(error => {
      console.log('❌ Erreur lors du diagnostic:', error);
    });
} else {
  console.log('⚠️ Impossible de lancer le diagnostic - Helper non disponible');
}

// Test 3: Vérifier les logs d'erreur 429
console.log('\n📊 Test 3: Vérification des logs récents...');
const logs = [];
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

// Intercepter les logs pour détecter les erreurs 429
let error429Count = 0;
console.warn = function(...args) {
  const message = args.join(' ');
  if (message.includes('429') || message.includes('Rate Limit')) {
    error429Count++;
    console.log(`🚨 Erreur 429 détectée: ${message}`);
  }
  originalConsoleWarn.apply(console, args);
};

// Restaurer après 10 secondes
setTimeout(() => {
  console.log = originalConsoleLog;
  console.warn = originalConsoleWarn;
  console.error = originalConsoleError;
  
  console.log(`\n📈 Résumé des tests (10 secondes):`);
  console.log(`   🚨 Erreurs 429 détectées: ${error429Count}`);
  
  if (error429Count === 0) {
    console.log('   ✅ Aucune erreur 429 détectée - Corrections efficaces !');
  } else {
    console.log('   ⚠️ Des erreurs 429 persistent - Vérifiez les logs ci-dessus');
  }
  
  console.log('\n🎯 Instructions pour tester le mode Premium:');
  console.log('1. Cliquez sur le bouton mode Premium dans le header');
  console.log('2. Entrez votre clé API OpenRouter');
  console.log('3. Vérifiez que l\'authentification réussit');
  console.log('4. Testez l\'envoi d\'un message en mode Premium');
  console.log('\n💡 Commandes utiles:');
  console.log('   window.premiumDebug.showDebugInfo() - Voir toutes les commandes');
  console.log('   window.premiumDebug.runFullDiagnostic() - Diagnostic complet');
  console.log('   window.premiumDebug.resetPremiumAuth() - Réinitialiser l\'auth');
  
}, 10000);

console.log('⏱️ Surveillance des erreurs 429 pendant 10 secondes...');
console.log('💡 Pendant ce temps, vous pouvez tester le mode Premium manuellement');
