/**
 * Service d'authentification et de gestion du mode Premium OpenRouter
 * Gère la connexion utilisateur, la validation des crédits et l'accès aux modèles premium
 */

import { PREMIUM_CONFIG, PREMIUM_MODELS_BY_TASK } from '../constants';
import type { PremiumUser, PremiumModel, OpenRouterResponse, AuthenticationState } from '../types';

interface OpenRouterCredits {
  credits: number;
  usage: number;
}

class PremiumAuthService {
  private currentUser: PremiumUser = {
    isAuthenticated: false,
    plan: 'free'
  };
  
  private availableModels: PremiumModel[] = [];
  private authListeners: ((auth: AuthenticationState) => void)[] = [];

  constructor() {
    this.loadAuthFromStorage();
  }

  /**
   * Charge l'authentification depuis le localStorage
   */
  private loadAuthFromStorage(): void {
    try {
      const stored = localStorage.getItem(PREMIUM_CONFIG.AUTH_STORAGE_KEY);
      if (stored) {
        const authData = JSON.parse(stored);
        this.currentUser = authData;
        
        // Valider la clé API au démarrage si elle existe
        if (this.currentUser.apiKey) {
          this.validateApiKey(this.currentUser.apiKey);
        }
      }
    } catch (error) {
      console.warn('⚠️ Erreur lors du chargement de l\'authentification Premium:', error);
    }
  }

  /**
   * Sauvegarde l'authentification dans le localStorage
   */
  private saveAuthToStorage(): void {
    try {
      localStorage.setItem(PREMIUM_CONFIG.AUTH_STORAGE_KEY, JSON.stringify(this.currentUser));
    } catch (error) {
      console.warn('⚠️ Erreur lors de la sauvegarde de l\'authentification Premium:', error);
    }
  }

  /**
   * Notifie tous les listeners des changements d'authentification
   */
  private notifyAuthChange(isLoading: boolean = false, error?: string): void {
    const authState: AuthenticationState = {
      user: { ...this.currentUser },
      isLoading,
      error
    };
    
    this.authListeners.forEach(listener => listener(authState));
  }

  /**
   * Ajoute un listener pour les changements d'authentification
   */
  public onAuthChange(listener: (auth: AuthenticationState) => void): () => void {
    this.authListeners.push(listener);
    
    // Envoyer l'état actuel immédiatement
    this.notifyAuthChange();
    
    // Retourner une fonction pour supprimer le listener
    return () => {
      const index = this.authListeners.indexOf(listener);
      if (index > -1) {
        this.authListeners.splice(index, 1);
      }
    };
  }

  /**
   * Authentifie un utilisateur avec sa clé API OpenRouter
   */
  public async authenticateUser(apiKey: string): Promise<boolean> {
    console.log('🔐 Début de l\'authentification Premium...');
    this.notifyAuthChange(true);

    try {
      // Valider la clé API avec méthodes multiples
      const isValid = await this.validateApiKey(apiKey);

      if (isValid) {
        console.log('✅ Clé API validée, chargement des données utilisateur...');

        this.currentUser = {
          isAuthenticated: true,
          apiKey: apiKey,
          plan: 'premium'
        };

        // Charger les crédits et modèles disponibles en parallèle
        try {
          await Promise.all([
            this.loadUserCredits(),
            this.loadAvailableModels()
          ]);

          console.log('📦 Données utilisateur chargées avec succès');
        } catch (dataError) {
          console.warn('⚠️ Erreur lors du chargement des données, mais authentification réussie:', dataError);
          // Continuer même si le chargement des données échoue
        }

        this.saveAuthToStorage();
        this.notifyAuthChange(false);

        console.log('✅ Authentification Premium réussie');
        return true;
      } else {
        console.error('❌ Validation de la clé API échouée');
        this.notifyAuthChange(false, 'Clé API invalide ou problème de connexion. Vérifiez votre clé et réessayez.');
        return false;
      }
    } catch (error) {
      console.error('❌ Erreur lors de l\'authentification Premium:', error);
      const errorMessage = error instanceof Error ? error.message : 'Erreur de connexion inconnue';
      this.notifyAuthChange(false, `Erreur d'authentification: ${errorMessage}`);
      return false;
    }
  }

  /**
   * Valide une clé API OpenRouter avec retry et validation alternative
   */
  private async validateApiKey(apiKey: string): Promise<boolean> {
    console.log('🔍 Validation de la clé API Premium...');

    // Méthode 1: Validation via endpoint /auth/key avec retry
    const authValidation = await this.validateViaAuthEndpoint(apiKey);
    if (authValidation.success) {
      console.log('✅ Clé API validée via endpoint /auth/key');
      return true;
    }

    // Méthode 2: Validation alternative via test de requête simple
    if (authValidation.shouldTryAlternative) {
      console.log('🔄 Tentative de validation alternative...');
      const alternativeValidation = await this.validateViaTestRequest(apiKey);
      if (alternativeValidation) {
        console.log('✅ Clé API validée via requête de test');
        return true;
      }
    }

    console.error('❌ Échec de validation de la clé API:', authValidation.error);
    return false;
  }

  /**
   * Validation via l'endpoint officiel /auth/key avec retry
   */
  private async validateViaAuthEndpoint(apiKey: string): Promise<{
    success: boolean;
    shouldTryAlternative: boolean;
    error?: string;
  }> {
    const maxRetries = 3;
    const baseDelay = 1000; // 1 seconde

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔍 Tentative ${attempt}/${maxRetries} de validation via /auth/key`);

        const response = await fetch('https://openrouter.ai/api/v1/auth/key', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          return { success: true, shouldTryAlternative: false };
        }

        // Gestion des erreurs spécifiques
        if (response.status === 401 || response.status === 403) {
          // Clé invalide - pas besoin de retry
          return {
            success: false,
            shouldTryAlternative: false,
            error: `Clé API invalide (${response.status})`
          };
        }

        if (response.status === 429) {
          // Rate limit - essayer la méthode alternative
          console.warn(`⏳ Rate limit détecté (429) - tentative ${attempt}/${maxRetries}`);
          if (attempt < maxRetries) {
            const delay = baseDelay * Math.pow(2, attempt - 1); // Backoff exponentiel
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          } else {
            return {
              success: false,
              shouldTryAlternative: true,
              error: 'Rate limit sur endpoint /auth/key'
            };
          }
        }

        // Autres erreurs serveur - essayer la méthode alternative
        return {
          success: false,
          shouldTryAlternative: true,
          error: `Erreur serveur (${response.status})`
        };

      } catch (error) {
        console.warn(`⚠️ Erreur réseau tentative ${attempt}/${maxRetries}:`, error);
        if (attempt < maxRetries) {
          const delay = baseDelay * Math.pow(2, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          return {
            success: false,
            shouldTryAlternative: true,
            error: `Erreur réseau: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
          };
        }
      }
    }

    return { success: false, shouldTryAlternative: true, error: 'Échec après tous les retries' };
  }

  /**
   * Validation alternative via une requête de test simple
   */
  private async validateViaTestRequest(apiKey: string): Promise<boolean> {
    try {
      console.log('🧪 Test de validation avec requête simple...');

      const testMessages = [
        { role: 'user', content: 'Test' }
      ];

      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Studio Agentique Roony - Validation Premium'
        },
        body: JSON.stringify({
          model: 'openai/gpt-3.5-turbo', // Modèle simple et fiable
          messages: testMessages,
          temperature: 0.1,
          max_tokens: 10
        })
      });

      // Même une erreur 400 peut indiquer que la clé est valide (problème de modèle/format)
      // Seules les erreurs 401/403 indiquent une clé invalide
      if (response.ok || (response.status !== 401 && response.status !== 403)) {
        return true;
      }

      console.warn(`❌ Validation alternative échouée: ${response.status}`);
      return false;

    } catch (error) {
      console.warn('❌ Erreur lors de la validation alternative:', error);
      return false;
    }
  }

  /**
   * Charge les crédits de l'utilisateur avec retry en cas d'erreur 429
   */
  private async loadUserCredits(): Promise<void> {
    if (!this.currentUser.apiKey) return;

    const maxRetries = 2;
    const baseDelay = 2000; // 2 secondes

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`💳 Chargement des crédits (tentative ${attempt}/${maxRetries})...`);

        const response = await fetch('https://openrouter.ai/api/v1/auth/key', {
          headers: {
            'Authorization': `Bearer ${this.currentUser.apiKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json() as OpenRouterCredits;
          this.currentUser.credits = data.credits;
          this.saveAuthToStorage();
          console.log(`✅ Crédits chargés: ${data.credits}$`);
          return;
        } else if (response.status === 429 && attempt < maxRetries) {
          console.warn(`⏳ Rate limit sur chargement crédits - tentative ${attempt}/${maxRetries}`);
          const delay = baseDelay * attempt;
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        } else {
          console.warn(`⚠️ Impossible de charger les crédits: ${response.status}`);
          // Définir des crédits par défaut pour éviter de bloquer l'authentification
          this.currentUser.credits = 0;
          return;
        }
      } catch (error) {
        console.warn(`⚠️ Erreur lors du chargement des crédits (tentative ${attempt}):`, error);
        if (attempt === maxRetries) {
          // Définir des crédits par défaut en cas d'échec complet
          this.currentUser.credits = 0;
        } else {
          const delay = baseDelay * attempt;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
  }

  /**
   * Charge les modèles disponibles dans la gamme de prix Premium
   */
  private async loadAvailableModels(): Promise<void> {
    try {
      // Vérifier le cache d'abord
      const cachedModels = this.getCachedModels();
      if (cachedModels) {
        this.availableModels = cachedModels;
        this.currentUser.models = cachedModels.map(m => m.id);
        console.log(`📦 ${cachedModels.length} modèles Premium chargés depuis le cache`);
        return;
      }

      // Charger depuis l'API
      const response = await fetch(PREMIUM_CONFIG.MODELS_API_URL, {
        headers: {
          'Authorization': `Bearer ${this.currentUser.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json() as OpenRouterResponse;
        
        if (!data.models || !Array.isArray(data.models)) {
          console.warn('⚠️ Réponse API malformée pour les modèles');
          return;
        }
        
        // Filtrer les modèles dans la gamme de prix Premium
        this.availableModels = data.models.filter(model => {
          try {
            const promptPrice = parseFloat(String(model.pricing.prompt || '0')) * 1000000; // Convertir en prix par million de tokens
            const completionPrice = parseFloat(String(model.pricing.completion || '0')) * 1000000;
            const maxPrice = Math.max(promptPrice, completionPrice);
            
            return maxPrice >= PREMIUM_CONFIG.PRICE_FILTER.MIN_PRICE && 
                   maxPrice <= PREMIUM_CONFIG.PRICE_FILTER.MAX_PRICE;
          } catch (error) {
            console.warn(`⚠️ Erreur lors du filtrage du modèle ${model.id}:`, error);
            return false;
          }
        });

        // Mettre en cache
        this.cacheModels(this.availableModels);
        this.currentUser.models = this.availableModels.map(m => m.id);
        this.saveAuthToStorage();

        console.log(`📦 ${this.availableModels.length} modèles Premium chargés depuis l'API`);
      } else {
        console.warn('⚠️ Échec du chargement des modèles Premium - Statut:', response.status);
      }
    } catch (error) {
      console.warn('⚠️ Impossible de charger les modèles Premium:', error);
    }
  }

  /**
   * Met en cache les modèles
   */
  private cacheModels(models: PremiumModel[]): void {
    try {
      const cacheData = {
        models,
        timestamp: Date.now()
      };
      localStorage.setItem(PREMIUM_CONFIG.MODELS_CACHE_KEY, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('⚠️ Erreur lors de la mise en cache des modèles:', error);
    }
  }

  /**
   * Récupère les modèles depuis le cache
   */
  private getCachedModels(): PremiumModel[] | null {
    try {
      const cached = localStorage.getItem(PREMIUM_CONFIG.MODELS_CACHE_KEY);
      if (!cached) return null;

      const cacheData = JSON.parse(cached);
      const now = Date.now();
      
      // Vérifier si le cache n'est pas expiré
      if (now - cacheData.timestamp < PREMIUM_CONFIG.MODELS_CACHE_DURATION) {
        return cacheData.models;
      }
    } catch (error) {
      console.warn('⚠️ Erreur lors de la lecture du cache des modèles:', error);
    }
    
    return null;
  }

  /**
   * Déconnecte l'utilisateur Premium
   */
  public logout(): void {
    this.currentUser = {
      isAuthenticated: false,
      plan: 'free'
    };
    
    this.availableModels = [];
    
    try {
      localStorage.removeItem(PREMIUM_CONFIG.AUTH_STORAGE_KEY);
      localStorage.removeItem(PREMIUM_CONFIG.MODELS_CACHE_KEY);
    } catch (error) {
      console.warn('⚠️ Erreur lors de la déconnexion:', error);
    }
    
    this.notifyAuthChange();
    console.log('👋 Déconnexion Premium effectuée');
  }

  /**
   * Retourne l'état actuel de l'authentification
   */
  public getCurrentAuth(): AuthenticationState {
    return {
      user: { ...this.currentUser },
      isLoading: false
    };
  }

  /**
   * Retourne les modèles disponibles pour une tâche donnée
   */
  public getModelsForTask(task: string): string[] {
    if (!this.currentUser.isAuthenticated) {
      return [];
    }

    const recommendedModels = PREMIUM_MODELS_BY_TASK[task as keyof typeof PREMIUM_MODELS_BY_TASK] || [];
    
    // Filtrer par les modèles réellement disponibles
    return recommendedModels.filter(modelId => 
      this.availableModels.some(available => available.id === modelId)
    );
  }

  /**
   * Retourne tous les modèles Premium disponibles
   */
  public getAvailableModels(): PremiumModel[] {
    return [...this.availableModels];
  }

  /**
   * Vérifie si les crédits sont suffisants
   */
  public hasEnoughCredits(): boolean {
    if (!this.currentUser.credits) return true; // Inconnu, on assume que oui
    return this.currentUser.credits > PREMIUM_CONFIG.LOW_CREDITS_THRESHOLD;
  }

  /**
   * Retourne le statut des crédits
   */
  public getCreditsStatus(): { credits: number; isLow: boolean } {
    const credits = this.currentUser.credits || 0;
    return {
      credits,
      isLow: credits <= PREMIUM_CONFIG.LOW_CREDITS_THRESHOLD
    };
  }

  /**
   * Diagnostic complet du mode Premium pour debug
   */
  public async runPremiumDiagnostic(): Promise<{
    isAuthenticated: boolean;
    hasApiKey: boolean;
    creditsStatus: { credits: number; isLow: boolean };
    modelsCount: number;
    apiKeyValidation: { success: boolean; method?: string; error?: string };
    recommendations: string[];
  }> {
    console.log('🔍 === DIAGNOSTIC MODE PREMIUM ===');

    const diagnostic = {
      isAuthenticated: this.currentUser.isAuthenticated,
      hasApiKey: !!this.currentUser.apiKey,
      creditsStatus: this.getCreditsStatus(),
      modelsCount: this.availableModels.length,
      apiKeyValidation: { success: false } as { success: boolean; method?: string; error?: string },
      recommendations: [] as string[]
    };

    // Test de validation de clé API si disponible
    if (this.currentUser.apiKey) {
      try {
        const isValid = await this.validateApiKey(this.currentUser.apiKey);
        diagnostic.apiKeyValidation = {
          success: isValid,
          method: 'Validation complète avec retry'
        };
      } catch (error) {
        diagnostic.apiKeyValidation = {
          success: false,
          error: error instanceof Error ? error.message : 'Erreur inconnue'
        };
      }
    }

    // Générer des recommandations
    if (!diagnostic.isAuthenticated) {
      diagnostic.recommendations.push('🔸 Connectez-vous avec votre clé API OpenRouter');
    }

    if (!diagnostic.hasApiKey) {
      diagnostic.recommendations.push('🔸 Obtenez une clé API sur https://openrouter.ai');
    }

    if (diagnostic.creditsStatus.isLow) {
      diagnostic.recommendations.push('🔸 Rechargez vos crédits sur https://openrouter.ai/credits');
    }

    if (diagnostic.modelsCount === 0) {
      diagnostic.recommendations.push('🔸 Vérifiez la connexion API - aucun modèle Premium disponible');
    }

    if (!diagnostic.apiKeyValidation.success && diagnostic.hasApiKey) {
      diagnostic.recommendations.push('🔸 Vérifiez la validité de votre clé API');
    }

    console.log('📊 Diagnostic Premium:', diagnostic);
    return diagnostic;
  }
}

// Instance singleton
export const premiumAuthService = new PremiumAuthService();
