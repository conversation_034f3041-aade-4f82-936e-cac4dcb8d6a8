/**
 * Service de Formatage en Plan d'Action
 * Implémente le Principe N°4 : "Structurer comme un Plan d'Action"
 * Implémente le Principe N°5 : "Le Filtre de Faisabilité Opérationnelle"
 * 
 * Structure chaque réponse comme une consultation d'expert avec :
 * Résumé → Analyse des risques → Confirmation des droits → Étapes chronologiques
 * + Validation critique de faisabilité opérationnelle sur chaque plan
 */

import { operationalFeasibilityFilter, type ActionToValidate, type FeasibilityCheckResult } from './operationalFeasibilityFilter';

export interface ActionStep {
  order: number;
  title: string;
  description: string;
  timeline: string;
  priority: 'URGENT' | 'IMPORTANT' | 'NORMAL';
  resources: string[];
  expectedOutcome: string;
}

export interface RiskAssessment {
  risk: string;
  probability: 'HIGH' | 'MEDIUM' | 'LOW';
  impact: 'HIGH' | 'MEDIUM' | 'LOW';
  mitigation: string;
}

export interface RightsConfirmation {
  right: string;
  certaintyLevel: 'ABSOLUTE' | 'HIGH' | 'CONTEXTUAL';
  legalBasis: string;
  practicalImplication: string;
}

export interface ActionPlan {
  empathyOpening: string;
  situationSummary: string;
  confirmedRights: RightsConfirmation[];
  riskAssessment: RiskAssessment[];
  actionSteps: ActionStep[];
  encouragingClosing: string;
  nextMilestone: string;
  feasibilityValidation?: {
    isValidated: boolean;
    confidence: 'HIGH' | 'MEDIUM' | 'LOW';
    issues: number;
    report?: string;
  };
}

class ActionPlanFormatterService {
  
  /**
   * Formate une réponse complète selon la structure consultation d'expert
   * AVEC application du Principe 5 - Filtre de Faisabilité Opérationnelle
   */
  formatExpertConsultation(
    userInput: string,
    domain: string,
    stepTitle: string,
    analysis: any
  ): ActionPlan {
    
    // Génération initiale du plan
    const initialActionSteps = this.generateActionSteps(analysis, domain, stepTitle);
    
    // Application du Filtre de Faisabilité Opérationnelle (Principe 5)
    const validatedPlan = this.applyOperationalFeasibilityFilter(
      initialActionSteps, 
      domain, 
      userInput
    );
    
    return {
      empathyOpening: this.generateEmpathyOpening(userInput, stepTitle),
      situationSummary: this.generateSituationSummary(userInput, analysis),
      confirmedRights: this.generateRightsConfirmation(domain, userInput),
      riskAssessment: this.generateRiskAssessment(analysis, domain),
      actionSteps: validatedPlan.actionSteps,
      encouragingClosing: this.generateEncouragingClosing(stepTitle),
      nextMilestone: this.generateNextMilestone(stepTitle),
      feasibilityValidation: validatedPlan.feasibilityValidation
    };
  }

  /**
   * Génère une ouverture empathique et chaleureuse
   */
  private generateEmpathyOpening(userInput: string, stepTitle: string): string {
    const empathyPhrases = [
      "Je comprends parfaitement votre préoccupation",
      "Votre situation mérite toute notre attention",
      "C'est tout à fait normal de se poser ces questions",
      "Vous avez raison de chercher des éclaircissements",
      "Permettez-moi de vous rassurer d'emblée"
    ];
    
    const contextualPhrases = [
      "concernant votre démarche administrative",
      "sur cette étape importante",
      "face à cette situation complexe",
      "dans ce contexte particulier",
      "pour cette problématique"
    ];
    
    const randomEmpathy = empathyPhrases[Math.floor(Math.random() * empathyPhrases.length)];
    const randomContext = contextualPhrases[Math.floor(Math.random() * contextualPhrases.length)];
    
    return `🤝 ${randomEmpathy} ${randomContext}. Analysons ensemble votre situation pour vous donner une vision claire et des actions concrètes.`;
  }

  /**
   * Génère un résumé structuré de la situation
   */
  private generateSituationSummary(userInput: string, analysis: any): string {
    let summary = "**📋 Résumé de votre situation :**\n\n";
    
    // Extraire les éléments clés du contexte utilisateur
    const keyElements = this.extractKeyElements(userInput);
    
    summary += "Voici ce que je retiens de votre description :\n";
    keyElements.forEach((element, index) => {
      summary += `${index + 1}. ${element}\n`;
    });
    
    if (analysis?.entities?.length > 0) {
      summary += `\nEntités administratives concernées : ${analysis.entities.map((e: any) => e.name).join(', ')}\n`;
    }
    
    return summary;
  }

  /**
   * Génère la confirmation des droits et certitudes
   */
  private generateRightsConfirmation(domain: string, userInput: string): RightsConfirmation[] {
    const rights: RightsConfirmation[] = [];
    const input = userInput.toLowerCase();
    
    // Droits spécifiques au domaine immigration
    if (domain === 'immigration') {
      if (input.includes('conjoint') || input.includes('famille')) {
        rights.push({
          right: "Droit au travail automatique pour les conjoints de citoyens UE",
          certaintyLevel: 'ABSOLUTE',
          legalBasis: "Directive européenne 2004/38/CE",
          practicalImplication: "Aucune autorisation préalable nécessaire pour exercer une activité professionnelle"
        });
      }
      
      if (input.includes('titre') || input.includes('séjour')) {
        rights.push({
          right: "Droit au maintien sur le territoire pendant l'instruction",
          certaintyLevel: 'HIGH',
          legalBasis: "Code de l'entrée et du séjour des étrangers (CESEDA)",
          practicalImplication: "Possibilité de continuer l'activité avec récépissé"
        });
      }
    }
    
    // Droits spécifiques au domaine social
    if (domain === 'social' || input.includes('urssaf')) {
      rights.push({
        right: "Présomption d'innocence en matière de cotisations sociales",
        certaintyLevel: 'ABSOLUTE',
        legalBasis: "Principe général du droit",
        practicalImplication: "Un litige contesté n'équivaut pas à une fraude avérée"
      });
      
      rights.push({
        right: "Droit de contestation et de recours",
        certaintyLevel: 'ABSOLUTE',
        legalBasis: "Code de la sécurité sociale",
        practicalImplication: "Possibilité de contester tout redressement dans les délais légaux"
      });
    }
    
    // Droits généraux
    rights.push({
      right: "Droit à l'information et à l'accompagnement administratif",
      certaintyLevel: 'HIGH',
      legalBasis: "Loi du 12 avril 2000 relative aux droits des citoyens",
      practicalImplication: "Les administrations doivent vous informer clairement de vos droits et obligations"
    });
    
    return rights;
  }

  /**
   * Génère l'évaluation des risques
   */
  private generateRiskAssessment(analysis: any, domain: string): RiskAssessment[] {
    const risks: RiskAssessment[] = [];
    
    // Risques génériques
    risks.push({
      risk: "Délais de traitement prolongés",
      probability: 'MEDIUM',
      impact: 'MEDIUM',
      mitigation: "Déposer les demandes avec une marge de sécurité et suivre régulièrement"
    });
    
    // Risques spécifiques au domaine
    if (domain === 'immigration') {
      risks.push({
        risk: "Vérifications croisées entre administrations",
        probability: 'HIGH',
        impact: 'MEDIUM',
        mitigation: "Préparer un dossier explicatif pour chaque situation complexe"
      });
    }
    
    if (domain === 'social') {
      risks.push({
        risk: "Impact du litige URSSAF sur autres démarches",
        probability: 'MEDIUM',
        impact: 'HIGH',
        mitigation: "Documenter formellement la contestation et joindre les preuves"
      });
    }
    
    // Risques basés sur l'analyse
    if (analysis?.entities?.length > 2) {
      risks.push({
        risk: "Coordination complexe entre multiples entités",
        probability: 'HIGH',
        impact: 'MEDIUM',
        mitigation: "Établir un calendrier de suivi et maintenir une communication proactive"
      });
    }
    
    return risks;
  }

  /**
   * Génère les étapes d'action chronologiques
   */
  private generateActionSteps(analysis: any, domain: string, stepTitle: string): ActionStep[] {
    const steps: ActionStep[] = [];
    
    // Étape 1 : Toujours commencer par la documentation
    steps.push({
      order: 1,
      title: "Rassemblement et organisation des documents",
      description: "Constituer un dossier complet avec tous les justificatifs nécessaires",
      timeline: "Immédiat (1-2 jours)",
      priority: 'URGENT',
      resources: ["Documents d'identité", "Justificatifs de situation", "Correspondances administratives"],
      expectedOutcome: "Dossier complet et organisé prêt pour les démarches"
    });
    
    // Étapes spécifiques au domaine
    if (domain === 'immigration') {
      steps.push({
        order: 2,
        title: "Préparation du dossier préfecture",
        description: "Constituer le dossier spécifique aux exigences préfectorales",
        timeline: "3-5 jours",
        priority: 'URGENT',
        resources: ["Formulaires officiels", "Photos d'identité", "Justificatifs spécifiques"],
        expectedOutcome: "Dossier conforme aux exigences préfectorales"
      });
    }
    
    if (domain === 'social') {
      steps.push({
        order: 2,
        title: "Formalisation de la contestation URSSAF",
        description: "Documenter officiellement la contestation du litige",
        timeline: "2-3 jours",
        priority: 'URGENT',
        resources: ["Courrier de contestation", "Preuves à l'appui", "Accusé de réception"],
        expectedOutcome: "Contestation formellement enregistrée et documentée"
      });
    }
    
    // Étape de dépôt/soumission
    steps.push({
      order: steps.length + 1,
      title: "Dépôt des demandes et suivi",
      description: "Soumettre les dossiers aux administrations concernées et organiser le suivi",
      timeline: "1 semaine",
      priority: 'IMPORTANT',
      resources: ["Dossiers complets", "Récépissés", "Calendrier de suivi"],
      expectedOutcome: "Demandes officiellement déposées avec suivi organisé"
    });
    
    // Étape de suivi et ajustement
    steps.push({
      order: steps.length + 1,
      title: "Suivi proactif et ajustements",
      description: "Maintenir le contact avec les administrations et réagir aux demandes",
      timeline: "En continu",
      priority: 'NORMAL',
      resources: ["Planning de relances", "Contacts administratifs", "Dossiers de sauvegarde"],
      expectedOutcome: "Traitement optimal des demandes avec réactivité maximale"
    });
    
    return steps;
  }

  /**
   * Applique le Filtre de Faisabilité Opérationnelle (Principe 5)
   * Challenge chaque plan d'action selon les 3 questions critiques
   */
  private applyOperationalFeasibilityFilter(
    actionSteps: ActionStep[], 
    domain: string, 
    userInput: string
  ): {
    actionSteps: ActionStep[],
    feasibilityValidation: {
      isValidated: boolean;
      confidence: 'HIGH' | 'MEDIUM' | 'LOW';
      issues: number;
      report?: string;
    }
  } {
    // Conversion des ActionStep vers ActionToValidate
    const actionsToValidate: ActionToValidate[] = actionSteps.map(step => ({
      title: step.title,
      description: step.description,
      timeline: step.timeline,
      priority: step.priority,
      order: step.order,
      context: {
        domain,
        userSituation: userInput,
        countries: this.extractCountriesFromInput(userInput),
        sectors: this.extractSectorsFromInput(userInput)
      }
    }));

    // Application du filtre de faisabilité
    const feasibilityResults = operationalFeasibilityFilter.validateActionPlan(actionsToValidate);
    
    // Amélioration des actions selon les résultats
    const improvedActionSteps: ActionStep[] = actionSteps.map((step, index) => {
      const result = feasibilityResults[index];
      
      if (result.improvedAction) {
        return {
          ...step,
          description: result.improvedAction,
          // Marquer visuellement que l'action a été améliorée
          title: result.confidence === 'LOW' ? `⚡ ${step.title} (Optimisé)` : step.title
        };
      }
      
      return step;
    });

    // Calcul des métriques de validation
    const totalIssues = feasibilityResults.reduce((sum, result) => sum + result.issues.length, 0);
    const criticalIssues = feasibilityResults.filter(r => 
      r.issues.some(i => i.severity === 'CRITICAL')
    ).length;
    
    let overallConfidence: 'HIGH' | 'MEDIUM' | 'LOW' = 'HIGH';
    if (criticalIssues > 0) {
      overallConfidence = 'LOW';
    } else if (totalIssues > 2) {
      overallConfidence = 'MEDIUM';
    }

    // Génération du rapport de faisabilité si nécessaire
    let feasibilityReport: string | undefined;
    if (totalIssues > 0) {
      feasibilityReport = operationalFeasibilityFilter.generateFeasibilityReport(actionsToValidate);
    }

    return {
      actionSteps: improvedActionSteps,
      feasibilityValidation: {
        isValidated: criticalIssues === 0,
        confidence: overallConfidence,
        issues: totalIssues,
        report: feasibilityReport
      }
    };
  }

  /**
   * Extrait les pays mentionnés dans l'input utilisateur
   */
  private extractCountriesFromInput(userInput: string): string[] {
    const countries: string[] = [];
    const input = userInput.toLowerCase();
    
    const countryKeywords = [
      'france', 'allemagne', 'espagne', 'italie', 'belgique', 
      'suisse', 'canada', 'maroc', 'algérie', 'tunisie'
    ];
    
    countryKeywords.forEach(country => {
      if (input.includes(country)) {
        countries.push(country);
      }
    });
    
    return countries;
  }

  /**
   * Extrait les secteurs mentionnés dans l'input utilisateur
   */
  private extractSectorsFromInput(userInput: string): string[] {
    const sectors: string[] = [];
    const input = userInput.toLowerCase();
    
    const sectorKeywords = [
      'santé', 'éducation', 'finance', 'technologie', 'immobilier',
      'commerce', 'industrie', 'agriculture', 'tourisme', 'transport'
    ];
    
    sectorKeywords.forEach(sector => {
      if (input.includes(sector)) {
        sectors.push(sector);
      }
    });
    
    return sectors;
  }

  /**
   * Génère une conclusion encourageante
   */
  private generateEncouragingClosing(stepTitle: string): string {
    const encouragements = [
      "Vous avez tous les éléments pour réussir cette démarche",
      "Votre situation est tout à fait gérable avec la bonne approche",
      "En suivant ce plan, vous maximisez vos chances de succès",
      "Cette stratégie a fait ses preuves dans des cas similaires",
      "Vous êtes sur la bonne voie pour résoudre cette situation"
    ];
    
    const actionCalls = [
      "Commençons par la première étape dès maintenant",
      "Passons à l'action avec confiance",
      "Mettons ce plan en œuvre étape par étape",
      "Démarrons cette démarche de manière structurée",
      "Avançons méthodiquement vers votre objectif"
    ];
    
    const randomEncouragement = encouragements[Math.floor(Math.random() * encouragements.length)];
    const randomAction = actionCalls[Math.floor(Math.random() * actionCalls.length)];
    
    return `💪 ${randomEncouragement}. ${randomAction} !`;
  }

  /**
   * Génère le prochain jalon à atteindre
   */
  private generateNextMilestone(stepTitle: string): string {
  // Formulation renforcée : action spécifique + sous-tâche + demande d'accord
  return `🎯 **Proposition de prochaine étape (Next Best Action) :** Sur la base de l'analyse, je propose que la prochaine étape soit de compléter l'étape "${stepTitle}" en commençant par rassembler les documents essentiels (preuve d'identité, preuve de domicile, courriers officiels relatifs au dossier). Pour y parvenir, concentrons-nous d'abord sur la sous-tâche suivante : lister et fournir les 3 documents prioritaires mentionnés ci-dessus. Êtes-vous d'accord pour aborder ce point en priorité ?`;
  }

  /**
   * Extrait les éléments clés du contexte utilisateur
   */
  private extractKeyElements(userInput: string): string[] {
    const elements: string[] = [];
    const input = userInput.toLowerCase();
    
    // Éléments de contexte courants
    if (input.includes('titre') && input.includes('séjour')) {
      elements.push("Démarche liée au titre de séjour");
    }
    
    if (input.includes('urssaf') || input.includes('cotisations')) {
      elements.push("Situation avec l'URSSAF à clarifier");
    }
    
    if (input.includes('entreprise') || input.includes('activité')) {
      elements.push("Projet entrepreneurial ou activité professionnelle");
    }
    
    if (input.includes('conjoint') || input.includes('famille')) {
      elements.push("Situation familiale européenne");
    }
    
    if (input.includes('renouvellement')) {
      elements.push("Procédure de renouvellement en cours");
    }
    
    if (input.includes('changement')) {
      elements.push("Demande de changement de statut");
    }
    
    // Si aucun élément spécifique, ajouter un élément générique
    if (elements.length === 0) {
      elements.push("Situation administrative complexe nécessitant un accompagnement structuré");
    }
    
    return elements;
  }

  /**
   * Formate le plan d'action complet pour inclusion dans une réponse
   */
  formatActionPlanForResponse(actionPlan: ActionPlan): string {
    let formatted = `${actionPlan.empathyOpening}\n\n`;
    
    // Résumé de situation
    formatted += `${actionPlan.situationSummary}\n\n`;
    
    // Droits confirmés
    if (actionPlan.confirmedRights.length > 0) {
      formatted += "✅ **Ce qui est acquis dans votre situation :**\n\n";
      actionPlan.confirmedRights.forEach(right => {
        const certaintyEmoji = right.certaintyLevel === 'ABSOLUTE' ? '🔒' : 
                              right.certaintyLevel === 'HIGH' ? '✅' : '📋';
        formatted += `${certaintyEmoji} **${right.right}**\n`;
        formatted += `   ${right.practicalImplication}\n\n`;
      });
    }
    
    // Évaluation des risques
    if (actionPlan.riskAssessment.length > 0) {
      formatted += "⚠️ **Points de vigilance identifiés :**\n\n";
      actionPlan.riskAssessment.forEach(risk => {
        const riskEmoji = risk.impact === 'HIGH' ? '🔴' : 
                         risk.impact === 'MEDIUM' ? '🟡' : '🟢';
        formatted += `${riskEmoji} **${risk.risk}**\n`;
        formatted += `   *Mitigation :* ${risk.mitigation}\n\n`;
      });
    }
    
    // Plan d'action chronologique
    formatted += "🎯 **Votre plan d'action chronologique :**\n\n";
    
    // Affichage de la validation de faisabilité si présente
    if (actionPlan.feasibilityValidation) {
      const validation = actionPlan.feasibilityValidation;
      const statusEmoji = validation.isValidated ? '✅' : '⚠️';
      const confidenceEmoji = validation.confidence === 'HIGH' ? '🟢' : 
                             validation.confidence === 'MEDIUM' ? '🟡' : '🔴';
      
      formatted += `${statusEmoji} **Validation de Faisabilité Opérationnelle** ${confidenceEmoji}\n`;
      formatted += `   *Confiance :* ${validation.confidence} | *Problèmes détectés :* ${validation.issues}\n`;
      
      if (!validation.isValidated && validation.issues > 0) {
        formatted += `   ⚡ *Actions optimisées selon l'expertise terrain*\n`;
      }
      formatted += "\n";
    }
    
    actionPlan.actionSteps.forEach(step => {
      const priorityEmoji = step.priority === 'URGENT' ? '🔥' : 
                           step.priority === 'IMPORTANT' ? '⭐' : '📌';
      formatted += `**${step.order}. ${step.title}** ${priorityEmoji}\n`;
      formatted += `   ${step.description}\n`;
      formatted += `   *Délai :* ${step.timeline}\n`;
      formatted += `   *Résultat attendu :* ${step.expectedOutcome}\n\n`;
    });
    
    // Rapport détaillé de faisabilité si nécessaire
    if (actionPlan.feasibilityValidation?.report && actionPlan.feasibilityValidation.issues > 0) {
      formatted += "---\n\n";
      formatted += "🔍 **Rapport d'Optimisation du Plan d'Action**\n\n";
      formatted += "*Note : Ce plan a été automatiquement challengé par le filtre de faisabilité opérationnelle pour éviter les écueils courants.*\n\n";
      // On n'affiche le rapport complet que si demandé explicitement pour ne pas surcharger
    }
    
    // Conclusion encourageante
    formatted += `${actionPlan.encouragingClosing}\n\n`;
    formatted += `${actionPlan.nextMilestone}`;
    
    return formatted;
  }
}

// Instance singleton
export const actionPlanFormatterService = new ActionPlanFormatterService();
