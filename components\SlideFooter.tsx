import React, { useState } from 'react';
import { gsap } from 'gsap';

interface SlideFooterProps {
  // Pas de props pour le moment, tout est en dur
}

const SlideFooter: React.FC<SlideFooterProps> = () => {
  const [isOpen, setIsOpen] = useState(false);

  // Fonction pour basculer l'état du footer
  const toggleFooter = () => {
    const newState = !isOpen;
    setIsOpen(newState);

    // Animation GSAP pour le slide
    const footerContent = document.querySelector('.footer-content');

    if (footerContent) {
      if (newState) {
        // Ouvrir le footer
        gsap.to(footerContent, {
          y: 0,
          duration: 0.4,
          ease: "power2.out"
        });
      } else {
        // Fermer le footer
        gsap.to(footerContent, {
          y: '100%',
          duration: 0.4,
          ease: "power2.in"
        });
      }
    }
  };

  // Année actuelle pour le copyright
  const currentYear = new Date().getFullYear();

  // Fonction pour gérer les erreurs de chargement d'images
  const handleImageError = (_e: React.SyntheticEvent<HTMLImageElement>) => {
    // Erreur silencieuse
  };

  return (
    <>
      {/* Contenu du footer slide avec languette intégrée */}
      <div
        className="footer-content fixed bottom-0 left-0 right-0 z-40 transform translate-y-full"
        style={{ willChange: 'transform' }}
      >
        {/* Languette pour ouvrir/fermer - Maintenant à l'intérieur du footer */}
        <div
          className="footer-tab absolute left-1/2 transform -translate-x-1/2 -top-[40px] z-10 cursor-pointer"
          onClick={toggleFooter}
        >
          <div className="bg-gray-800/95 backdrop-blur-md border-l border-r border-t border-gray-600 rounded-t-lg px-4 py-2 shadow-lg hover:bg-gray-700/95 transition-colors duration-300">
            <div className="flex items-center space-x-2">
              {/* Icône de flèche qui tourne selon l'état */}
              <svg
                className={`w-4 h-4 text-gray-300 transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
              </svg>
              <span className="text-sm text-gray-300 font-medium">
                {isOpen ? 'Fermer' : 'Liens'}
              </span>
            </div>
          </div>
        </div>
        <div className="bg-gray-800/95 backdrop-blur-md border-t border-gray-600 shadow-2xl">
          {/* Section des icônes sociales */}
          <div className="p-6">
            <div className="flex justify-center items-center space-x-8">
              
              {/* Lien Portfolio FlexoDiv */}
              <a 
                href="https://flexodiv.netlify.app/" 
                target="_blank" 
                rel="noopener noreferrer" 
                className="transition-transform duration-300 hover:scale-110 group"
                aria-label="Portfolio de FlexoDiv"
              >
                <div className="relative">
                  <img
                    src="https://res.cloudinary.com/dte5zykne/image/upload/v1750807840/02-Logo-FlexoDiv_uoxcao.png"
                    alt="Logo FlexoDiv"
                    className="h-12 w-12 rounded-full object-cover shadow-lg"
                    onError={handleImageError}
                  />
                  <div className="absolute inset-0 rounded-full bg-gradient-to-t from-purple-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                  <div className="text-center mt-2">
                    <span className="text-xs text-gray-300 group-hover:text-purple-300 transition-colors duration-300">FlexoDiv</span>
                  </div>
              </a>

              {/* Lien Gmail */}
              <a 
                href="mailto:<EMAIL>?subject=Prise%20de%20contact" 
                className="transition-transform duration-300 hover:scale-110 group"
                aria-label="Envoyer un email"
              >
                <div className="relative">
                  <div className="h-12 w-12 bg-red-600 rounded-lg shadow-lg flex items-center justify-center">
                    <svg className="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 5.457v13.909c0 .904-.732 1.636-1.636 1.636h-3.819V11.73L12 16.64l-6.545-4.91v9.273H1.636A1.636 1.636 0 0 1 0 19.366V5.457c0-.904.732-1.636 1.636-1.636h3.819l6.545 4.91 6.545-4.91h3.819A1.636 1.636 0 0 1 24 5.457z"/>
                    </svg>
                  </div>
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-t from-red-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div className="text-center mt-2">
                  <span className="text-xs text-gray-300 group-hover:text-red-300 transition-colors duration-300">Email</span>
                </div>
              </a>

              {/* Lien LinkedIn */}
              <a
                href="https://www.linkedin.com/in/flexodiv-engineering-prompting-982582203"
                target="_blank"
                rel="noopener noreferrer"
                className="transition-transform duration-300 hover:scale-110 group"
                aria-label="Voir le profil LinkedIn"
              >
                <div className="relative">
                  <div className="h-12 w-12 bg-blue-600 rounded-lg shadow-lg flex items-center justify-center">
                    <svg className="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                  </div>
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-t from-blue-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div className="text-center mt-2">
                  <span className="text-xs text-gray-300 group-hover:text-blue-300 transition-colors duration-300">LinkedIn</span>
                </div>
              </a>

              {/* Lien YouTube */}
              <a
                href="https://www.youtube.com/@flexodiv"
                target="_blank"
                rel="noopener noreferrer"
                className="transition-transform duration-300 hover:scale-110 group"
                aria-label="Visiter la chaîne YouTube"
              >
                <div className="relative">
                  <div className="h-12 w-12 bg-red-600 rounded-lg shadow-lg flex items-center justify-center">
                    <svg className="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                    </svg>
                  </div>
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-t from-red-600/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div className="text-center mt-2">
                  <span className="text-xs text-gray-300 group-hover:text-red-300 transition-colors duration-300">YouTube</span>
                </div>
              </a>

            </div>
          </div>

          {/* Section copyright */}
          <div className="border-t border-gray-600 px-6 py-4">
            <div className="text-center">
              <p className="text-sm text-gray-400">
                © {currentYear} FlexoDiv - Tous droits réservés
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Roony - Studio Agentique de Workflow IA
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Overlay pour fermer en cliquant à côté (optionnel) */}
      {isOpen && (
        <div
          className="fixed inset-0 z-30 bg-black/20 backdrop-blur-sm"
          onClick={toggleFooter}
        />
      )}
    </>
  );
};

export default SlideFooter;
