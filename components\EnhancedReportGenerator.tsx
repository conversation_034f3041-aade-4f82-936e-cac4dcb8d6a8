import React, { useState, useEffect } from 'react';
import { enhancedReportGenerationService, type StrategicSynthesisReport } from '../src/services/enhancedReportGenerationService';
import { workflowMemoryService } from '../services/workflowMemoryService';
import type { Message } from '../types';

interface EnhancedReportGeneratorProps {
  initialProblem: string;
  conversation: Message[];
  finalPrompt: string;
  currentStepIndex: number;
  className?: string;
}

export const EnhancedReportGenerator: React.FC<EnhancedReportGeneratorProps> = ({
  initialProblem,
  conversation,
  finalPrompt,
  currentStepIndex,
  className = ''
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [report, setReport] = useState<StrategicSynthesisReport | null>(null);
  const [stateTrackingEnabled, setStateTrackingEnabled] = useState(true);
  const [showReport, setShowReport] = useState(false);

  // Effet pour le state tracking automatique
  useEffect(() => {
    if (stateTrackingEnabled && conversation.length > 0) {
      // Analyser automatiquement les corrections dans la conversation
      analyzeConversationForCorrections();
    }
  }, [conversation, stateTrackingEnabled]);

  const analyzeConversationForCorrections = () => {
    conversation.forEach((message, index) => {
      if (message.sender === 'user' && message.text.toLowerCase().includes('correction')) {
        // Détecter les corrections utilisateur
        const technicalValues = message.text.match(/(\d+[\s]*[kKWwHh]+)/g);
        if (technicalValues) {
          technicalValues.forEach(value => {
            enhancedReportGenerationService.trackDataState(
              `technical_value_${index}`,
              value,
              'USER'
            );
          });
        }
      }
    });
  };

  const handleGenerateReport = async () => {
    setIsGenerating(true);
    try {
      const insights = workflowMemoryService.getAllInsights();
      
      const strategicReport = enhancedReportGenerationService.generateStrategicSynthesisReport(
        conversation,
        initialProblem,
        finalPrompt,
        insights
      );
      
      setReport(strategicReport);
      setShowReport(true);
    } catch (error) {
      console.error('Erreur lors de la génération du rapport amélioré:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleExportReport = (format: 'pdf' | 'md') => {
    if (!report) return;

    const markdownContent = enhancedReportGenerationService.formatReportAsMarkdown(report);

    if (format === 'pdf') {
      // Créer une version PDF via impression
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>Rapport Stratégique Roony v4.3</title>
            <meta charset="utf-8">
            <style>
              body { 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                line-height: 1.6; 
                color: #333; 
                max-width: 800px; 
                margin: 0 auto; 
                padding: 20px;
              }
              h1 { color: #2563eb; border-bottom: 3px solid #2563eb; padding-bottom: 10px; }
              h2 { color: #1e40af; margin-top: 30px; }
              h3 { color: #1e3a8a; }
              .theme-section { 
                background: #f8fafc; 
                padding: 20px; 
                border-left: 4px solid #2563eb; 
                margin: 20px 0; 
                border-radius: 8px;
              }
              .solution-validated { 
                background: #f0f9ff; 
                padding: 15px; 
                border-radius: 8px; 
                border: 1px solid #bae6fd;
              }
              .config-item { 
                background: #fefce8; 
                padding: 10px; 
                margin: 5px 0; 
                border-radius: 5px;
              }
              .conclusion { 
                background: #ecfdf5; 
                padding: 20px; 
                border-radius: 8px; 
                border: 1px solid #bbf7d0; 
              }
              @media print {
                body { font-size: 12px; }
              }
            </style>
          </head>
          <body>
            ${markdownToHTML(markdownContent)}
          </body>
          </html>
        `);
        printWindow.document.close();
        setTimeout(() => printWindow.print(), 500);
      }
    } else {
      // Export Markdown
      const blob = new Blob([markdownContent], { type: 'text/markdown', endings: 'native' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `rapport-strategique-synthetique-${new Date().toISOString().split('T')[0]}.md`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const markdownToHTML = (markdown: string): string => {
    return markdown
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/^\*\*(.*?)\*\*/gm, '<strong>$1</strong>')
      .replace(/^- (.*$)/gm, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')
      .replace(/^---$/gm, '<hr>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/^(.*)$/gm, '<p>$1</p>')
      .replace(/<p><h/g, '<h')
      .replace(/<\/h([1-6])><\/p>/g, '</h$1>')
      .replace(/<p><hr><\/p>/g, '<hr>')
      .replace(/<p><ul>/g, '<ul>')
      .replace(/<\/ul><\/p>/g, '</ul>');
  };

  // Ne pas afficher si pas assez de données
  if (conversation.length < 4 || currentStepIndex < 3) {
    return null;
  }

  const stats = enhancedReportGenerationService.getStateTrackingStats();

  return (
    <div className={`${className} bg-gradient-to-r from-emerald-900/20 to-teal-900/20 rounded-xl border border-emerald-500/30 overflow-hidden`}>
      <div className="p-4 bg-emerald-700/20 border-b border-emerald-600/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
            <div>
              <h3 className="text-sm font-semibold text-emerald-300">Rapport Stratégique Synthétique v4.3</h3>
              <p className="text-xs text-emerald-400/80">Selon les principes de Gemini</p>
            </div>
          </div>
          
          {stats.totalTracked > 0 && (
            <div className="text-xs text-emerald-400 bg-emerald-900/30 px-2 py-1 rounded">
              {stats.validated}/{stats.totalTracked} validées
            </div>
          )}
        </div>
      </div>

      <div className="p-4 space-y-4">
        {!showReport ? (
          <>
            <div className="space-y-3">
              <p className="text-sm text-slate-300">
                📊 Génération d'un rapport synthétique unique remplaçant les 2 rapports actuels
              </p>
              
              <div className="grid grid-cols-2 gap-3 text-xs">
                <div className="bg-slate-800/50 p-3 rounded-lg">
                  <div className="text-emerald-400 font-semibold mb-1">🎯 Analyse Thématique</div>
                  <div className="text-slate-400">Identification automatique des arcs thématiques</div>
                </div>
                <div className="bg-slate-800/50 p-3 rounded-lg">
                  <div className="text-blue-400 font-semibold mb-1">🔍 State Tracking</div>
                  <div className="text-slate-400">Suivi des corrections et validations</div>
                </div>
              </div>

              <div className="flex items-center gap-2 text-xs">
                <input
                  type="checkbox"
                  id="stateTracking"
                  checked={stateTrackingEnabled}
                  onChange={(e) => setStateTrackingEnabled(e.target.checked)}
                  className="rounded"
                />
                <label htmlFor="stateTracking" className="text-slate-400">
                  Activer le suivi des corrections automatique
                </label>
              </div>
            </div>

            <button
              onClick={handleGenerateReport}
              disabled={isGenerating}
              className="w-full bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-500 hover:to-teal-500 disabled:from-slate-600 disabled:to-slate-600 text-white text-sm font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center justify-center gap-2"
            >
              {isGenerating ? (
                <>
                  <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Analyse thématique en cours...
                </>
              ) : (
                <>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Générer Synthèse Stratégique
                </>
              )}
            </button>
          </>
        ) : report && (
          <div className="space-y-4">
            {/* Résumé du rapport */}
            <div className="bg-slate-800/50 p-4 rounded-lg">
              <h4 className="text-lg font-bold text-transparent bg-clip-text bg-gradient-to-r from-emerald-400 to-teal-400 mb-2">
                📋 Synthèse Générée
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-slate-400">Thèmes identifiés:</span>
                  <span className="text-emerald-400 font-semibold">{report.metadata.totalThemes}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Solutions validées:</span>
                  <span className="text-emerald-400 font-semibold">{report.validatedSolutions.length}</span>
                </div>
                <div className="text-slate-300 mt-2">
                  {report.metadata.sessionSummary}
                </div>
              </div>
            </div>

            {/* Aperçu des thèmes */}
            <div className="space-y-2">
              <h5 className="text-sm font-semibold text-emerald-300">🎯 Axes Thématiques</h5>
              {report.thematicAnalysis.slice(0, 3).map((cluster, index) => (
                <div key={cluster.id} className="bg-slate-800/30 p-3 rounded border-l-2 border-emerald-500/50">
                  <div className="text-sm font-medium text-emerald-300">{cluster.theme}</div>
                  <div className="text-xs text-slate-400 mt-1">{cluster.description}</div>
                  <div className="text-xs text-slate-500 mt-1">
                    Confiance: {Math.round(cluster.confidence * 100)}%
                  </div>
                </div>
              ))}
              {report.thematicAnalysis.length > 3 && (
                <div className="text-xs text-slate-500 text-center">
                  +{report.thematicAnalysis.length - 3} thème(s) supplémentaire(s)
                </div>
              )}
            </div>

            {/* Actions d'export */}
            <div className="flex gap-2 pt-2 border-t border-slate-700/50">
              <button
                onClick={() => handleExportReport('pdf')}
                className="flex-1 bg-emerald-600/80 hover:bg-emerald-600 text-white text-sm font-semibold py-2 px-3 rounded transition-colors flex items-center justify-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                PDF
              </button>
              <button
                onClick={() => handleExportReport('md')}
                className="flex-1 bg-teal-600/80 hover:bg-teal-600 text-white text-sm font-semibold py-2 px-3 rounded transition-colors flex items-center justify-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                MD
              </button>
              <button
                onClick={() => {
                  setShowReport(false);
                  setReport(null);
                }}
                className="bg-slate-600/80 hover:bg-slate-600 text-white text-sm font-semibold py-2 px-3 rounded transition-colors flex items-center justify-center"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
