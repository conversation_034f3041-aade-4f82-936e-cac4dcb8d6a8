import React, { useState, useEffect } from 'react';
import { ArrowLeft } from 'lucide-react';
import { Header } from './components/Header';
import { ChatInterface } from './components/ChatInterface';
import { UserInput } from './components/UserInput';
import { WorkflowTracker } from './components/WorkflowTracker';
import { ThinkingSpace } from './components/ThinkingSpace';
import { FinalOutput } from './components/FinalOutput';
import { ModelMonitor } from './components/ModelMonitor';
import LanguageComplianceMonitor from './components/LanguageComplianceMonitor';
import TranslationMonitor from './components/TranslationMonitor';
import { EngagementPrompts } from './components/EngagementPrompts';
import { UserGuidance } from './components/UserGuidance';
import { ReportGenerator } from './components/ReportGenerator';
import { FinalActionPlanGenerator } from './components/FinalActionPlanGenerator';
import { NotificationManager } from './components/UpdateNotification';
import SlideFooter from './components/SlideFooter';
import ApiKeyStats from './components/ApiKeyStats';
import ModelUpdateNotification from './components/ModelUpdateNotification';
import ProfileBackup from './components/ProfileBackup';
import BackupNotification from './components/BackupNotification';
import RoonyMascot from './components/RoonyMascot';
import RoonyContextualMascot, { type RoonyContextualMascotRef } from './components/RoonyContextualMascot';
import PremiumAuth from './components/PremiumAuth';
import ModeToggle from './components/ModeToggle';
import SimplePremiumModal from './components/SimplePremiumModal';
import ModeSelection from './components/ModeSelection';
import { QuickPromptGenerator } from './components/QuickPromptGenerator';
import CompleteAnalysisInterface from './components/CompleteAnalysisInterface';
import { sendMessageToAI, generateSystemPrompt, generateFinalOutput, setUsageMode } from './services/geminiService';
import { contextFileService, type ContextFile } from './src/services/contextFileService';
import { expertConsultantService } from './src/services/expertConsultantService';
import { executeEnhancedRoonyAnalysis } from './src/services/roonyIntelligenceAPI';
import { buildMissionContext } from './services/roonyConversationService';
import { profileBackupService } from './services/profileBackupService';
import { silentMemoryService } from './src/services/silentMemoryService';
import { workflowMemoryService } from './services/workflowMemoryService';
import { usageModeService } from './services/usageModeService';
import { scrollProtectionService } from './services/scrollProtectionService';
import { premiumAuthService } from './services/premiumAuthService';
import './utils/premiumDebugHelper'; // Charge l'helper de debug Premium
import { WORKFLOW_STEPS } from './constants';
import type { Message } from './types';
import { roonyPreloader } from './utils/roonyPreloader';

const App: React.FC = () => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [initialProblem, setInitialProblem] = useState('');
  const [conversation, setConversation] = useState<Message[]>([]);
  const [reasoningLog, setReasoningLog] = useState<string[]>([]);
  const [finalPrompt, setFinalPrompt] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showApiStats, setShowApiStats] = useState(false);
  const [showWelcomeRoony, setShowWelcomeRoony] = useState(true); // Pour contrôler l'affichage de Roony d'accueil
  
  // États pour la sélection de mode
  const [currentMode, setCurrentMode] = useState<'selection' | 'quickPrompt' | 'fullAnalysis'>('selection');
  const [isWorkflowStarted, setIsWorkflowStarted] = useState(false);
  
  // États pour le mode Premium - intégré avec usageModeService
  const [usageMode, setUsageModeState] = useState<'free' | 'premium'>('free');
  const [showPremiumAuth, setShowPremiumAuth] = useState(false);
  const [showModeModal, setShowModeModal] = useState(false);
  const [showSimplePremiumModal, setShowSimplePremiumModal] = useState(false);
  const [requiresAuth, setRequiresAuth] = useState(false);

  // Référence pour contrôler les animations de Roony (pour les animations de réflexion)
  const roonyRef = React.useRef<RoonyContextualMascotRef>(null);

  // Synchronisation avec le service de mode d'utilisation
  useEffect(() => {
    const unsubscribe = usageModeService.onModeChange((mode) => {
      setUsageModeState(mode);
      setUsageMode(mode); // Synchroniser avec le service gemini
    });

    return unsubscribe;
  }, []);

  // Vérification de l'authentification au démarrage
  useEffect(() => {
    const checkAuthRequired = () => {
      // Vérifier si l'utilisateur a déjà fait un choix (stocké dans localStorage)
      const userChoice = localStorage.getItem('roony_user_choice');

      if (userChoice === 'free_mode_chosen') {
        // L'utilisateur a déjà choisi le mode gratuit
        setRequiresAuth(false);
        setShowSimplePremiumModal(false);
        return;
      }

      // Vérifier si l'utilisateur est déjà authentifié en Premium
      const premiumAuth = premiumAuthService.getCurrentAuth();

      if (premiumAuth.user.isAuthenticated) {
        // Utilisateur déjà authentifié, pas besoin de modal
        setRequiresAuth(false);
        setShowSimplePremiumModal(false);
      } else {
        // Vérifier si les clés API par défaut fonctionnent
        const mainApiKey = process.env.VITE_API_KEY;
        const hasValidDefaultKey = mainApiKey && mainApiKey !== 'PLACEHOLDER_API_KEY' && mainApiKey.trim() !== '';

        if (!hasValidDefaultKey) {
          // Aucune clé API valide, proposer le choix à l'utilisateur
          console.log('🔒 Aucune clé API valide détectée - Choix du mode requis');
          setRequiresAuth(true);
          setShowSimplePremiumModal(true);
        }
      }
    };

    checkAuthRequired();
  }, []);

  // Gestion du changement de mode améliorée
  const handleModeChange = (mode: 'free' | 'premium') => {
    const success = usageModeService.setUsageMode(mode);
    
    if (!success && mode === 'premium') {
      // Si le changement vers Premium échoue, ouvrir l'authentification
      setShowPremiumAuth(true);
    }
    
    setShowModeModal(false);
  };

  // Ouvrir/fermer la modal de mode
  const handleModeToggle = () => {
    setShowModeModal(!showModeModal);
  };

  // Gestion directe de l'authentification Premium depuis le Header
  const handlePremiumAuth = () => {
    console.log('🔒 Ouverture directe de l\'authentification Premium');
    setShowSimplePremiumModal(true);
  };

  // Gestion du choix du mode gratuit
  const handleFreeModeChoice = () => {
    console.log('🆓 Utilisateur choisit le mode gratuit');
    // Sauvegarder le choix de l'utilisateur
    localStorage.setItem('roony_user_choice', 'free_mode_chosen');
    setRequiresAuth(false);
    setShowSimplePremiumModal(false);
    // Rester en mode gratuit (pas de changement de mode nécessaire)
  };

  // Précharger les animations de Roony au démarrage de l'application
  useEffect(() => {
    // NOUVEAU: Démarrer la protection du scroll
    scrollProtectionService.startMonitoring();

    // Précharger les animations prioritaires immédiatement
    roonyPreloader.preloadPriorityAnimations();

    // Précharger toutes les animations après un délai
    const timer = setTimeout(() => {
      roonyPreloader.preloadAllAnimations();
    }, 2000);

    return () => {
      clearTimeout(timer);
      // Arrêter la surveillance du scroll au démontage
      scrollProtectionService.stopMonitoring();
    };
  }, []);

  // L'animation de Roony est maintenant en boucle permanente, plus besoin de gestion de cycle

  useEffect(() => {
    if (currentStepIndex === WORKFLOW_STEPS.length - 1 && !finalPrompt && !isProcessing) {
      generateFinalPrompt();
    }
  }, [currentStepIndex, finalPrompt, isProcessing]);

  const generateFinalPrompt = async () => {
      setIsProcessing(true);
      setReasoningLog((prev: string[]) => [...prev, "Génération du prompt final et de la méta-analyse..."]);

      // Déclencher l'animation de réflexion de Roony pour l'étape finale
      roonyRef.current?.triggerContext('thinking', WORKFLOW_STEPS.length - 1);

      try {
          const fullConversation = [...conversation];
          const result = await generateFinalOutput(fullConversation);
          setFinalPrompt(result);
          setCurrentStepIndex((prev: number) => prev + 1); // Mark the final step as complete

          // Animation de résultat final supprimée
      } catch (e) {
          console.error(e);
          const errorMessage = e instanceof Error ? e.message : 'Une erreur inconnue est survenue.';
          setError(`Erreur lors de la génération de la sortie finale : ${errorMessage}`);

          // Animation d'erreur supprimée
      } finally {
          setIsProcessing(false);
      }
  };

  const handleStartWorkflow = async (problem: string, contextFiles?: ContextFile[]) => {
    setInitialProblem(problem);
    setCurrentStepIndex(0);
    setError(null);
    setFinalPrompt(null);
    setReasoningLog([]);
    setShowWelcomeRoony(false); // Cacher Roony d'accueil quand on commence le workflow
    setIsWorkflowStarted(true);

    // Animation de début de workflow supprimée

    const introMessage: Message = { sender: 'ai', text: "Excellent ! Commençons à analyser votre défi. Étape 1 : Définition du Problème." };
    setConversation([introMessage]);

    // Sauvegarder le nouveau workflow dans le profil utilisateur
    profileBackupService.addWorkflow({
      problemDescription: problem,
      stepsCompleted: 0,
      totalSteps: WORKFLOW_STEPS.length
    });

    await processMessage(problem, 0, [introMessage], contextFiles);
  };

  const handleStartNewWorkflow = () => {
    // Réinitialiser complètement l'application pour un nouveau workflow
    setInitialProblem('');
    setCurrentStepIndex(0);
    setError(null);
    setFinalPrompt(null);
    setReasoningLog([]);
    setConversation([]);
    setShowWelcomeRoony(true); // Réafficher Roony d'accueil
    // Réinitialiser la mémoire du workflow
    workflowMemoryService.reset();
    // Réinitialiser le mode de sélection
    setCurrentMode('selection');
    setIsWorkflowStarted(false);
  };

  const handleRefinePrompt = () => {
    // Revenir au workflow pour affiner le prompt existant
    setFinalPrompt(null);
    setCurrentStepIndex(WORKFLOW_STEPS.length - 2); // Revenir à l'avant-dernière étape

    const refineMessage: Message = {
      sender: 'ai',
      text: "Parfait ! Reprenons le processus pour affiner votre prompt. Que souhaitez-vous améliorer ou préciser dans la solution actuelle ?"
    };
    setConversation((prev: Message[]) => [...prev, refineMessage]);
  };

  // Handlers pour la sélection de mode
  const handleSelectQuickPromptMode = () => {
    setCurrentMode('quickPrompt');
    setShowWelcomeRoony(false);
  };

  const handleSelectAnalysisMode = () => {
    setCurrentMode('fullAnalysis');
    setShowWelcomeRoony(false);
  };

  const handleBackToSelection = () => {
    setCurrentMode('selection');
    setIsWorkflowStarted(false);
    setCurrentStepIndex(0);
    setInitialProblem('');
    setConversation([]);
    setFinalPrompt(null);
    setError(null);
    setShowWelcomeRoony(true);
    workflowMemoryService.reset();
  };

  const handleStartFullAnalysis = async (problem: string, contextFiles?: ContextFile[]) => {
    setCurrentMode('fullAnalysis');
    setIsWorkflowStarted(true);
    await handleStartWorkflow(problem, contextFiles);
  };

  const handleSendMessage = async (messageText: string, contextFiles?: ContextFile[]) => {
    if (isProcessing || currentStepIndex >= WORKFLOW_STEPS.length - 1) return;

    const newUserMessage: Message = { sender: 'user', text: messageText };
    const updatedConversation = [...conversation, newUserMessage];
    setConversation(updatedConversation);

    // Sauvegarder le message utilisateur
    profileBackupService.addConversation(newUserMessage);

    await processMessage(messageText, currentStepIndex, updatedConversation, contextFiles);
  };

  const handleSuggestedAction = (suggestion: string) => {
    handleSendMessage(suggestion);
  };

  const processMessage = async (userInput: string, stepIndex: number, currentConversation: Message[], contextFiles?: ContextFile[]) => {
    setIsProcessing(true);
    setError(null);

    try {
        // 🔇 NOUVEAU : Lecture silencieuse de la mémoire AVANT l'analyse
        console.log('🔇 Lecture silencieuse de la mémoire...');

        const silentMemoryContext = await silentMemoryService.readMemorySilently(
          userInput,
          'analysis' // Stage pour éviter les doublons
        );

        // Construction du contexte de conversation pour P.R.O.F.
        // ✅ CORRECTION : Mode silencieux activé
        const conversationContext = {
          stage: 'synthesis' as const, // ✅ Stage qui évite la réinjection
          collectedData: {
            personnage: initialProblem ? `Utilisateur avec le problème: ${initialProblem}` : 'Utilisateur',
            objectif: initialProblem || userInput,
            role: 'Expert conseil en résolution de problèmes',
            format: 'Analyse structurée et recommandations pratiques'
          },
          conversation: currentConversation,
          // ✅ NOUVEAU : Contexte silencieux pour l'IA uniquement
          silentMemoryContext: silentMemoryContext
        };
        
        // Appel de l'API enrichie avec mémoire persistante
        const analysisResult = await executeEnhancedRoonyAnalysis(
          conversationContext,
          initialProblem || userInput,
          [WORKFLOW_STEPS[stepIndex]], // Une étape à la fois
          contextFiles
        );

        if (analysisResult.success && analysisResult.data) {
          // ✅ Utiliser le résultat enrichi silencieusement
          const enrichedContent = analysisResult.data.finalDeliverable;

          // ✅ CORRECTION : Logs silencieux uniquement - AUCUN affichage utilisateur
          if (silentMemoryContext.hasPertinentCases) {
            console.log(`🔇 [SILENT] Analyse enrichie avec ${silentMemoryContext.casesCount} cas (invisible utilisateur)`);
          } else {
            console.log('🔇 [SILENT] Analyse sans contexte mémoire pertinent');
          }

          // ✅ Affichage du résultat SANS mention de mémoire
          const aiMessage: Message = { sender: 'ai', text: enrichedContent };
          const newConversation = [...currentConversation, aiMessage];
          setConversation(newConversation);

          // ✅ Sauvegarder silencieusement l'interaction pour apprentissage futur
          await silentMemoryService.saveInteractionSilently(userInput, enrichedContent);

          // Sauvegarder la réponse dans le profil
          profileBackupService.addConversation(aiMessage);

          // Log des métriques de performance avec modèle utilisé
          if (analysisResult.debugInfo) {
            const { performanceMetrics, analysisMode, memoryEnriched, modelUsed } = analysisResult.debugInfo;

            // Utiliser le modèle depuis debugInfo ou fallback
            const modelInfo = modelUsed || 'Modèle non spécifié';

            setReasoningLog((prev: string[]) => [
              ...prev,
              `Étape ${stepIndex + 1}: ${modelInfo} - Mode ${analysisMode} (Mémoire: ${memoryEnriched ? 'OUI' : 'NON'})`,
              `Performance: ${performanceMetrics.executionTime}ms`
            ]);
          }

          // Progression vers l'étape suivante
          if (stepIndex < WORKFLOW_STEPS.length - 1) {
            setCurrentStepIndex(stepIndex + 1);
            
            const nextStep = WORKFLOW_STEPS[stepIndex + 1];
            const nextStepMessage: Message = {
              sender: 'ai',
              text: `Parfait ! Passons maintenant à l'étape ${stepIndex + 2} : ${nextStep.title}`
            };
            
            setTimeout(() => {
              setConversation(prev => [...prev, nextStepMessage]);
              profileBackupService.addConversation(nextStepMessage);
            }, 1000);
          } else {
            // Fin du workflow - génération du prompt final
            setFinalPrompt(enrichedContent);
            
            // Sauvegarder le workflow complet
            profileBackupService.addWorkflow({
              problemDescription: initialProblem || userInput,
              stepsCompleted: WORKFLOW_STEPS.length,
              totalSteps: WORKFLOW_STEPS.length
            });
          }
        } else {
          // ⚠️ Fallback vers l'ancien système en cas d'erreur
          console.warn('⚠️ Erreur API mémoire, fallback vers ancien système:', analysisResult.error);
          
          // Utiliser l'ancien service comme fallback
          const workflowStep = WORKFLOW_STEPS[stepIndex];
          let systemPrompt: string;
          
          if (contextFiles && contextFiles.length > 0) {
            systemPrompt = expertConsultantService.generateExpertSystemPrompt(
              stepIndex, 
              initialProblem || userInput, 
              workflowStep,
              contextFiles
            );
          } else {
            systemPrompt = generateSystemPrompt(stepIndex, initialProblem || userInput);
          }
          
          const apiConversation = [...currentConversation];
          if (stepIndex === 0) {
              apiConversation.push({ sender: 'user', text: userInput });
          }

          const messagesForApi = [
              { role: 'system', content: systemPrompt },
              ...apiConversation.map(m => ({
                  role: m.sender === 'user' ? 'user' : 'assistant',
                  content: m.text
              }))
          ];

          const task = WORKFLOW_STEPS[stepIndex].task;
          const { content, modelUsed } = await sendMessageToAI(messagesForApi, task);

          setReasoningLog((prev: string[]) => [...prev, `Étape ${stepIndex + 1}: Modèle utilisé - ${modelUsed} (Mode fallback)`]);

          const aiMessage: Message = { sender: 'ai', text: content };
          const newConversation = [...currentConversation, aiMessage];
          setConversation(newConversation);

          profileBackupService.addConversation(aiMessage);

          if (stepIndex < WORKFLOW_STEPS.length - 1) {
              setCurrentStepIndex(stepIndex + 1);
              
              const nextStep = WORKFLOW_STEPS[stepIndex + 1];
              const nextStepMessage: Message = {
                  sender: 'ai',
                  text: `Étape ${stepIndex + 2} : ${nextStep.title}\n\n${nextStep.description}`
              };
              
              setTimeout(() => {
                  setConversation(prev => [...prev, nextStepMessage]);
                  profileBackupService.addConversation(nextStepMessage);
              }, 1000);
          } else {
              setFinalPrompt(content);
              profileBackupService.addWorkflow({
                  problemDescription: initialProblem || userInput,
                  stepsCompleted: WORKFLOW_STEPS.length,
                  totalSteps: WORKFLOW_STEPS.length
              });
          }
        }

    } catch (error) {
        console.error('Erreur lors du traitement du message:', error);
        setError(`Erreur lors du traitement: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
        const errorAiMessage: Message = { sender: 'ai', text: `Désolé, une erreur est survenue : ${error instanceof Error ? error.message : 'Erreur inconnue'}`};
        setConversation((prev: Message[]) => [...prev, errorAiMessage]);
    } finally {
        setIsProcessing(false);
    }
  };


  return (
    <NotificationManager>
      <div
        className="h-screen w-screen text-gray-200 font-sans flex flex-col overflow-hidden"
        style={{
          backgroundColor: '#0f172a', // Fond sombre pour éviter le blanc avec PNG transparent
          backgroundImage: 'url(/Background.png)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundAttachment: 'fixed'
        }}
      >
      <Header
        usageMode={usageMode}
        onModeToggle={handleModeToggle}
        onPremiumAuth={handlePremiumAuth}
        showModeButton={!!initialProblem} // Afficher seulement pendant le workflow
      />
      
      <main className="flex-grow flex container mx-auto p-4 gap-4 overflow-hidden">
        {finalPrompt ? (
          // Mode plein écran pour le résultat final
          <div className="flex-grow flex flex-col bg-slate-800/50 rounded-2xl shadow-2xl border border-slate-700 overflow-hidden">
            <FinalOutput
              finalPrompt={finalPrompt}
              onStartNewWorkflow={handleStartNewWorkflow}
              onRefinePrompt={handleRefinePrompt}
              initialProblem={initialProblem}
              conversation={conversation}
              reasoningLog={reasoningLog}
              currentStepIndex={currentStepIndex}
            />
          </div>
        ) : initialProblem && currentMode === 'fullAnalysis' ? (
          // Layout 3 colonnes pour le workflow actif
          <>
            {/* COLONNE GAUCHE - Progression + Suggestions (25%) */}
            <aside className="w-1/4 flex flex-col gap-4">
              <div className="bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 overflow-hidden">
                <WorkflowTracker steps={WORKFLOW_STEPS} currentStepIndex={currentStepIndex} />
              </div>

              <div className="bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 p-4">
                <UserGuidance
                  currentStep={currentStepIndex}
                  totalSteps={WORKFLOW_STEPS.length}
                  isProcessing={isProcessing}
                />
              </div>

              {/* Suggestions d'engagement */}
              {!isProcessing && currentStepIndex < WORKFLOW_STEPS.length - 1 && currentStepIndex > 0 && (
                <div className="bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 p-4">
                  <EngagementPrompts
                    currentStep={currentStepIndex}
                    totalSteps={WORKFLOW_STEPS.length}
                    onSuggestedAction={handleSuggestedAction}
                  />
                </div>
              )}

              {/* Générateur de Plan d'Action Final - DÉPLACÉ ICI */}
              <FinalActionPlanGenerator
                initialProblem={initialProblem || ''}
                currentStepIndex={currentStepIndex}
                className="flex-shrink-0"
              />

              {/* Générateur de rapport stratégique */}
              {initialProblem && currentStepIndex > 1 && (
                <div className="bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 p-4">
                  <ReportGenerator
                    initialProblem={initialProblem}
                    conversation={conversation}
                    reasoningLog={reasoningLog}
                    currentStepIndex={currentStepIndex}
                  />
                </div>
              )}
            </aside>

            {/* COLONNE CENTRE - Chat + Input (50%) */}
            <div className="flex-1 flex flex-col bg-slate-800/50 rounded-2xl shadow-2xl border border-slate-700 overflow-hidden">
              <ChatInterface conversation={conversation} isProcessing={isProcessing} />
              <div className="p-4 border-t border-slate-700">
                {error && <p className="text-red-400 text-center mb-2">{error}</p>}
                <UserInput
                  isProcessing={isProcessing}
                  onSendMessage={handleSendMessage}
                  isWorkflowStarted={true}
                  isWorkflowComplete={currentStepIndex >= WORKFLOW_STEPS.length - 1}
                  lastAiMessage={conversation.length > 0 ? conversation[conversation.length - 1]?.sender === 'ai' ? conversation[conversation.length - 1]?.text : undefined : undefined}
                />
              </div>
            </div>

            {/* COLONNE DROITE - Raisonnement + Modèles (25%) */}
            <aside className="w-1/4 flex flex-col gap-4">
              {/* Bouton de retour à l'accueil */}
              <div className="flex-shrink-0">
                <button
                  onClick={handleBackToSelection}
                  className="w-full bg-slate-700 hover:bg-slate-600 text-slate-200 text-sm font-semibold py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                  Retour à l'accueil
                </button>
              </div>

              <ThinkingSpace log={reasoningLog} className="flex-grow" />
              <ModelMonitor className="flex-shrink-0" />
              <LanguageComplianceMonitor className="flex-shrink-0" />
              <TranslationMonitor className="flex-shrink-0" />

              {/* Composant de sauvegarde du profil utilisateur */}
              <ProfileBackup className="flex-shrink-0" />

              {/* Bouton pour afficher les statistiques des clés API */}
              <div className="flex-shrink-0">
                <button
                  onClick={() => setShowApiStats(true)}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-lg"
                >
                  📊 Statistiques API
                </button>
              </div>

              {/* Notification de mise à jour des modèles */}
              <ModelUpdateNotification className="flex-shrink-0" />

              <footer className="flex-shrink-0 text-center py-2">
                <a href="https://flexodiv.com" target="_blank" rel="noopener noreferrer" className="inline-block">
                  <img src="https://res.cloudinary.com/dte5zykne/image/upload/v1750807840/02-Logo-FlexoDiv_uoxcao.png" alt="Logo FlexoDiv" className="h-10 mx-auto opacity-60 hover:opacity-100 transition-opacity" />
                </a>
              </footer>
            </aside>
          </>
        ) : currentMode === 'selection' ? (
          // Écran de sélection de mode
          <div className="flex-grow flex flex-col">
            <ModeSelection 
              onSelectAnalysisMode={handleSelectAnalysisMode}
              onSelectQuickPromptMode={handleSelectQuickPromptMode}
            />
          </div>
        ) : currentMode === 'quickPrompt' ? (
          // Mode génération rapide de prompt
          <div className="flex-grow flex flex-col bg-slate-800/50 rounded-2xl shadow-2xl border border-slate-700 overflow-hidden">
            <QuickPromptGenerator 
              onBackToSelection={handleBackToSelection}
            />
          </div>
        ) : (
          // Écran d'accueil pour le mode analyse complète (pas encore démarré)
          <div className="flex-grow flex flex-col justify-center items-center p-8 text-center bg-slate-800/50 rounded-2xl shadow-2xl border border-slate-700 relative">
            {/* Roony toujours visible qui salue toutes les 30 secondes */}
            {showWelcomeRoony && (
              <div className="absolute top-12 left-1/2 transform -translate-x-1/2 z-10">
                <RoonyMascot
                  animation="greeting"
                  size={150}
                  entranceAnimation="fadeIn"
                  exitAnimation="none"
                  duration={0}
                  className=""
                  onAnimationComplete={() => {
                    console.log('Salut de Roony terminé');
                  }}
                />
              </div>
            )}

            <div className="flex items-center gap-4 mb-6">
              <button
                onClick={handleBackToSelection}
                className="bg-slate-700 hover:bg-slate-600 text-slate-200 text-sm font-semibold py-2 px-4 rounded-lg transition-colors flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Changer de mode
              </button>
              
              <h2 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-indigo-500">
                Analyse Complète en 14 Étapes
              </h2>
            </div>

            {/* Message d'information sur l'authentification si requise */}
            {requiresAuth && (
              <div className="bg-amber-500/10 border border-amber-500/20 rounded-lg p-4 mb-6 max-w-2xl">
                <div className="flex items-start gap-3">
                  <span className="text-amber-400 text-lg">🔑</span>
                  <div className="text-left">
                    <h4 className="text-amber-300 font-medium mb-1">Authentification requise</h4>
                    <p className="text-amber-200 text-sm">
                      Pour utiliser Roony, vous devez vous connecter avec votre clé API OpenRouter personnelle.
                      Cela vous donne accès aux modèles IA les plus performants avec vos propres crédits.
                    </p>
                    <button
                      onClick={() => setShowSimplePremiumModal(true)}
                      className="mt-2 text-amber-300 hover:text-amber-200 text-sm underline transition-colors"
                    >
                      Se connecter maintenant →
                    </button>
                  </div>
                </div>
              </div>
            )}
            
            <CompleteAnalysisInterface
              onSubmit={requiresAuth ? () => {} : handleStartFullAnalysis}
              isProcessing={isProcessing || requiresAuth}
            />
          </div>
        )}
      </main>
      <SlideFooter />

      {/* Modal des statistiques API */}
      <ApiKeyStats
        isVisible={showApiStats}
        onClose={() => setShowApiStats(false)}
      />

      {/* Notification de sauvegarde */}
      <BackupNotification />

      {/* Modal de sélection du mode d'utilisation */}
      {showModeModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-slate-800/95 border border-slate-600 rounded-2xl p-6 max-w-md w-full mx-4 shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-white">🎯 Mode d'utilisation</h3>
              <button
                onClick={() => setShowModeModal(false)}
                className="text-slate-400 hover:text-white transition-colors"
              >
                ✕
              </button>
            </div>

            <ModeToggle
              currentMode={usageMode}
              onModeChange={handleModeChange}
              disabled={isProcessing}
            />

            {showPremiumAuth && (
              <div className="mt-4">
                <PremiumAuth
                  onAuthSuccess={() => {
                    console.log('🌟 Authentification Premium réussie !');
                    setShowPremiumAuth(false);
                    setShowModeModal(false);
                  }}
                  onAuthError={(error) => {
                    console.error('❌ Erreur authentification Premium:', error);
                    setError(error);
                  }}
                />
              </div>
            )}
          </div>
        </div>
      )}

      {/* Modal simple d'authentification Premium */}
      <SimplePremiumModal
        isOpen={showSimplePremiumModal}
        onClose={() => {
          // Ne permettre la fermeture que si l'authentification n'est pas requise
          if (!requiresAuth) {
            setShowSimplePremiumModal(false);
          }
        }}
        onSuccess={() => {
          console.log('🌟 Authentification Premium réussie via modal simple !');
          setRequiresAuth(false);
          setShowSimplePremiumModal(false);
        }}
        onFreeMode={handleFreeModeChoice}
        isRequired={requiresAuth}
      />

      {/* Mascotte Roony contextuelle pour les animations de réflexion - Positionnée en bas à droite mais invisible par défaut */}
      <RoonyContextualMascot
        ref={roonyRef}
        position="fixed"
        size={120}
        customPosition={{ bottom: 80, right: 20 }}
        onAnimationComplete={(animation) => {
          console.log(`Animation Roony terminée: ${animation}`);
        }}
      />
      </div>
    </NotificationManager>
  );
};

export default App;