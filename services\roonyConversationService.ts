import { sendMessageToAI } from './geminiService';
import type { MissionContext } from '../types';

export interface ConversationContext {
  stage: 'welcome' | 'personnage' | 'role' | 'objectif' | 'format' | 'synthesis' | 'ready';
  collectedData: {
    personnage?: string;
    role?: string;
    objectif?: string;
    format?: string;
  };
  conversation: Array<{sender: 'user' | 'ai', text: string}>;
}

// Nouvelle fonction pour convertir les données collectées en MissionContext
export function buildMissionContext(collectedData: ConversationContext['collectedData']): MissionContext | null {
  if (!collectedData.personnage || !collectedData.objectif || !collectedData.role || !collectedData.format) {
    return null;
  }
  
  // Extraire les contraintes de l'objectif (souvent les "lignes rouges" sont mentionnées avec l'objectif)
  const objectifParts = collectedData.objectif.split(/(?:lignes rouges|contraintes|interdictions|limites)/i);
  const objectifPrincipal = objectifParts[0].trim();
  const contraintes = objectifParts.length > 1 ? objectifParts.slice(1).join(' ').trim() : 'Aucune contrainte spécifique mentionnée';
  
  return {
    personnage: collectedData.personnage,
    objectif: objectifPrincipal,
    contraintes: contraintes,
    roleAgent: collectedData.role,
    formatSortie: collectedData.format
  };
}

const ROONEY_SYSTEM_PROMPT = `Tu es Rooney, un consultant expert en résolution de problèmes complexes. Tu mènes un entretien stratégique conversationnel pour collecter les informations selon la méthodologie P.R.O.F.

RÈGLES STRICTES :
1. Tu poses TOUJOURS une seule question à la fois
2. Tu accuses réception de chaque réponse avant de passer à la suite
3. Tu restes naturel et conversationnel, jamais robotique
4. Tu suis EXACTEMENT la séquence définie sans la dévier
5. Tu NE DONNES JAMAIS de conseil avant d'avoir terminé la collecte P.R.O.F.
6. Tu ne recommences JAMAIS depuis le début - continue toujours là où tu en étais

SÉQUENCE OBLIGATOIRE selon l'étape en cours :

Si ÉTAPE "welcome" :
Dis : "Bonjour. Je suis Rooney. Pour vous fournir l'analyse la plus pertinente possible, nous allons d'abord discuter de votre situation pendant quelques instants. Pour commencer, décrivez-moi simplement le problème ou la situation que vous souhaitez que j'analyse."

Si ÉTAPE "personnage" :
Après avoir reçu la description du problème, tu dis : "Merci pour cette description. C'est très clair. Pour aller plus loin et bien cerner tous les enjeux, j'aurais besoin de quelques précisions. Pourriez-vous me parler un peu de la personne qui est au cœur de ce problème ? Quel est son rôle, sa situation, peut-être même son état d'esprit actuel ?"

Si ÉTAPE "objectif" :
Après avoir reçu les infos sur le personnage, tu dis : "Compris. Merci pour ces détails importants. Maintenant, quel serait, pour vous, le résultat idéal ? Si tout se passait parfaitement, à quoi ressemblerait le succès ? Et à l'inverse, y a-t-il des limites à ne pas franchir, des 'lignes rouges' absolues ?"

Si ÉTAPE "role" :
Après avoir reçu les objectifs, tu dis : "Merci pour ces précisions. Pour vous conseiller au mieux, quelle posture dois-je adopter ? Souhaitez-vous que j'agisse principalement comme un avocat cherchant à minimiser les risques, un stratège cherchant des opportunités, ou peut-être autre chose ?"

Si ÉTAPE "format" :
Après avoir reçu le rôle, tu dis : "Une dernière question pratique : une fois mon analyse terminée, sous quelle forme préférez-vous recevoir mes conclusions ? Un plan d'action direct et concis ? Un rapport très détaillé avec plusieurs scénarios ? Ou autre chose ?"

Si ÉTAPE "synthesis" :
Après avoir reçu le format, tu fais une synthèse complète avec TOUTES les données collectées et tu demandes confirmation.

IMPORTANT: Adapte tes réponses selon l'étape en cours et les données déjà collectées.`;

export class RoonyConversationService {
  
  async processUserMessage(
    userMessage: string, 
    context: ConversationContext
  ): Promise<{
    roonyResponse: string;
    nextStage: ConversationContext['stage'];
    updatedData: ConversationContext['collectedData'];
  }> {
    
    // Préparer le contexte pour l'IA
    const systemPrompt = this.buildSystemPrompt(context);
    const conversationHistory = context.conversation.map(msg => ({
      role: msg.sender === 'user' ? 'user' as const : 'assistant' as const,
      content: msg.text
    }));

    // Ajouter le message utilisateur actuel
    conversationHistory.push({
      role: 'user',
      content: userMessage
    });

    const messages = [
      { role: 'system' as const, content: systemPrompt },
      ...conversationHistory
    ];

    try {
      // Appeler l'IA
      const { content: roonyResponse } = await sendMessageToAI(messages, 'génération');
      
      // Déterminer la prochaine étape et les données mises à jour
      const { nextStage, updatedData } = this.analyzeResponse(context, userMessage, roonyResponse);
      
      return {
        roonyResponse,
        nextStage,
        updatedData
      };
      
    } catch (error) {
      console.error('Erreur lors de la communication avec Rooney:', error);
      throw new Error('Erreur de communication avec Rooney. Veuillez réessayer.');
    }
  }

  private buildSystemPrompt(context: ConversationContext): string {
    let prompt = ROONEY_SYSTEM_PROMPT;

    // Ajouter le contexte de l'étape actuelle
    prompt += `\n\nÉTAPE ACTUELLE: "${context.stage}"`;

    // Ajouter les données déjà collectées si disponibles
    if (Object.keys(context.collectedData).length > 0) {
      prompt += '\n\nDONNÉES DÉJÀ COLLECTÉES:';
      if (context.collectedData.personnage) {
        prompt += `\n- Personnage & Contexte: ${context.collectedData.personnage}`;
      }
      if (context.collectedData.objectif) {
        prompt += `\n- Objectifs & Contraintes: ${context.collectedData.objectif}`;
      }
      if (context.collectedData.role) {
        prompt += `\n- Rôle d'Expert: ${context.collectedData.role}`;
      }
      if (context.collectedData.format) {
        prompt += `\n- Format de Sortie: ${context.collectedData.format}`;
      }
    }

    // IMPORTANT : Éviter la réinjection de mémoire selon le stage
    // Le stage sera utilisé par les services de mémoire pour éviter les répétitions
    console.log(`🎯 [Rooney] Stage actuel pour mémoire: ${context.stage}`);

    return prompt;
  }

  private analyzeResponse(
    context: ConversationContext, 
    userMessage: string, 
    roonyResponse: string
  ): {
    nextStage: ConversationContext['stage'];
    updatedData: ConversationContext['collectedData'];
  } {
    
    const updatedData = { ...context.collectedData };
    let nextStage = context.stage;

    console.log(`🎯 Analyse étape: ${context.stage} -> Message utilisateur: ${userMessage.substring(0, 50)}...`);

    switch (context.stage) {
      case 'welcome':
        // L'utilisateur a décrit son problème, on passe au personnage
        console.log('✅ Problème décrit, passage au personnage');
        nextStage = 'personnage';
        break;

      case 'personnage':
        // L'utilisateur a donné des infos sur le personnage
        console.log('✅ Personnage décrit, sauvegarde et passage aux objectifs');
        updatedData.personnage = userMessage;
        nextStage = 'objectif';
        break;

      case 'objectif':
        // L'utilisateur a défini les objectifs
        console.log('✅ Objectifs définis, sauvegarde et passage au rôle');
        updatedData.objectif = userMessage;
        nextStage = 'role';
        break;

      case 'role':
        // L'utilisateur a choisi le rôle d'expert
        console.log('✅ Rôle défini, sauvegarde et passage au format');
        updatedData.role = userMessage;
        nextStage = 'format';
        break;

      case 'format':
        // L'utilisateur a choisi le format
        console.log('✅ Format défini, sauvegarde et passage à la synthèse');
        updatedData.format = userMessage;
        nextStage = 'synthesis';
        break;

      case 'synthesis':
        // Analyser si l'utilisateur confirme ou demande des ajustements
        const lowerMessage = userMessage.toLowerCase();
        const confirmationWords = ['oui', 'exact', 'parfait', 'correct', 'c\'est bon', 'd\'accord', 'ok', 'valide', 'confirme'];
        const isConfirmation = confirmationWords.some(word => lowerMessage.includes(word));
        
        if (isConfirmation) {
          console.log('✅ Synthèse confirmée, passage au lancement');
          nextStage = 'ready';
        } else {
          console.log('⚠️ Synthèse non confirmée, reste en synthèse pour ajustements');
          nextStage = 'synthesis';
        }
        break;

      case 'ready':
        // Étape finale - ne change plus
        console.log('🏁 Étape finale atteinte');
        nextStage = 'ready';
        break;

      default:
        console.warn(`⚠️ Étape inconnue: ${context.stage}`);
        break;
    }

    console.log(`🔄 Transition: ${context.stage} -> ${nextStage}`);
    console.log(`📝 Données collectées:`, updatedData);

    return { nextStage, updatedData };
  }

  // Nouvelle méthode pour obtenir le contexte de mission complet
  getMissionContext(context: ConversationContext): MissionContext | null {
    return buildMissionContext(context.collectedData);
  }

  getInitialMessage(): string {
    return "Bonjour. Je suis Rooney. Pour vous fournir l'analyse la plus pertinente possible, nous allons d'abord discuter de votre situation pendant quelques instants. Pour commencer, décrivez-moi simplement le problème ou la situation que vous souhaitez que j'analyse.";
  }

  getStageTitle(stage: ConversationContext['stage']): string {
    switch (stage) {
      case 'welcome': return 'Présentation du problème';
      case 'personnage': return 'Personnage & Contexte';
      case 'objectif': return 'Objectifs & Contraintes';
      case 'role': return 'Rôle d\'expert';
      case 'format': return 'Format de sortie';
      case 'synthesis': return 'Synthèse et validation';
      case 'ready': return 'Lancement de l\'analyse';
      default: return 'Entretien en cours';
    }
  }

  getStageProgress(stage: ConversationContext['stage']): number {
    const stages: ConversationContext['stage'][] = ['welcome', 'personnage', 'objectif', 'role', 'format', 'synthesis', 'ready'];
    return Math.round((stages.indexOf(stage) / (stages.length - 1)) * 100);
  }
}

export const roonyConversationService = new RoonyConversationService();
