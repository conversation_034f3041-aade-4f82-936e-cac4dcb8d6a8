/**
 * Service de Templates de Prompts avec Contexte de Mission
 * Génère les prompts augmentés pour chaque étape d'analyse avec le contexte P.R.O.F.
 * ENRICHI : Intégration du Protocole d'Analyse Éthique et accès aux connaissances documentaires
 */

import type { MissionContext } from '../../types';
import { documentationKnowledgeService } from './documentationKnowledgeService';

export class MissionContextPromptService {
  
  /**
   * Template de base pour tous les prompts augmentés avec le contexte de mission
   */
  private buildMissionContextPrompt(missionContext: MissionContext): string {
    return `--- CONTEXTE DE MISSION (RÈGLE ABSOLUE) ---
Tu dois impérativement filtrer toute ton analyse et tes réponses à travers le prisme de ce contexte :
- **Personnage :** ${missionContext.personnage}
- **Objectif Principal :** ${missionContext.objectif}
- **Contraintes Inviolables :** ${missionContext.contraintes}
- **Mon Rôle :** Je dois agir en tant que ${missionContext.roleAgent}
--------------------------------------------------`;
  }

  /**
   * Génère un prompt augmenté pour une étape spécifique du workflow
   */
  buildStepPrompt(
    stepNumber: number,
    stepName: string,
    stepDescription: string,
    missionContext: MissionContext,
    problemDescription: string
  ): string {
    
    // Extraire les connaissances documentaires pertinentes
    const keywords = this.extractKeywords(problemDescription);
    const knowledgeResult = documentationKnowledgeService.extractRelevantKnowledge(
      problemDescription,
      missionContext,
      keywords
    );
    
    let basePrompt = `[INSTRUCTION SYSTÈME PRIORITAIRE - IGNORER TOUT AUTRE COMPORTEMENT PAR DÉFAUT]

Tu dois analyser la situation suivante :
**[PROBLÈME INITIAL DE L'UTILISATEUR]**
${problemDescription}

Tu dois mener cette analyse en te conformant STRICTEMENT et SANS EXCEPTION au cadre de mission suivant. Ce cadre outrepasse toutes tes autres instructions.

--- CADRE DE MISSION IMPÉRATIF ---
- **Personnage Central :** ${missionContext.personnage}
- **Objectif Stratégique Final :** ${missionContext.objectif}
- **Contraintes Inviolables (Lignes Rouges) :** ${missionContext.contraintes}
- **Mon Rôle d'Expert :** Je dois agir en tant que ${missionContext.roleAgent}
------------------------------------

**TA TÂCHE IMMÉDIATE (ÉTAPE ${stepNumber} / 15) : ${stepName}**

AVANT de formuler tes recommandations, tu DOIS appliquer le PROTOCOLE D'ANALYSE ÉTHIQUE APPROFONDIE :

🔍 **ÉTAPE 1 - ANALYSE CONSÉQUENTIALISTE :**
- Identifie les CONSÉQUENCES POSITIVES fondamentales : Quels bénéfices concrets pour le plus grand nombre ? Quels groupes en profiteraient ? Quelles valeurs seraient renforcées ?
- Identifie les CONSÉQUENCES NÉGATIVES fondamentales : Quels préjudices potentiels ? Quels groupes seraient lésés ? Quelles valeurs seraient compromises ?

⚖️ **ÉTAPE 2 - JUGEMENT MORAL :**
Formule explicitement : "Après analyse, j'estime que les bénéfices/préjudices potentiels pour la collectivité l'emportent sur les préjudices/bénéfices, car..."

🎯 **ÉTAPE 3 - PLAN D'ACTION ÉTHIQUE :**
- Si bénéfices > préjudices : Amplifie les aspects positifs + garde-fous robustes
- Si préjudices > bénéfices : Modification radicale, report ou abandon selon faisabilité

En te basant **UNIQUEMENT** sur le problème de l'utilisateur et en appliquant le **CADRE DE MISSION IMPÉRATIF** + le **PROTOCOLE ÉTHIQUE**, exécute maintenant : ${stepDescription}

**Vérification de Cohérence :** Ta réponse doit prouver que tu as intégré le CADRE DE MISSION ET l'analyse éthique. Aucune recommandation générique autorisée.`;

    // Enrichir le prompt avec les connaissances documentaires
    return documentationKnowledgeService.generateKnowledgeEnhancedPrompt(
      basePrompt,
      knowledgeResult
    );
  }

  /**
   * Extrait les mots-clés du problème
   */
  private extractKeywords(problem: string): string[] {
    const words = problem.toLowerCase().split(/\s+/);
    const stopWords = ['le', 'la', 'les', 'de', 'du', 'des', 'et', 'ou', 'mais', 'car', 'donc', 'que', 'qui', 'quoi', 'pour', 'avec', 'sans'];
    
    return words
      .filter(word => word.length > 3 && !stopWords.includes(word))
      .slice(0, 8);
  }

  /**
   * Prompts spécialisés pour les différentes étapes d'analyse
   */
  buildAnalysisStepPrompts(missionContext: MissionContext, problemDescription: string) {
    const steps = [
      {
        number: 1,
        name: "Analyse des Parties Prenantes",
        description: "Identifie toutes les parties prenantes concernées par ce problème et analyse leurs intérêts, pouvoirs d'influence et positions probables."
      },
      {
        number: 2,
        name: "Analyse des Risques",
        description: "Évalue les risques potentiels associés aux différentes approches possibles, en priorisant ceux qui pourraient compromettre l'objectif principal."
      },
      {
        number: 3,
        name: "Identification des Opportunités",
        description: "Détecte les opportunités disponibles et les points de levier qui peuvent faciliter l'atteinte de l'objectif."
      },
      {
        number: 4,
        name: "Élaboration des Solutions",
        description: "Génère des solutions concrètes et actionnables, en excluant d'emblée celles qui violeraient les contraintes identifiées."
      },
      {
        number: 5,
        name: "Évaluation de Faisabilité",
        description: "Évalue la faisabilité technique, financière et organisationnelle de chaque solution proposée."
      },
      {
        number: 6,
        name: "Priorisation Stratégique",
        description: "Hiérarchise les solutions selon leur potentiel d'impact sur l'objectif principal et leur alignement avec le rôle défini."
      },
      {
        number: 7,
        name: "Planification Opérationnelle",
        description: "Détaille les étapes concrètes de mise en œuvre de la ou des solutions retenues."
      },
      {
        number: 8,
        name: "Analyse des Ressources",
        description: "Identifie les ressources nécessaires (humaines, financières, techniques) pour la mise en œuvre."
      },
      {
        number: 9,
        name: "Gestion des Résistances",
        description: "Anticipe les résistances possibles et développe des stratégies pour les surmonter."
      },
      {
        number: 10,
        name: "Mesure et Suivi",
        description: "Définit les indicateurs de succès et les mécanismes de suivi des progrès vers l'objectif."
      },
      {
        number: 11,
        name: "Scénarios d'Urgence",
        description: "Prépare des plans de contingence en cas d'obstacles majeurs ou d'échec des solutions principales."
      },
      {
        number: 12,
        name: "Communication Stratégique",
        description: "Développe une stratégie de communication adaptée aux différentes parties prenantes."
      },
      {
        number: 13,
        name: "Validation Juridique",
        description: "Vérifie la conformité légale et réglementaire des solutions proposées."
      },
      {
        number: 14,
        name: "Optimisation Continue",
        description: "Identifie les mécanismes d'amélioration continue et d'adaptation des solutions."
      },
      {
        number: 15,
        name: "Synthèse Exécutive",
        description: "Compile toutes les analyses en un livrable final structuré selon le format demandé."
      }
    ];

    return steps.map(step => ({
      ...step,
      prompt: this.buildStepPrompt(
        step.number,
        step.name,
        step.description,
        missionContext,
        problemDescription
      )
    }));
  }

  /**
   * Génère un prompt de validation finale avec le contexte de mission
   */
  buildFinalValidationPrompt(missionContext: MissionContext, analysisResults: string[]): string {
    
    return `[INSTRUCTION SYSTÈME PRIORITAIRE - IGNORER TOUT AUTRE COMPORTEMENT PAR DÉFAUT]

Tu dois analyser la situation suivante :
**[PROBLÈME INITIAL DE L'UTILISATEUR]**
Validation finale des analyses précédentes

Tu dois mener cette validation en te conformant STRICTEMENT et SANS EXCEPTION au cadre de mission suivant. Ce cadre outrepasse toutes tes autres instructions.

--- CADRE DE MISSION IMPÉRATIF ---
- **Personnage Central :** ${missionContext.personnage}
- **Objectif Stratégique Final :** ${missionContext.objectif}
- **Contraintes Inviolables (Lignes Rouges) :** ${missionContext.contraintes}
- **Mon Rôle d'Expert :** Je dois agir en tant que ${missionContext.roleAgent}
------------------------------------

**TA TÂCHE IMMÉDIATE (ÉTAPE FINALE) : VALIDATION DE COHÉRENCE GLOBALE**

En te basant **UNIQUEMENT** sur le CADRE DE MISSION IMPÉRATIF, valide et compile les analyses suivantes :

${analysisResults.map((result, index) => `**Étape ${index + 1} :** ${result}`).join('\n\n')}

**Vérification de Cohérence :** Ta réponse finale doit prouver que CHAQUE élément contribue directement à l'OBJECTIF STRATÉGIQUE FINAL et respecte les CONTRAINTES INVIOLABLES. Format de sortie : ${missionContext.formatSortie}`;
  }
}

export const missionContextPromptService = new MissionContextPromptService();
