/**
 * Script de validation finale du système de mise à jour automatique
 * Studio Agentique Roony - Août 2025
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function validateCompleteSystem() {
    console.log('🔍 Validation complète du système de mise à jour automatique');
    console.log('='.repeat(80));

    const validationResults = {
        services: {},
        components: {},
        scripts: {},
        configuration: {},
        integration: {}
    };

    try {
        // 1. Validation des services
        console.log('\n1. 🛠️ Validation des services');
        
        const serviceFiles = [
            'autoModelUpdateService.ts',
            'modelDetectionService.ts',
            'premiumApiService.ts'
        ];

        for (const file of serviceFiles) {
            const filePath = path.join(__dirname, '..', 'services', file);
            try {
                const content = await fs.readFile(filePath, 'utf-8');
                
                const checks = {
                    hasAutoUpdate: content.includes('AUTO_UPDATE'),
                    hasClassification: content.includes('classifyModel'),
                    hasValidation: content.includes('validate'),
                    hasErrorHandling: content.includes('try') && content.includes('catch')
                };

                validationResults.services[file] = {
                    exists: true,
                    checks: checks,
                    status: Object.values(checks).every(Boolean) ? 'PASS' : 'PARTIAL'
                };

                console.log(`   ${validationResults.services[file].status === 'PASS' ? '✅' : '⚠️'} ${file}`);

            } catch (error) {
                validationResults.services[file] = {
                    exists: false,
                    error: error.message,
                    status: 'FAIL'
                };
                console.log(`   ❌ ${file} - ${error.message}`);
            }
        }

        // 2. Validation des composants
        console.log('\n2. 🧩 Validation des composants');
        
        const componentFiles = [
            'ModelUpdateNotification.tsx',
            'ModelMonitor.tsx'
        ];

        for (const file of componentFiles) {
            const filePath = path.join(__dirname, '..', 'components', file);
            try {
                const content = await fs.readFile(filePath, 'utf-8');
                
                const checks = {
                    isReactComponent: content.includes('React') || content.includes('jsx'),
                    hasStateManagement: content.includes('useState') || content.includes('useEffect'),
                    hasNotifications: content.includes('notification') || content.includes('alert'),
                    hasTypeScript: content.includes('interface') || content.includes('type')
                };

                validationResults.components[file] = {
                    exists: true,
                    checks: checks,
                    status: Object.values(checks).filter(Boolean).length >= 2 ? 'PASS' : 'PARTIAL'
                };

                console.log(`   ${validationResults.components[file].status === 'PASS' ? '✅' : '⚠️'} ${file}`);

            } catch (error) {
                validationResults.components[file] = {
                    exists: false,
                    error: error.message,
                    status: 'FAIL'
                };
                console.log(`   ❌ ${file} - Non trouvé ou inaccessible`);
            }
        }

        // 3. Validation des scripts
        console.log('\n3. 📜 Validation des scripts');
        
        const scriptFiles = [
            'testImprovedModelDetection.js',
            'generateUpdatedConstants.js',
            'integrateAutoUpdate.js'
        ];

        for (const file of scriptFiles) {
            const filePath = path.join(__dirname, file);
            try {
                const content = await fs.readFile(filePath, 'utf-8');
                
                const checks = {
                    isES6Module: content.includes('import') && content.includes('export'),
                    hasAsyncSupport: content.includes('async') && content.includes('await'),
                    hasErrorHandling: content.includes('try') && content.includes('catch'),
                    hasLogging: content.includes('console.log')
                };

                validationResults.scripts[file] = {
                    exists: true,
                    checks: checks,
                    status: Object.values(checks).every(Boolean) ? 'PASS' : 'PARTIAL'
                };

                console.log(`   ${validationResults.scripts[file].status === 'PASS' ? '✅' : '⚠️'} ${file}`);

            } catch (error) {
                validationResults.scripts[file] = {
                    exists: false,
                    error: error.message,
                    status: 'FAIL'
                };
                console.log(`   ❌ ${file} - ${error.message}`);
            }
        }

        // 4. Validation de la configuration
        console.log('\n4. ⚙️ Validation de la configuration');
        
        const configFiles = [
            'constants.ts',
            'constants_updated.ts'
        ];

        for (const file of configFiles) {
            const filePath = path.join(__dirname, '..', file);
            try {
                const content = await fs.readFile(filePath, 'utf-8');
                
                const checks = {
                    hasAutoUpdateConfig: content.includes('AUTO_UPDATE'),
                    hasModelLists: content.includes('OPENROUTER_FREE_MODELS') && content.includes('OPENROUTER_PREMIUM_MODELS'),
                    hasAPIConfig: content.includes('OPENROUTER_API'),
                    hasValidation: content.includes('VALIDATION') || content.includes('CHECK')
                };

                validationResults.configuration[file] = {
                    exists: true,
                    checks: checks,
                    status: Object.values(checks).filter(Boolean).length >= 3 ? 'PASS' : 'PARTIAL'
                };

                console.log(`   ${validationResults.configuration[file].status === 'PASS' ? '✅' : '⚠️'} ${file}`);

            } catch (error) {
                validationResults.configuration[file] = {
                    exists: false,
                    error: error.message,
                    status: 'FAIL'
                };
                console.log(`   ❌ ${file} - ${error.message}`);
            }
        }

        // 5. Test d'intégration rapide
        console.log('\n5. 🧪 Test d\'intégration rapide');
        
        try {
            // Vérifier que les chemins d'importation sont cohérents
            const appTsxPath = path.join(__dirname, '..', 'App.tsx');
            const appContent = await fs.readFile(appTsxPath, 'utf-8');
            
            const integrationChecks = {
                importsServices: appContent.includes('services/') || appContent.includes('./services'),
                importsComponents: appContent.includes('components/') || appContent.includes('./components'),
                hasConstants: appContent.includes('constants') || appContent.includes('./constants'),
                hasErrorBoundary: appContent.includes('ErrorBoundary') || appContent.includes('try')
            };

            validationResults.integration.app = {
                checks: integrationChecks,
                status: Object.values(integrationChecks).filter(Boolean).length >= 2 ? 'PASS' : 'PARTIAL'
            };

            console.log(`   ${validationResults.integration.app.status === 'PASS' ? '✅' : '⚠️'} Intégration App.tsx`);

        } catch (error) {
            validationResults.integration.app = {
                error: error.message,
                status: 'FAIL'
            };
            console.log(`   ❌ Intégration App.tsx - ${error.message}`);
        }

        // 6. Génération du rapport de validation
        console.log('\n6. 📋 Génération du rapport de validation');
        
        const report = generateValidationReport(validationResults);
        const reportPath = path.join(__dirname, '..', 'validation_report.md');
        await fs.writeFile(reportPath, report);
        
        console.log(`   ✅ Rapport sauvegardé: ${path.basename(reportPath)}`);

        // 7. Résumé final
        console.log('\n7. 📊 Résumé de validation');
        
        const allResults = [
            ...Object.values(validationResults.services),
            ...Object.values(validationResults.components),
            ...Object.values(validationResults.scripts),
            ...Object.values(validationResults.configuration),
            ...Object.values(validationResults.integration)
        ];

        const passCount = allResults.filter(r => r.status === 'PASS').length;
        const partialCount = allResults.filter(r => r.status === 'PARTIAL').length;
        const failCount = allResults.filter(r => r.status === 'FAIL').length;
        const totalCount = allResults.length;

        console.log(`   ✅ PASS: ${passCount}/${totalCount}`);
        console.log(`   ⚠️ PARTIAL: ${partialCount}/${totalCount}`);
        console.log(`   ❌ FAIL: ${failCount}/${totalCount}`);

        const overallStatus = failCount === 0 && partialCount <= 2 ? 'SYSTÈME VALIDÉ' : 
                             failCount === 0 ? 'SYSTÈME PARTIELLEMENT VALIDÉ' : 'SYSTÈME NÉCESSITE DES CORRECTIONS';

        console.log(`\n🎯 ${overallStatus}`);
        
        if (overallStatus === 'SYSTÈME VALIDÉ') {
            console.log('   Le système de mise à jour automatique est prêt pour la production !');
        } else if (overallStatus === 'SYSTÈME PARTIELLEMENT VALIDÉ') {
            console.log('   Le système fonctionne mais quelques améliorations sont recommandées.');
        } else {
            console.log('   Veuillez corriger les erreurs avant de déployer en production.');
        }

        console.log('\n='.repeat(80));

        return {
            success: overallStatus !== 'SYSTÈME NÉCESSITE DES CORRECTIONS',
            results: validationResults,
            summary: {
                total: totalCount,
                pass: passCount,
                partial: partialCount,
                fail: failCount,
                status: overallStatus
            }
        };

    } catch (error) {
        console.error('❌ Erreur lors de la validation:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

function generateValidationReport(results) {
    const timestamp = new Date().toISOString();
    
    return `# Rapport de Validation - Système de Mise à Jour Automatique

**Date:** ${timestamp}
**Studio:** Agentique Roony
**Version:** 1.0.0

## Résumé Exécutif

Ce rapport présente les résultats de la validation complète du système de mise à jour automatique des modèles OpenRouter.

## Validation des Services

${Object.entries(results.services).map(([file, result]) => 
    `### ${file}
- **Statut:** ${result.status}
- **Existe:** ${result.exists ? 'Oui' : 'Non'}
${result.checks ? Object.entries(result.checks).map(([check, passed]) => 
    `- ${check}: ${passed ? '✅' : '❌'}`
).join('\n') : ''}
${result.error ? `- **Erreur:** ${result.error}` : ''}
`).join('\n')}

## Validation des Composants

${Object.entries(results.components).map(([file, result]) => 
    `### ${file}
- **Statut:** ${result.status}
- **Existe:** ${result.exists ? 'Oui' : 'Non'}
${result.checks ? Object.entries(result.checks).map(([check, passed]) => 
    `- ${check}: ${passed ? '✅' : '❌'}`
).join('\n') : ''}
${result.error ? `- **Erreur:** ${result.error}` : ''}
`).join('\n')}

## Validation des Scripts

${Object.entries(results.scripts).map(([file, result]) => 
    `### ${file}
- **Statut:** ${result.status}
- **Existe:** ${result.exists ? 'Oui' : 'Non'}
${result.checks ? Object.entries(result.checks).map(([check, passed]) => 
    `- ${check}: ${passed ? '✅' : '❌'}`
).join('\n') : ''}
${result.error ? `- **Erreur:** ${result.error}` : ''}
`).join('\n')}

## Validation de la Configuration

${Object.entries(results.configuration).map(([file, result]) => 
    `### ${file}
- **Statut:** ${result.status}
- **Existe:** ${result.exists ? 'Oui' : 'Non'}
${result.checks ? Object.entries(result.checks).map(([check, passed]) => 
    `- ${check}: ${passed ? '✅' : '❌'}`
).join('\n') : ''}
${result.error ? `- **Erreur:** ${result.error}` : ''}
`).join('\n')}

## Tests d'Intégration

${Object.entries(results.integration).map(([component, result]) => 
    `### ${component}
- **Statut:** ${result.status}
${result.checks ? Object.entries(result.checks).map(([check, passed]) => 
    `- ${check}: ${passed ? '✅' : '❌'}`
).join('\n') : ''}
${result.error ? `- **Erreur:** ${result.error}` : ''}
`).join('\n')}

## Recommandations

1. **Production Ready:** Le système est prêt pour le déploiement en production
2. **Monitoring:** Activez le monitoring automatique via cron
3. **Tests Réguliers:** Exécutez les tests de validation mensuellement
4. **Sauvegarde:** Maintenez des sauvegardes des configurations

## Prochaines Étapes

1. Déployer les nouvelles constantes
2. Activer le monitoring automatique
3. Tester en environnement de production
4. Documenter les procédures opérationnelles

---
*Rapport généré automatiquement par le système de validation*
`;
}

// Exécution
const isMainModule = import.meta.url === `file://${process.argv[1]}`;
if (isMainModule) {
    validateCompleteSystem().then(result => {
        if (result.success) {
            console.log('\n🎉 Validation terminée avec succès !');
        } else {
            console.error('\n💥 Validation échouée. Veuillez vérifier les erreurs.');
            process.exit(1);
        }
    });
}

export { validateCompleteSystem };
