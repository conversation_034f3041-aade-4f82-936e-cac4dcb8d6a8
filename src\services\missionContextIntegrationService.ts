/**
 * Service d'Intégration du Contexte de Mission
 * Connecte le framework P.R.O.F. avec le système d'analyse existant
 */

import type { MissionContext } from '../../types';
import { expertConsultantService } from './expertConsultantService';
import { roonyConversationService, buildMissionContext } from '../../services/roonyConversationService';
import type { ConversationContext } from '../../services/roonyConversationService';
import { sendMessageToAI } from '../../services/geminiService';
import { missionContextPromptService } from './missionContextPromptService';

export interface MissionContextualAnalysis {
  missionContext: MissionContext;
  stepResults: Array<{ stepNumber: number; stepName: string; result: string }>;
  finalReport: string;
  isComplete: boolean;
}

class MissionContextIntegrationService {
  
  /**
   * Exécute une analyse complète avec le contexte de mission P.R.O.F.
   */
  async executeContextualAnalysis(
    conversationContext: ConversationContext,
    problemDescription: string
  ): Promise<MissionContextualAnalysis | null> {
    
    // Construire le contexte de mission à partir des données P.R.O.F.
    const missionContext = buildMissionContext(conversationContext.collectedData);
    
    if (!missionContext) {
      throw new Error('Contexte de mission incomplet. Toutes les informations P.R.O.F. sont requises.');
    }

    // Générer les prompts contextualisés pour les 15 étapes
    const contextualizedSteps = missionContextPromptService.buildAnalysisStepPrompts(
      missionContext, 
      problemDescription
    );

    const stepResults: Array<{ stepNumber: number; stepName: string; result: string }> = [];
    
    // Exécuter chaque étape d'analyse avec le contexte de mission
    for (const step of contextualizedSteps) {
      try {
        const { content: stepResult } = await sendMessageToAI([
          { 
            role: 'system' as const, 
            content: step.prompt 
          },
          { 
            role: 'user' as const, 
            content: `Analyse le problème suivant selon l'étape ${step.number} - ${step.name}: ${problemDescription}` 
          }
        ], 'analyse');

        stepResults.push({
          stepNumber: step.number,
          stepName: step.name,
          result: stepResult
        });

      } catch (error) {
        console.error(`Erreur lors de l'étape ${step.number}:`, error);
        stepResults.push({
          stepNumber: step.number,
          stepName: step.name,
          result: `Erreur lors de l'analyse de l'étape ${step.name}. Veuillez réessayer.`
        });
      }
    }

    // Validation finale avec le contexte de mission
    const finalReport = await this.generateFinalReport(
      missionContext,
      stepResults,
      problemDescription
    );

    return {
      missionContext,
      stepResults,
      finalReport,
      isComplete: true
    };
  }

  /**
   * Génère le rapport final avec validation du contexte de mission
   */
  private async generateFinalReport(
    missionContext: MissionContext,
    stepResults: Array<{ stepNumber: number; stepName: string; result: string }>,
    problemDescription: string
  ): Promise<string> {
    
    const validationPrompt = missionContextPromptService.buildFinalValidationPrompt(
      missionContext,
      stepResults.map(step => step.result)
    );

    try {
      const { content: finalReport } = await sendMessageToAI([
        { 
          role: 'system' as const, 
          content: validationPrompt 
        },
        { 
          role: 'user' as const, 
          content: `Génère maintenant le rapport final selon le format demandé (${missionContext.formatSortie}) pour le problème: ${problemDescription}` 
        }
      ], 'synthèse');

      return finalReport;

    } catch (error) {
      console.error('Erreur lors de la génération du rapport final:', error);
      return this.generateFallbackReport(missionContext, stepResults);
    }
  }

  /**
   * Génère un rapport de fallback en cas d'erreur
   */
  private generateFallbackReport(
    missionContext: MissionContext,
    stepResults: Array<{ stepNumber: number; stepName: string; result: string }>
  ): string {
    return `
# RAPPORT D'ANALYSE CONTEXTUELLE

## CONTEXTE DE MISSION
- **Personnage concerné**: ${missionContext.personnage}
- **Objectif principal**: ${missionContext.objectif}
- **Contraintes à respecter**: ${missionContext.contraintes}
- **Rôle d'expert**: ${missionContext.roleAgent}
- **Format demandé**: ${missionContext.formatSortie}

## RÉSULTATS D'ANALYSE

${stepResults.map(step => `
### Étape ${step.stepNumber}: ${step.stepName}
${step.result}
`).join('\n')}

## SYNTHÈSE
L'analyse a été menée en respectant le contexte de mission défini. Chaque recommandation a été filtrée selon le rôle d'expert demandé (${missionContext.roleAgent}) et les contraintes spécifiées.

*Note: Ce rapport a été généré automatiquement suite à une erreur technique lors de la validation finale.*
    `.trim();
  }

  /**
   * Valide si le contexte de mission est complet
   */
  validateMissionContext(conversationContext: ConversationContext): {
    isValid: boolean;
    missingElements: string[];
    missionContext?: MissionContext;
  } {
    const missionContext = buildMissionContext(conversationContext.collectedData);
    
    if (!missionContext) {
      const missingElements: string[] = [];
      
      if (!conversationContext.collectedData.personnage) {
        missingElements.push('Personnage');
      }
      if (!conversationContext.collectedData.objectif) {
        missingElements.push('Objectif');
      }
      if (!conversationContext.collectedData.role) {
        missingElements.push('Rôle');
      }
      if (!conversationContext.collectedData.format) {
        missingElements.push('Format');
      }

      return {
        isValid: false,
        missingElements
      };
    }

    return {
      isValid: true,
      missingElements: [],
      missionContext
    };
  }

  /**
   * Obtient un résumé du contexte de mission pour affichage
   */
  getMissionContextSummary(missionContext: MissionContext): string {
    return `
🎯 **Mission définie** :
- **Situation** : ${missionContext.personnage.slice(0, 100)}${missionContext.personnage.length > 100 ? '...' : ''}
- **Objectif** : ${missionContext.objectif}
- **Mon rôle** : ${missionContext.roleAgent}
- **Format** : ${missionContext.formatSortie}
- **Contraintes** : ${missionContext.contraintes}
    `.trim();
  }
}

export const missionContextIntegrationService = new MissionContextIntegrationService();
