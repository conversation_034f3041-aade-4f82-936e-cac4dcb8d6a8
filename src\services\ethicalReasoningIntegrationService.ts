/**
 * Service d'Intégration du Protocole d'Analyse Éthique Approfondie v4.3
 * 
 * Ce service coordonne l'application du nouveau protocole de raisonnement éthique
 * et l'accès aux connaissances documentaires pour enrichir l'intelligence de Rooney.
 * 
 * Implémente les directives du fichier Tasks.md :
 * - Protocole d'Analyse Éthique Approfondie en 3 étapes
 * - Accès aux ressources documentaires Data-Docs
 * - Amélioration du raisonnement et de la réflexion de l'agent
 */

import type { MissionContext } from '../../types';
import { expertConsultantService, type EthicalAnalysis } from './expertConsultantService';
import { documentationKnowledgeService, type KnowledgeExtractionResult } from './documentationKnowledgeService';

export interface EnhancedReasoningResult {
  ethicalAnalysis: EthicalAnalysis;
  documentationInsights: KnowledgeExtractionResult;
  enhancedRecommendations: string[];
  reasoningQuality: ReasoningQuality;
}

export interface ReasoningQuality {
  ethicalDepth: number;        // Score de profondeur éthique (0-1)
  documentationRichness: number;  // Score d'enrichissement documentaire (0-1)
  coherenceLevel: number;      // Score de cohérence globale (0-1)
  practicalValue: number;      // Score de valeur pratique (0-1)
  overallScore: number;        // Score global de qualité du raisonnement (0-1)
}

class EthicalReasoningIntegrationService {
  
  /**
   * FONCTION PRINCIPALE : Application complète du protocole d'analyse éthique enrichi
   */
  performEnhancedEthicalReasoning(
    problemDescription: string,
    missionContext: MissionContext,
    currentStepResult?: string
  ): EnhancedReasoningResult {
    
    // Étape 1 : Analyse éthique approfondie selon le protocole Tasks.md
    const ethicalAnalysis = expertConsultantService.performEthicalAnalysis(
      problemDescription,
      missionContext
    );
    
    // Étape 2 : Extraction des connaissances documentaires
    const keywords = this.extractKeywords(problemDescription);
    const documentationInsights = documentationKnowledgeService.extractRelevantKnowledge(
      problemDescription,
      missionContext,
      keywords
    );
    
    // Étape 3 : Synthèse et recommandations enrichies
    const enhancedRecommendations = this.generateEnhancedRecommendations(
      ethicalAnalysis,
      documentationInsights,
      missionContext
    );
    
    // Étape 4 : Évaluation de la qualité du raisonnement
    const reasoningQuality = this.assessReasoningQuality(
      ethicalAnalysis,
      documentationInsights,
      enhancedRecommendations
    );
    
    return {
      ethicalAnalysis,
      documentationInsights,
      enhancedRecommendations,
      reasoningQuality
    };
  }

  /**
   * Génère des recommandations enrichies basées sur l'analyse éthique et documentaire
   */
  private generateEnhancedRecommendations(
    ethicalAnalysis: EthicalAnalysis,
    documentationInsights: KnowledgeExtractionResult,
    missionContext: MissionContext
  ): string[] {
    
    const recommendations: string[] = [];
    
    // Recommandations basées sur le jugement moral
    if (ethicalAnalysis.moralJudgment.balanceAssessment === 'benefits_outweigh_harms') {
      recommendations.push(
        `✅ RECOMMANDATION ÉTHIQUE PRINCIPALE : ${ethicalAnalysis.moralJudgment.ethicalConclusion}`
      );
      
      recommendations.push(
        `🎯 PLAN D'AMPLIFICATION : ${ethicalAnalysis.strategicActionPlan.specificActions.join(', ')}`
      );
      
      recommendations.push(
        `🛡️ GARDE-FOUS NÉCESSAIRES : ${ethicalAnalysis.strategicActionPlan.safeguards.join(', ')}`
      );
    } else {
      recommendations.push(
        `⚠️ RECOMMANDATION ÉTHIQUE CRITIQUE : ${ethicalAnalysis.moralJudgment.ethicalConclusion}`
      );
      
      const approachText = this.getApproachDescription(ethicalAnalysis.strategicActionPlan.approachType);
      recommendations.push(
        `🔄 APPROCHE RECOMMANDÉE : ${approachText}`
      );
    }
    
    // Recommandations basées sur les techniques documentaires
    if (documentationInsights.promptingTechniques.length > 0) {
      const topTechnique = documentationInsights.promptingTechniques[0];
      recommendations.push(
        `🔧 TECHNIQUE RECOMMANDÉE : Appliquer "${topTechnique.title}" - ${topTechnique.content}`
      );
    }
    
    if (documentationInsights.applicableTemplates.length > 0) {
      const topTemplate = documentationInsights.applicableTemplates[0];
      recommendations.push(
        `📋 TEMPLATE SUGGÉRÉ : Utiliser "${topTemplate.title}" pour structurer l'approche`
      );
    }
    
    // Recommandation de suivi
    recommendations.push(
      `📊 SUIVI RECOMMANDÉ : ${ethicalAnalysis.strategicActionPlan.monitoringMechanisms.join(' | ')}`
    );
    
    return recommendations;
  }

  /**
   * Évalue la qualité du raisonnement selon plusieurs dimensions
   */
  private assessReasoningQuality(
    ethicalAnalysis: EthicalAnalysis,
    documentationInsights: KnowledgeExtractionResult,
    recommendations: string[]
  ): ReasoningQuality {
    
    // Score de profondeur éthique
    const ethicalDepth = this.calculateEthicalDepth(ethicalAnalysis);
    
    // Score d'enrichissement documentaire
    const documentationRichness = Math.min(documentationInsights.totalRelevanceScore / 3, 1.0);
    
    // Score de cohérence
    const coherenceLevel = this.calculateCoherence(ethicalAnalysis, recommendations);
    
    // Score de valeur pratique
    const practicalValue = this.calculatePracticalValue(recommendations);
    
    // Score global
    const overallScore = (ethicalDepth + documentationRichness + coherenceLevel + practicalValue) / 4;
    
    return {
      ethicalDepth,
      documentationRichness,
      coherenceLevel,
      practicalValue,
      overallScore
    };
  }

  /**
   * Calcule la profondeur de l'analyse éthique
   */
  private calculateEthicalDepth(ethicalAnalysis: EthicalAnalysis): number {
    let score = 0;
    
    // Vérifier la richesse de l'analyse conséquentialiste
    const positiveCount = ethicalAnalysis.consequentialistAnalysis.positiveConsequences.fundamentalBenefits.length;
    const negativeCount = ethicalAnalysis.consequentialistAnalysis.negativeConsequences.fundamentalBenefits.length;
    
    if (positiveCount >= 3 && negativeCount >= 2) score += 0.4;
    else if (positiveCount >= 2 && negativeCount >= 1) score += 0.2;
    
    // Vérifier la qualité du jugement moral
    if (ethicalAnalysis.moralJudgment.reasoning.length >= 3) score += 0.3;
    if (ethicalAnalysis.moralJudgment.ethicalConclusion.length > 50) score += 0.2;
    
    // Vérifier la richesse du plan d'action
    if (ethicalAnalysis.strategicActionPlan.specificActions.length >= 3) score += 0.1;
    
    return Math.min(score, 1.0);
  }

  /**
   * Calcule la cohérence entre l'analyse et les recommandations
   */
  private calculateCoherence(ethicalAnalysis: EthicalAnalysis, recommendations: string[]): number {
    let score = 0.5; // Score de base
    
    // Vérifier la cohérence entre jugement et plan d'action
    const hasEthicalRecommendation = recommendations.some(rec => 
      rec.includes('ÉTHIQUE') && rec.includes(ethicalAnalysis.moralJudgment.balanceAssessment === 'benefits_outweigh_harms' ? 'PRINCIPALE' : 'CRITIQUE')
    );
    
    if (hasEthicalRecommendation) score += 0.3;
    
    // Vérifier la présence de mesures de suivi
    const hasMonitoring = recommendations.some(rec => rec.includes('SUIVI'));
    if (hasMonitoring) score += 0.2;
    
    return Math.min(score, 1.0);
  }

  /**
   * Calcule la valeur pratique des recommandations
   */
  private calculatePracticalValue(recommendations: string[]): number {
    let score = 0;
    
    // Vérifier la présence de différents types de recommandations
    const hasAction = recommendations.some(rec => rec.includes('PLAN') || rec.includes('APPROCHE'));
    const hasTechnique = recommendations.some(rec => rec.includes('TECHNIQUE'));
    const hasTemplate = recommendations.some(rec => rec.includes('TEMPLATE'));
    const hasSafeguards = recommendations.some(rec => rec.includes('GARDE-FOUS'));
    
    if (hasAction) score += 0.3;
    if (hasTechnique) score += 0.2;
    if (hasTemplate) score += 0.2;
    if (hasSafeguards) score += 0.3;
    
    return Math.min(score, 1.0);
  }

  /**
   * Génère un rapport de qualité du raisonnement
   */
  generateReasoningQualityReport(quality: ReasoningQuality): string {
    const getQualityLevel = (score: number): string => {
      if (score >= 0.8) return 'Excellent';
      if (score >= 0.6) return 'Bon';
      if (score >= 0.4) return 'Satisfaisant';
      return 'À améliorer';
    };
    
    return `
📊 **ÉVALUATION DE LA QUALITÉ DU RAISONNEMENT**

🧠 **Profondeur Éthique :** ${(quality.ethicalDepth * 100).toFixed(0)}% - ${getQualityLevel(quality.ethicalDepth)}
📚 **Richesse Documentaire :** ${(quality.documentationRichness * 100).toFixed(0)}% - ${getQualityLevel(quality.documentationRichness)}
🔗 **Cohérence Globale :** ${(quality.coherenceLevel * 100).toFixed(0)}% - ${getQualityLevel(quality.coherenceLevel)}
⚡ **Valeur Pratique :** ${(quality.practicalValue * 100).toFixed(0)}% - ${getQualityLevel(quality.practicalValue)}

🎯 **SCORE GLOBAL :** ${(quality.overallScore * 100).toFixed(0)}% - ${getQualityLevel(quality.overallScore)}
    `.trim();
  }

  /**
   * Méthodes utilitaires
   */
  private extractKeywords(problem: string): string[] {
    const words = problem.toLowerCase().split(/\s+/);
    const stopWords = ['le', 'la', 'les', 'de', 'du', 'des', 'et', 'ou', 'mais', 'car', 'donc', 'que', 'qui', 'quoi'];
    
    return words
      .filter(word => word.length > 3 && !stopWords.includes(word))
      .slice(0, 10);
  }

  private getApproachDescription(approachType: string): string {
    switch (approachType) {
      case 'radical_modification':
        return 'Modification radicale de l\'approche pour inverser la balance éthique';
      case 'postponement':
        return 'Report de l\'initiative pour développer des mesures de protection';
      case 'abandonment':
        return 'Abandon de l\'initiative dans sa forme actuelle';
      default:
        return 'Amplification des bénéfices avec garde-fous robustes';
    }
  }
}

// Instance singleton
export const ethicalReasoningIntegrationService = new EthicalReasoningIntegrationService();
