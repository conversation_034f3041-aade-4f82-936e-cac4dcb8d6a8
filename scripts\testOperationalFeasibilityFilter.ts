/**
 * Script de test pour le Principe 5 - Filtre de Faisabilité Opérationnelle
 * 
 * Ce script teste les 3 questions critiques sur des exemples concrets
 * pour valider le bon fonctionnement du filtre.
 */

import { operationalFeasibilityFilter, type ActionToValidate } from '../src/services/operationalFeasibilityFilter';

// Cas de test 1 : Problème de séquençage
const testSequencingIssue = (): void => {
  console.log('\n🔍 TEST 1 : Problème de Séquençage');
  console.log('=' .repeat(50));
  
  const actionsWithSequencingIssue: ActionToValidate[] = [
    {
      title: "Présentation des résultats",
      description: "Présenter le rapport final à la direction",
      timeline: "1 semaine",
      priority: "HIGH",
      order: 1,
      context: {
        domain: "business",
        userSituation: "Analyse financière en cours"
      }
    },
    {
      title: "Audit financier",
      description: "Effectuer l'audit complet des finances",
      timeline: "2 semaines", 
      priority: "HIGH",
      order: 2,
      context: {
        domain: "business",
        userSituation: "Analyse financière en cours"
      }
    }
  ];
  
  const results = operationalFeasibilityFilter.validateActionPlan(actionsWithSequencingIssue);
  const report = operationalFeasibilityFilter.generateFeasibilityReport(actionsWithSequencingIssue);
  
  console.log('Résultats de validation :');
  results.forEach((result, index) => {
    console.log(`  Action ${index + 1}: ${result.isValid ? '✅ Valide' : '❌ Problème'} (Confiance: ${result.confidence})`);
    if (result.issues.length > 0) {
      result.issues.forEach(issue => {
        console.log(`    - ${issue.type}: ${issue.description}`);
      });
    }
    if (result.improvedAction) {
      console.log(`    ⚡ Amélioration: ${result.improvedAction}`);
    }
  });
  
  console.log('\n' + report);
};

// Cas de test 2 : Problème juridique/culturel
const testLegalCulturalIssue = (): void => {
  console.log('\n🔍 TEST 2 : Problème Juridique/Culturel');
  console.log('=' .repeat(50));
  
  const actionsWithLegalIssue: ActionToValidate[] = [
    {
      title: "Réunion commune internationale",
      description: "Organiser une réunion commune France-Allemagne-Belgique",
      timeline: "2 semaines",
      priority: "HIGH",
      order: 1,
      context: {
        domain: "immigration",
        countries: ["france", "allemagne", "belgique"],
        userSituation: "Dossier multi-juridictionnel"
      }
    },
    {
      title: "Notification administrative",
      description: "Effectuer la notification obligatoire aux autorités",
      timeline: "1 semaine",
      priority: "MEDIUM",
      order: 2,
      context: {
        domain: "immigration",
        countries: ["france", "allemagne"],
        userSituation: "Changement de statut"
      }
    }
  ];
  
  const results = operationalFeasibilityFilter.validateActionPlan(actionsWithLegalIssue);
  
  console.log('Résultats de validation :');
  results.forEach((result, index) => {
    console.log(`  Action ${index + 1}: ${result.isValid ? '✅ Valide' : '❌ Problème'} (Confiance: ${result.confidence})`);
    if (result.issues.length > 0) {
      result.issues.forEach(issue => {
        console.log(`    - ${issue.type}: ${issue.description}`);
        console.log(`      Suggestion: ${issue.suggestion}`);
      });
    }
  });
};

// Cas de test 3 : Problème de réalisme temporel
const testTemporalRealismIssue = (): void => {
  console.log('\n🔍 TEST 3 : Problème de Réalisme Temporel');
  console.log('=' .repeat(50));
  
  const actionsWithTemporalIssue: ActionToValidate[] = [
    {
      title: "Négociation préfectorale",
      description: "Négocier avec la préfecture pour accélérer le traitement",
      timeline: "3 jours",
      priority: "URGENT",
      order: 1,
      context: {
        domain: "immigration",
        userSituation: "Renouvellement de titre de séjour"
      }
    },
    {
      title: "Audit juridique complet",
      description: "Effectuer un audit juridique approfondi du dossier",
      timeline: "immédiat",
      priority: "HIGH",
      order: 2,
      context: {
        domain: "immigration",
        userSituation: "Situation complexe multi-critères"
      }
    },
    {
      title: "Validation avocat",
      description: "Faire valider le dossier par un avocat spécialisé",
      timeline: "2 jours",
      priority: "IMPORTANT",
      order: 3,
      context: {
        domain: "legal",
        userSituation: "Recours administratif"
      }
    }
  ];
  
  const results = operationalFeasibilityFilter.validateActionPlan(actionsWithTemporalIssue);
  
  console.log('Résultats de validation :');
  results.forEach((result, index) => {
    console.log(`  Action ${index + 1}: ${result.isValid ? '✅ Valide' : '❌ Problème'} (Confiance: ${result.confidence})`);
    if (result.issues.length > 0) {
      result.issues.forEach(issue => {
        console.log(`    - ${issue.type}: ${issue.description}`);
        console.log(`      Suggestion: ${issue.suggestion}`);
      });
    }
    if (result.improvedAction) {
      console.log(`    ⚡ Version améliorée: ${result.improvedAction.substring(0, 100)}...`);
    }
  });
};

// Cas de test 4 : Plan d'action parfait (aucun problème)
const testPerfectActionPlan = (): void => {
  console.log('\n🔍 TEST 4 : Plan d\'Action Parfait');
  console.log('=' .repeat(50));
  
  const perfectActions: ActionToValidate[] = [
    {
      title: "Constitution du dossier",
      description: "Rassembler tous les documents nécessaires",
      timeline: "1 semaine",
      priority: "HIGH",
      order: 1,
      context: {
        domain: "immigration",
        userSituation: "Première demande de titre"
      }
    },
    {
      title: "Validation juridique",
      description: "Faire vérifier la conformité du dossier par un expert",
      timeline: "2 semaines",
      priority: "IMPORTANT",
      order: 2,
      context: {
        domain: "immigration",
        userSituation: "Première demande de titre"
      }
    },
    {
      title: "Dépôt en préfecture",
      description: "Déposer le dossier complet en préfecture",
      timeline: "1 jour",
      priority: "NORMAL",
      order: 3,
      context: {
        domain: "immigration",
        userSituation: "Première demande de titre"
      }
    }
  ];
  
  const results = operationalFeasibilityFilter.validateActionPlan(perfectActions);
  const report = operationalFeasibilityFilter.generateFeasibilityReport(perfectActions);
  
  console.log('Résultats de validation :');
  results.forEach((result, index) => {
    console.log(`  Action ${index + 1}: ${result.isValid ? '✅ Valide' : '❌ Problème'} (Confiance: ${result.confidence})`);
  });
  
  console.log('\n' + report);
};

// Fonction principale de test
const runAllTests = (): void => {
  console.log('🚀 TESTS DU PRINCIPE 5 - FILTRE DE FAISABILITÉ OPÉRATIONNELLE');
  console.log('================================================================');
  
  try {
    testSequencingIssue();
    testLegalCulturalIssue();
    testTemporalRealismIssue();
    testPerfectActionPlan();
    
    console.log('\n✅ TOUS LES TESTS TERMINÉS AVEC SUCCÈS');
    console.log('\n📊 RÉSUMÉ:');
    console.log('- Test séquençage: Détection des prérequis manquants');
    console.log('- Test juridique: Détection des problèmes multi-juridictionnels');
    console.log('- Test temporel: Détection des délais irréalistes');
    console.log('- Test parfait: Validation des plans bien construits');
    
  } catch (error) {
    console.error('❌ ERREUR LORS DES TESTS:', error);
  }
};

export {
  runAllTests,
  testSequencingIssue,
  testLegalCulturalIssue,
  testTemporalRealismIssue,
  testPerfectActionPlan
};
