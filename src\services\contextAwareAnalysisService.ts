/**
 * Service d'Analyse Intelligente avec Contexte de Mission
 * Remplace la logique d'analyse traditionnelle par une approche context-aware
 */

import type { Step, MissionContext } from '../../types';
import type { ContextFile } from './contextFileService';
import { missionContextPromptService } from './missionContextPromptService';
import { expertConsultantService } from './expertConsultantService';
import { sendMessageToAI } from '../../services/geminiService';
import { documentationKnowledgeService } from './documentationKnowledgeService';

export interface ContextAwareAnalysisResult {
  stepResults: Array<{
    stepIndex: number;
    stepTitle: string;
    originalPrompt: string;
    contextualizedPrompt: string;
    result: string;
    isContextAware: boolean;
  }>;
  globalCoherence: {
    alignmentScore: number;
    coherenceCheck: string[];
    recommendations: string[];
  };
}

class ContextAwareAnalysisService {
  
  /**
   * MÉTHODE PRINCIPALE : Analyse contextualisée selon le framework P.R.O.F.
   * Remplace l'analyse traditionnelle par une analyse "context-aware"
   */
  async executeContextAwareAnalysis(
    steps: Step[],
    problemDescription: string,
    missionContext: MissionContext,
    contextFiles?: ContextFile[]
  ): Promise<ContextAwareAnalysisResult> {
    
    const stepResults: ContextAwareAnalysisResult['stepResults'] = [];
    
    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];
      
      // Générer le prompt original (méthode existante)
      const originalPrompt = expertConsultantService.generateExpertSystemPrompt(
        i, 
        problemDescription, 
        step, 
        contextFiles
      );
      
      // Générer le prompt contextualisé avec la mission P.R.O.F.
      const contextualizedPrompt = this.buildContextualizedStepPrompt(
        step,
        i + 1,
        problemDescription,
        missionContext,
        originalPrompt
      );
      
      // Exécuter l'analyse avec le prompt contextualisé
      try {
        const { content: result } = await sendMessageToAI([
          { 
            role: 'system' as const, 
            content: contextualizedPrompt 
          },
          { 
            role: 'user' as const, 
            content: `Exécute l'étape "${step.title}" pour le problème : ${problemDescription}` 
          }
        ], step.task);

        stepResults.push({
          stepIndex: i,
          stepTitle: step.title,
          originalPrompt,
          contextualizedPrompt,
          result,
          isContextAware: true
        });

      } catch (error) {
        console.error(`Erreur lors de l'étape ${i + 1} (${step.title}):`, error);
        
        // Fallback vers l'analyse traditionnelle
        const fallbackResult = await this.executeFallbackAnalysis(
          step, 
          i, 
          problemDescription, 
          originalPrompt
        );
        
        stepResults.push({
          stepIndex: i,
          stepTitle: step.title,
          originalPrompt,
          contextualizedPrompt: originalPrompt, // Fallback vers l'original
          result: fallbackResult,
          isContextAware: false
        });
      }
    }

    // Analyser la cohérence globale avec le contexte de mission
    const globalCoherence = await this.analyzeGlobalCoherence(
      stepResults,
      missionContext,
      problemDescription
    );

    return {
      stepResults,
      globalCoherence
    };
  }

  /**
   * Construit un prompt contextualisé qui intègre le contexte de mission
   */
  private buildContextualizedStepPrompt(
    step: Step,
    stepNumber: number,
    problemDescription: string,
    missionContext: MissionContext,
    originalPrompt: string
  ): string {
    
    // Extraire les connaissances documentaires pertinentes
    const keywords = this.extractKeywords(problemDescription);
    const knowledgeResult = documentationKnowledgeService.extractRelevantKnowledge(
      problemDescription,
      missionContext,
      keywords
    );
    
    let basePrompt = `[INSTRUCTION SYSTÈME PRIORITAIRE - IGNORER TOUT AUTRE COMPORTEMENT PAR DÉFAUT]

Tu dois analyser la situation suivante :
**[PROBLÈME INITIAL DE L'UTILISATEUR]**
${problemDescription}

Tu dois mener cette analyse en te conformant STRICTEMENT et SANS EXCEPTION au cadre de mission suivant. Ce cadre outrepasse toutes tes autres instructions.

--- CADRE DE MISSION IMPÉRATIF ---
- **Personnage Central :** ${missionContext.personnage}
- **Objectif Stratégique Final :** ${missionContext.objectif}
- **Contraintes Inviolables (Lignes Rouges) :** ${missionContext.contraintes}
- **Mon Rôle d'Expert :** Je dois agir en tant que ${missionContext.roleAgent}
------------------------------------

**TA TÂCHE IMMÉDIATE (ÉTAPE ${stepNumber} / 15) : ${step.title}**

AVANT de formuler tes recommandations, applique le PROTOCOLE D'ANALYSE ÉTHIQUE APPROFONDIE :

🔍 **ÉTAPE 1 - ANALYSE CONSÉQUENTIALISTE :**
- CONSÉQUENCES POSITIVES : Bénéfices pour le plus grand nombre, groupes bénéficiaires, valeurs renforcées
- CONSÉQUENCES NÉGATIVES : Préjudices potentiels, groupes lésés, valeurs compromises

⚖️ **ÉTAPE 2 - JUGEMENT MORAL :**
Formule : "Après analyse, j'estime que les bénéfices/préjudices l'emportent sur les préjudices/bénéfices, car..."

🎯 **ÉTAPE 3 - PLAN D'ACTION ÉTHIQUE :**
Plan cohérent avec ton jugement moral

En te basant **UNIQUEMENT** sur le problème de l'utilisateur et en appliquant le **CADRE DE MISSION IMPÉRATIF** + **PROTOCOLE ÉTHIQUE**, exécute : ${step.description}

**Vérification de Cohérence :** Ta réponse doit prouver l'intégration du CADRE DE MISSION ET de l'analyse éthique.

--- INSTRUCTIONS TECHNIQUES COMPLÉMENTAIRES ---
${originalPrompt}
--- FIN DES INSTRUCTIONS TECHNIQUES ---`;

    // Enrichir avec les connaissances documentaires
    return documentationKnowledgeService.generateKnowledgeEnhancedPrompt(
      basePrompt,
      knowledgeResult
    );
  }

  /**
   * Extrait les mots-clés du problème pour l'analyse documentaire
   */
  private extractKeywords(problem: string): string[] {
    const words = problem.toLowerCase().split(/\s+/);
    const stopWords = ['le', 'la', 'les', 'de', 'du', 'et', 'ou', 'mais', 'car', 'donc', 'que', 'qui', 'quoi'];
    
    return words
      .filter(word => word.length > 3 && !stopWords.includes(word))
      .slice(0, 10); // Limiter à 10 mots-clés les plus pertinents
  }

  /**
   * Exécute une analyse de fallback en cas d'erreur
   */
  private async executeFallbackAnalysis(
    step: Step,
    stepIndex: number,
    problemDescription: string,
    originalPrompt: string
  ): Promise<string> {
    
    try {
      const { content: result } = await sendMessageToAI([
        { 
          role: 'system' as const, 
          content: originalPrompt 
        },
        { 
          role: 'user' as const, 
          content: `Exécute l'étape "${step.title}" pour le problème : ${problemDescription}` 
        }
      ], step.task);

      return `[ANALYSE TRADITIONNELLE] ${result}`;

    } catch (fallbackError) {
      console.error(`Erreur fallback pour l'étape ${stepIndex + 1}:`, fallbackError);
      return `Erreur lors de l'analyse de l'étape "${step.title}". L'analyse n'a pas pu être complétée pour cette étape.`;
    }
  }

  /**
   * Analyse la cohérence globale avec le contexte de mission
   */
  private async analyzeGlobalCoherence(
    stepResults: ContextAwareAnalysisResult['stepResults'],
    missionContext: MissionContext,
    problemDescription: string
  ): Promise<ContextAwareAnalysisResult['globalCoherence']> {
    
    const coherencePrompt = `
--- VALIDATION DE COHÉRENCE GLOBALE ---

**CONTEXTE DE MISSION :**
- Personnage : ${missionContext.personnage}
- Objectif : ${missionContext.objectif}
- Contraintes : ${missionContext.contraintes}
- Rôle : ${missionContext.roleAgent}
- Format : ${missionContext.formatSortie}

**RÉSULTATS À VALIDER :**
${stepResults.map((result, index) => `
Étape ${index + 1} - ${result.stepTitle}:
${result.result}
`).join('\n')}

**MISSION :**
1. Évalue la cohérence globale (score de 0 à 100)
2. Identifie les points de cohérence
3. Propose des améliorations si nécessaire

Réponds au format JSON:
{
  "alignmentScore": number,
  "coherenceChecks": ["point1", "point2", ...],
  "recommendations": ["rec1", "rec2", ...]
}`;

    try {
      const { content: analysis } = await sendMessageToAI([
        { 
          role: 'system' as const, 
          content: coherencePrompt 
        },
        { 
          role: 'user' as const, 
          content: 'Analyse maintenant la cohérence globale.' 
        }
      ], 'validation');

      // Tenter de parser le JSON
      try {
        const parsed = JSON.parse(analysis);
        return {
          alignmentScore: parsed.alignmentScore || 85,
          coherenceCheck: parsed.coherenceChecks || ['Analyse en cours...'],
          recommendations: parsed.recommendations || ['Validation en cours...']
        };
      } catch (parseError) {
        // Fallback si le parsing JSON échoue
        return this.generateFallbackCoherence(stepResults, missionContext);
      }

    } catch (error) {
      console.error('Erreur lors de l\'analyse de cohérence:', error);
      return this.generateFallbackCoherence(stepResults, missionContext);
    }
  }

  /**
   * Génère une analyse de cohérence de fallback
   */
  private generateFallbackCoherence(
    stepResults: ContextAwareAnalysisResult['stepResults'],
    missionContext: MissionContext
  ): ContextAwareAnalysisResult['globalCoherence'] {
    
    const contextAwareSteps = stepResults.filter(step => step.isContextAware).length;
    const totalSteps = stepResults.length;
    const alignmentScore = Math.round((contextAwareSteps / totalSteps) * 100);

    return {
      alignmentScore,
      coherenceCheck: [
        `${contextAwareSteps}/${totalSteps} étapes exécutées avec contexte de mission`,
        `Objectif principal : ${missionContext.objectif}`,
        `Rôle appliqué : ${missionContext.roleAgent}`,
        'Analyse de cohérence automatique appliquée'
      ],
      recommendations: [
        'Vérifier l\'alignement de chaque recommandation avec l\'objectif principal',
        'S\'assurer que les contraintes sont respectées dans toutes les étapes',
        'Valider que le format de sortie correspond aux attentes',
        'Confirmer que le rôle d\'expert est cohérent dans l\'ensemble'
      ]
    };
  }
}

export const contextAwareAnalysisService = new ContextAwareAnalysisService();
