{"compilerOptions": {"target": "ES2022", "experimentalDecorators": true, "useDefineForClassFields": false, "module": "ESNext", "lib": ["ES2022", "DOM", "DOM.Iterable"], "skipLibCheck": true, "types": ["node", "@types/react", "@types/react-dom"], "moduleResolution": "bundler", "isolatedModules": true, "moduleDetection": "force", "allowJs": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "noImplicitThis": true, "alwaysStrict": true, "jsx": "react-jsx", "paths": {"@/*": ["./*"]}, "allowImportingTsExtensions": true, "noEmit": true}, "exclude": ["tests/**/*", "**/*.test.ts", "**/*.test.tsx"]}