/**
 * Service de Génération d'Hypothèses Intelligentes
 * Implémente le Principe N°1 : "L'Hypothèse Prime sur la Question"
 * 
 * Au lieu de poser des questions, Rooney formule des hypothèses plausibles
 * basées sur les cas les plus courants et le contexte fourni.
 */

export interface HypothesisContext {
  userInput: string;
  domain: string;
  keywords: string[];
  stepType: 'analyse' | 'génération' | 'validation' | 'synthèse';
}

export interface GeneratedHypothesis {
  scenario: string;
  probability: 'HIGH' | 'MEDIUM' | 'LOW';
  reasoning: string;
  implications: string[];
  nextSteps: string[];
}

export interface HypothesisSet {
  primaryHypothesis: GeneratedHypothesis;
  alternativeHypotheses: GeneratedHypothesis[];
  confidenceLevel: number;
  recommendedApproach: string;
}

class HypothesisGeneratorService {
  
  /**
   * Génère un ensemble d'hypothèses plausibles pour un contexte donné
   */
  generateHypotheses(context: HypothesisContext): HypothesisSet {
    const hypotheses = this.analyzeContext(context);
    const primaryHypothesis = this.selectPrimaryHypothesis(hypotheses);
    const alternatives = hypotheses.filter(h => h !== primaryHypothesis).slice(0, 2);
    
    return {
      primaryHypothesis,
      alternativeHypotheses: alternatives,
      confidenceLevel: this.calculateConfidence(context, hypotheses),
      recommendedApproach: this.generateRecommendedApproach(primaryHypothesis, alternatives)
    };
  }

  /**
   * Analyse le contexte et génère des hypothèses basées sur des patterns courants
   */
  private analyzeContext(context: HypothesisContext): GeneratedHypothesis[] {
    const hypotheses: GeneratedHypothesis[] = [];
    
    // Hypothèses basées sur le domaine
    if (context.domain === 'immigration') {
      hypotheses.push(...this.generateImmigrationHypotheses(context));
    } else if (context.domain === 'social') {
      hypotheses.push(...this.generateSocialHypotheses(context));
    } else if (context.domain === 'entreprise') {
      hypotheses.push(...this.generateBusinessHypotheses(context));
    }
    
    // Hypothèses génériques basées sur le type d'étape
    hypotheses.push(...this.generateStepTypeHypotheses(context));
    
    return hypotheses;
  }

  /**
   * Génère des hypothèses spécifiques au domaine immigration
   */
  private generateImmigrationHypotheses(context: HypothesisContext): GeneratedHypothesis[] {
    const hypotheses: GeneratedHypothesis[] = [];
    
    if (this.containsKeywords(context.keywords, ['titre', 'séjour', 'renouvellement'])) {
      hypotheses.push({
        scenario: "Renouvellement de titre de séjour avec situation administrative complexe",
        probability: 'HIGH',
        reasoning: "Les mots-clés indiquent une démarche de renouvellement, cas très fréquent",
        implications: [
          "Nécessité de justifier la continuité de la situation",
          "Possible impact des litiges administratifs en cours",
          "Importance de la documentation complète"
        ],
        nextSteps: [
          "Rassembler tous les justificatifs de situation",
          "Préparer une note explicative pour les points complexes",
          "Anticiper les questions de la préfecture"
        ]
      });
    }

    if (this.containsKeywords(context.keywords, ['conjoint', 'famille', 'UE'])) {
      hypotheses.push({
        scenario: "Demande liée au statut de membre de famille d'un citoyen UE",
        probability: 'HIGH',
        reasoning: "Le droit au travail des conjoints UE est un principe acquis",
        implications: [
          "Droit automatique au travail",
          "Procédure simplifiée normalement",
          "Protection par le droit européen"
        ],
        nextSteps: [
          "Confirmer le statut du conjoint UE",
          "Rassembler les preuves du lien familial",
          "Préparer le dossier de demande"
        ]
      });
    }

    return hypotheses;
  }

  /**
   * Génère des hypothèses spécifiques au domaine social (URSSAF, etc.)
   */
  private generateSocialHypotheses(context: HypothesisContext): GeneratedHypothesis[] {
    const hypotheses: GeneratedHypothesis[] = [];
    
    if (this.containsKeywords(context.keywords, ['urssaf', 'litige', 'redressement'])) {
      hypotheses.push({
        scenario: "Litige URSSAF en cours de contestation avec impact sur autres démarches",
        probability: 'HIGH',
        reasoning: "Les litiges URSSAF sont fréquents et souvent mal compris par les autres administrations",
        implications: [
          "Le litige contesté n'équivaut pas à une fraude",
          "Possible impact sur le renouvellement de titre",
          "Nécessité de séparer les procédures"
        ],
        nextSteps: [
          "Documenter formellement la contestation",
          "Préparer une note explicative pour la préfecture",
          "Rassembler les preuves de bonne foi"
        ]
      });
    }

    return hypotheses;
  }

  /**
   * Génère des hypothèses spécifiques au domaine entreprise
   */
  private generateBusinessHypotheses(context: HypothesisContext): GeneratedHypothesis[] {
    const hypotheses: GeneratedHypothesis[] = [];
    
    if (this.containsKeywords(context.keywords, ['création', 'entreprise', 'activité'])) {
      hypotheses.push({
        scenario: "Création ou développement d'activité entrepreneuriale",
        probability: 'MEDIUM',
        reasoning: "L'entrepreneuriat est un objectif fréquent, surtout pour les étrangers qualifiés",
        implications: [
          "Besoin d'autorisation de travail adaptée",
          "Optimisation du statut juridique",
          "Planification fiscale et sociale"
        ],
        nextSteps: [
          "Vérifier les autorisations nécessaires",
          "Choisir le statut optimal",
          "Préparer le business plan"
        ]
      });
    }

    return hypotheses;
  }

  /**
   * Génère des hypothèses basées sur le type d'étape du workflow
   */
  private generateStepTypeHypotheses(context: HypothesisContext): GeneratedHypothesis[] {
    const hypotheses: GeneratedHypothesis[] = [];
    
    switch (context.stepType) {
      case 'analyse':
        hypotheses.push({
          scenario: "Situation complexe nécessitant une analyse multi-dimensionnelle",
          probability: 'MEDIUM',
          reasoning: "Les problèmes administratifs impliquent souvent plusieurs entités",
          implications: [
            "Nécessité de séparer les différents aspects",
            "Identification des priorités",
            "Gestion des interdépendances"
          ],
          nextSteps: [
            "Cartographier tous les acteurs impliqués",
            "Hiérarchiser les objectifs",
            "Identifier les points de blocage"
          ]
        });
        break;
        
      case 'génération':
        hypotheses.push({
          scenario: "Recherche de solutions créatives et pragmatiques",
          probability: 'MEDIUM',
          reasoning: "Les solutions standards ne suffisent pas toujours",
          implications: [
            "Exploration d'approches alternatives",
            "Adaptation aux contraintes spécifiques",
            "Innovation dans la présentation du dossier"
          ],
          nextSteps: [
            "Brainstormer les approches possibles",
            "Évaluer la faisabilité de chaque option",
            "Préparer un plan B"
          ]
        });
        break;
    }
    
    return hypotheses;
  }

  /**
   * Sélectionne l'hypothèse principale basée sur la probabilité et la pertinence
   */
  private selectPrimaryHypothesis(hypotheses: GeneratedHypothesis[]): GeneratedHypothesis {
    // Trier par probabilité (HIGH > MEDIUM > LOW)
    const sorted = hypotheses.sort((a, b) => {
      const probabilityOrder = { 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
      return probabilityOrder[b.probability] - probabilityOrder[a.probability];
    });
    
    return sorted[0] || this.getDefaultHypothesis();
  }

  /**
   * Calcule le niveau de confiance basé sur la qualité du contexte
   */
  private calculateConfidence(context: HypothesisContext, hypotheses: GeneratedHypothesis[]): number {
    let confidence = 50; // Base
    
    // Bonus pour les mots-clés spécifiques
    if (context.keywords.length > 3) confidence += 20;
    
    // Bonus pour les hypothèses de haute probabilité
    const highProbHypotheses = hypotheses.filter(h => h.probability === 'HIGH');
    confidence += highProbHypotheses.length * 10;
    
    // Bonus pour la longueur du contexte utilisateur
    if (context.userInput.length > 100) confidence += 15;
    
    return Math.min(confidence, 95); // Maximum 95%
  }

  /**
   * Génère l'approche recommandée basée sur les hypothèses
   */
  private generateRecommendedApproach(primary: GeneratedHypothesis, alternatives: GeneratedHypothesis[]): string {
    let approach = `En supposant que votre situation corresponde au scénario le plus probable (${primary.scenario.toLowerCase()}), `;
    
    if (alternatives.length > 0) {
      approach += `voici mon analyse. Si ce n'est pas le cas, j'ai également prévu ${alternatives.length} scénario${alternatives.length > 1 ? 's' : ''} alternatif${alternatives.length > 1 ? 's' : ''} que nous pourrons explorer.`;
    } else {
      approach += `voici mon analyse et mes recommandations.`;
    }
    
    return approach;
  }

  /**
   * Vérifie si les mots-clés contiennent certains termes
   */
  private containsKeywords(keywords: string[], searchTerms: string[]): boolean {
    return searchTerms.some(term => 
      keywords.some(keyword => 
        keyword.toLowerCase().includes(term.toLowerCase()) ||
        term.toLowerCase().includes(keyword.toLowerCase())
      )
    );
  }

  /**
   * Hypothèse par défaut si aucune spécifique n'est trouvée
   */
  private getDefaultHypothesis(): GeneratedHypothesis {
    return {
      scenario: "Situation administrative complexe nécessitant une approche structurée",
      probability: 'MEDIUM',
      reasoning: "Contexte insuffisant pour une hypothèse spécifique",
      implications: [
        "Besoin de clarification progressive",
        "Approche méthodique recommandée",
        "Adaptation selon les éléments révélés"
      ],
      nextSteps: [
        "Rassembler plus d'informations contextuelles",
        "Identifier les objectifs prioritaires",
        "Établir un plan d'action flexible"
      ]
    };
  }

  /**
   * Formate les hypothèses pour inclusion dans un prompt
   */
  formatHypothesesForPrompt(hypothesisSet: HypothesisSet): string {
    let formatted = `${hypothesisSet.recommendedApproach}\n\n`;
    
    formatted += `**Scénario principal (${hypothesisSet.primaryHypothesis.probability === 'HIGH' ? 'très probable' : 'probable'}) :**\n`;
    formatted += `${hypothesisSet.primaryHypothesis.scenario}\n\n`;
    
    if (hypothesisSet.alternativeHypotheses.length > 0) {
      formatted += `**Scénarios alternatifs à considérer :**\n`;
      hypothesisSet.alternativeHypotheses.forEach((alt, index) => {
        formatted += `${index + 1}. ${alt.scenario}\n`;
      });
      formatted += '\n';
    }
    
    return formatted;
  }
}

// Instance singleton
export const hypothesisGeneratorService = new HypothesisGeneratorService();
