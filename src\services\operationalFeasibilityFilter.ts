/**
 * Service du Filtre de Faisabilité Opérationnelle
 * Implémente le Principe N°5 : "Le Filtre de Faisabilité Opérationnelle"
 * 
 * Applique une boucle de vérification critique sur chaque plan d'action généré
 * pour éviter les propositions logiques en théorie mais erronées en pratique.
 */

export interface FeasibilityCheckResult {
  isValid: boolean;
  issues: FeasibilityIssue[];
  improvedAction?: string;
  confidence: 'HIGH' | 'MEDIUM' | 'LOW';
}

export interface FeasibilityIssue {
  type: 'SEQUENCING' | 'LEGAL_CULTURAL' | 'TEMPORAL_REALISM';
  severity: 'CRITICAL' | 'MAJOR' | 'MINOR';
  description: string;
  suggestion: string;
}

export interface ActionToValidate {
  title: string;
  description: string;
  timeline: string;
  priority: string;
  order: number;
  context?: {
    domain: string;
    countries?: string[];
    sectors?: string[];
    userSituation: string;
  };
}

class OperationalFeasibilityFilter {
  
  /**
   * Applique le filtre de faisabilité opérationnelle sur un plan d'action complet
   */
  validateActionPlan(actions: ActionToValidate[]): FeasibilityCheckResult[] {
    const results: FeasibilityCheckResult[] = [];
    
    for (let i = 0; i < actions.length; i++) {
      const action = actions[i];
      const previousActions = actions.slice(0, i);
      const nextActions = actions.slice(i + 1);
      
      const result = this.validateSingleAction(action, previousActions, nextActions);
      results.push(result);
    }
    
    return results;
  }

  /**
   * Valide une action individuelle selon les 3 questions critiques
   */
  private validateSingleAction(
    action: ActionToValidate, 
    previousActions: ActionToValidate[], 
    nextActions: ActionToValidate[]
  ): FeasibilityCheckResult {
    const issues: FeasibilityIssue[] = [];
    
    // Question 1: Séquençage - L'ordre est-il correct ?
    const sequencingIssues = this.checkSequencing(action, previousActions, nextActions);
    issues.push(...sequencingIssues);
    
    // Question 2: Faisabilité Juridique/Culturelle
    const legalCulturalIssues = this.checkLegalCulturalFeasibility(action);
    issues.push(...legalCulturalIssues);
    
    // Question 3: Réalisme Temporel
    const temporalIssues = this.checkTemporalRealism(action);
    issues.push(...temporalIssues);
    
    // Déterminer la validité et la confiance
    const criticalIssues = issues.filter(i => i.severity === 'CRITICAL');
    const majorIssues = issues.filter(i => i.severity === 'MAJOR');
    
    let isValid = criticalIssues.length === 0;
    let confidence: 'HIGH' | 'MEDIUM' | 'LOW' = 'HIGH';
    
    if (criticalIssues.length > 0) {
      confidence = 'LOW';
    } else if (majorIssues.length > 0) {
      confidence = 'MEDIUM';
    }
    
    // Générer une version améliorée si nécessaire
    let improvedAction: string | undefined;
    if (!isValid || confidence === 'MEDIUM') {
      improvedAction = this.generateImprovedAction(action, issues);
    }
    
    return {
      isValid,
      issues,
      improvedAction,
      confidence
    };
  }

  /**
   * Question 1: Vérification du séquençage
   * "L'ordre de mes actions est-il correct ? Y a-t-il un prérequis manquant ?"
   */
  private checkSequencing(
    action: ActionToValidate, 
    previousActions: ActionToValidate[], 
    nextActions: ActionToValidate[]
  ): FeasibilityIssue[] {
    const issues: FeasibilityIssue[] = [];
    
    // Vérifications spécifiques de séquençage
    const actionText = `${action.title} ${action.description}`.toLowerCase();
    
    // Règle 1: L'audit/analyse doit précéder la présentation
    if ((actionText.includes('présent') || actionText.includes('rapport') || actionText.includes('conclusion')) &&
        !previousActions.some(prev => 
          prev.title.toLowerCase().includes('audit') || 
          prev.title.toLowerCase().includes('analys') ||
          prev.title.toLowerCase().includes('évaluation') ||
          prev.title.toLowerCase().includes('diagnostic')
        )) {
      issues.push({
        type: 'SEQUENCING',
        severity: 'CRITICAL',
        description: "Présentation/rapport prévu avant l'analyse préalable",
        suggestion: "Ajouter une étape d'audit/diagnostic avant la présentation"
      });
    }
    
    // Règle 2: La documentation doit précéder le dépôt
    if ((actionText.includes('dépôt') || actionText.includes('soumet') || actionText.includes('dépos')) &&
        !previousActions.some(prev => 
          prev.title.toLowerCase().includes('document') || 
          prev.title.toLowerCase().includes('dossier') ||
          prev.title.toLowerCase().includes('constitu')
        )) {
      issues.push({
        type: 'SEQUENCING',
        severity: 'CRITICAL',
        description: "Dépôt prévu avant la constitution du dossier",
        suggestion: "Ajouter une étape de constitution/préparation du dossier avant le dépôt"
      });
    }
    
    // Règle 3: La validation juridique doit précéder l'action finale
    if ((actionText.includes('signature') || actionText.includes('accord') || actionText.includes('contrat')) &&
        !previousActions.some(prev => 
          prev.title.toLowerCase().includes('juridique') || 
          prev.title.toLowerCase().includes('légal') ||
          prev.title.toLowerCase().includes('conformité')
        )) {
      issues.push({
        type: 'SEQUENCING',
        severity: 'MAJOR',
        description: "Engagement contractuel sans validation juridique préalable",
        suggestion: "Ajouter une étape de vérification juridique avant l'engagement"
      });
    }
    
    return issues;
  }

  /**
   * Question 2: Vérification de la faisabilité juridique/culturelle
   * "Mon action est-elle applicable dans TOUS les contextes mentionnés ?"
   */
  private checkLegalCulturalFeasibility(action: ActionToValidate): FeasibilityIssue[] {
    const issues: FeasibilityIssue[] = [];
    
    const actionText = `${action.title} ${action.description}`.toLowerCase();
    const context = action.context;
    
    // Vérification des "actions communes" dangereuses
    if (actionText.includes('réunion commune') || actionText.includes('approche commune')) {
      if (context?.countries && context.countries.length > 1) {
        issues.push({
          type: 'LEGAL_CULTURAL',
          severity: 'CRITICAL',
          description: "Approche 'commune' entre plusieurs pays aux législations différentes",
          suggestion: "Remplacer par une série de réunions nationales coordonnées"
        });
      }
      
      if (context?.sectors && context.sectors.length > 1) {
        issues.push({
          type: 'LEGAL_CULTURAL',
          severity: 'MAJOR',
          description: "Approche commune entre secteurs aux réglementations distinctes",
          suggestion: "Adapter l'approche aux spécificités de chaque secteur"
        });
      }
    }
    
    // Vérification des obligations légales spécifiques
    if (actionText.includes('notification') || actionText.includes('déclaration')) {
      issues.push({
        type: 'LEGAL_CULTURAL',
        severity: 'MAJOR',
        description: "Obligation de notification qui peut varier selon la juridiction",
        suggestion: "Vérifier les obligations spécifiques à chaque contexte légal"
      });
    }
    
    // Vérification des procédures administratives
    if (actionText.includes('dépôt') && context?.domain === 'immigration') {
      issues.push({
        type: 'LEGAL_CULTURAL',
        severity: 'MAJOR',
        description: "Procédures de dépôt variables selon les préfectures",
        suggestion: "Préciser les spécificités de la préfecture concernée"
      });
    }
    
    return issues;
  }

  /**
   * Question 3: Vérification du réalisme temporel
   * "Les délais proposés sont-ils crédibles ou dangereusement optimistes ?"
   */
  private checkTemporalRealism(action: ActionToValidate): FeasibilityIssue[] {
    const issues: FeasibilityIssue[] = [];
    
    const actionText = `${action.title} ${action.description}`.toLowerCase();
    const timeline = action.timeline.toLowerCase();
    
    // Extraction des délais numériques
    const timelineNumbers = timeline.match(/\d+/g);
    const timelineDays = this.convertTimelineTodays(timeline);
    
    // Règle 1: Négociations administratives
    if ((actionText.includes('négoci') || actionText.includes('accord') || actionText.includes('convention')) &&
        timelineDays < 14) {
      issues.push({
        type: 'TEMPORAL_REALISM',
        severity: 'CRITICAL',
        description: "Délai irréaliste pour une négociation administrative",
        suggestion: "Prévoir au minimum 2-4 semaines pour les négociations"
      });
    }
    
    // Règle 2: Procédures préfectorales
    if ((actionText.includes('préfecture') || actionText.includes('préfectoral')) &&
        timelineDays < 21) {
      issues.push({
        type: 'TEMPORAL_REALISM',
        severity: 'MAJOR',
        description: "Délai optimiste pour les procédures préfectorales",
        suggestion: "Prévoir 3-6 semaines minimum pour les démarches préfectorales"
      });
    }
    
    // Règle 3: Validation juridique
    if ((actionText.includes('juridique') || actionText.includes('légal') || actionText.includes('avocat')) &&
        timelineDays < 7) {
      issues.push({
        type: 'TEMPORAL_REALISM',
        severity: 'MAJOR',
        description: "Délai insuffisant pour une validation juridique approfondie",
        suggestion: "Prévoir au minimum 1-2 semaines pour l'analyse juridique"
      });
    }
    
    // Règle 4: Actions "immédiates" suspectes
    if ((timeline.includes('immédiat') || timeline.includes('urgent')) &&
        (actionText.includes('audit') || actionText.includes('analys') || actionText.includes('étude'))) {
      issues.push({
        type: 'TEMPORAL_REALISM',
        severity: 'MAJOR',
        description: "Audit/analyse marqué comme 'immédiat' - irréaliste",
        suggestion: "Prévoir un délai réaliste de 1-2 semaines minimum pour l'analyse"
      });
    }
    
    return issues;
  }

  /**
   * Convertit une timeline textuelle en nombre de jours approximatif
   */
  private convertTimelineTodays(timeline: string): number {
    const text = timeline.toLowerCase();
    
    if (text.includes('immédiat') || text.includes('urgent')) return 1;
    if (text.includes('jour')) {
      const match = text.match(/(\d+)\s*jour/);
      return match ? parseInt(match[1]) : 3;
    }
    if (text.includes('semaine')) {
      const match = text.match(/(\d+)\s*semaine/);
      return match ? parseInt(match[1]) * 7 : 7;
    }
    if (text.includes('mois')) {
      const match = text.match(/(\d+)\s*mois/);
      return match ? parseInt(match[1]) * 30 : 30;
    }
    
    // Délai par défaut
    return 7;
  }

  /**
   * Génère une version améliorée de l'action basée sur les problèmes identifiés
   */
  private generateImprovedAction(action: ActionToValidate, issues: FeasibilityIssue[]): string {
    let improved = action.description;
    
    // Appliquer les suggestions d'amélioration
    for (const issue of issues) {
      switch (issue.type) {
        case 'SEQUENCING':
          if (issue.description.includes('avant l\'analyse')) {
            improved = `**PRÉALABLE REQUIS:** Effectuer d'abord un diagnostic complet, puis ${improved.toLowerCase()}`;
          }
          if (issue.description.includes('avant la constitution')) {
            improved = `**PRÉALABLE REQUIS:** Constituer et valider le dossier complet avant de ${improved.toLowerCase()}`;
          }
          break;
          
        case 'LEGAL_CULTURAL':
          if (issue.description.includes('commune')) {
            improved = improved.replace(/commune|commun/gi, 'coordonnée mais adaptée aux spécificités locales');
          }
          if (issue.description.includes('juridiction')) {
            improved = `${improved} **ATTENTION:** Vérifier les obligations spécifiques à chaque contexte légal`;
          }
          break;
          
        case 'TEMPORAL_REALISM':
          if (issue.description.includes('irréaliste') || issue.description.includes('optimiste')) {
            improved = `${improved} **DÉLAI RÉAJUSTÉ:** ${issue.suggestion}`;
          }
          break;
      }
    }
    
    return improved;
  }

  /**
   * Méthode utilitaire pour challenger un plan complet et retourner un rapport
   */
  generateFeasibilityReport(actions: ActionToValidate[]): string {
    const results = this.validateActionPlan(actions);
    
    let report = "🔍 **RAPPORT DU FILTRE DE FAISABILITÉ OPÉRATIONNELLE**\n\n";
    report += "**Principe 5 appliqué - Vérification critique du plan d'action**\n\n";
    
    let totalIssues = 0;
    let actionsNeedingImprovement = 0;
    
    results.forEach((result, index) => {
      const action = actions[index];
      totalIssues += result.issues.length;
      
      if (!result.isValid || result.confidence !== 'HIGH') {
        actionsNeedingImprovement++;
        
        report += `⚠️ **Étape ${index + 1}: ${action.title}**\n`;
        report += `   Confiance: ${result.confidence}\n\n`;
        
        result.issues.forEach(issue => {
          const emoji = issue.severity === 'CRITICAL' ? '🔴' : issue.severity === 'MAJOR' ? '🟡' : '🟢';
          report += `   ${emoji} **${issue.type.replace('_', ' ')}:** ${issue.description}\n`;
          report += `      *Suggestion:* ${issue.suggestion}\n\n`;
        });
        
        if (result.improvedAction) {
          report += `   ✅ **Version améliorée:**\n   ${result.improvedAction}\n\n`;
        }
        
        report += "---\n\n";
      }
    });
    
    report += `📊 **RÉSUMÉ:**\n`;
    report += `- Actions analysées: ${actions.length}\n`;
    report += `- Actions nécessitant amélioration: ${actionsNeedingImprovement}\n`;
    report += `- Total des problèmes détectés: ${totalIssues}\n\n`;
    
    if (actionsNeedingImprovement === 0) {
      report += "✅ **Plan d'action validé** - Faisabilité opérationnelle confirmée\n";
    } else {
      report += "⚠️ **Plan d'action à ajuster** - Voir les recommandations ci-dessus\n";
    }
    
    return report;
  }
}

// Instance singleton
export const operationalFeasibilityFilter = new OperationalFeasibilityFilter();
