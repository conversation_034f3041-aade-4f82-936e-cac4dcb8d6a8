# Scripts de Test et Utilitaires

Ce répertoire contient les scripts de test et utilitaires pour le système Roony Agent.

## Scripts de Test du Principe 5

### `testOperationalFeasibilityFilter.ts`

Script de test complet pour le **Principe 5 - Filtre de Faisabilité Opérationnelle**.

#### Comment l'utiliser

```bash
# Option 1: Avec ts-node (recommandé)
npx ts-node scripts/testOperationalFeasibilityFilter.ts

# Option 2: Compilation puis exécution
npx tsc scripts/testOperationalFeasibilityFilter.ts
node scripts/testOperationalFeasibilityFilter.js

# Option 3: Avec le package.json (si configuré)
npm run test:feasibility
```

#### Tests inclus

1. **Test Séquençage** - Détecte les problèmes d'ordre des actions
2. **Test Juridique/Culturel** - Identifie les problèmes multi-juridictionnels
3. **Test Réalisme Temporel** - Corrige les délais irréalistes
4. **Test Plan Parfait** - Valide les plans bien construits

#### Exemple de sortie

```
🚀 TESTS DU PRINCIPE 5 - FILTRE DE FAISABILITÉ OPÉRATIONNELLE
================================================================

🔍 TEST 1 : Problème de Séquençage
==================================================
Résultats de validation :
  Action 1: ❌ Problème (Confiance: LOW)
    - SEQUENCING: Présentation/rapport prévu avant l'analyse préalable
    ⚡ Amélioration: **PRÉALABLE REQUIS:** Effectuer d'abord un diagnostic complet...

✅ TOUS LES TESTS TERMINÉS AVEC SUCCÈS
```

## Autres Scripts

### Scripts de Validation Système
- `validateSystem.js` - Validation générale du système
- `testLanguageValidation.js` - Tests de validation linguistique
- `testScrollFixes.js` - Tests des corrections de défilement

### Scripts de Mise à Jour
- `generateUpdatedConstants.js` - Génération des constantes mises à jour
- `integrateAutoUpdate.js` - Intégration des mises à jour automatiques
- `testAutoModelUpdate.js` - Tests des mises à jour de modèles

### Scripts Premium
- `test-premium-fix.js` - Tests des fonctionnalités premium

## Développement et Contribution

Pour ajouter de nouveaux tests au Principe 5 :

1. Modifier `testOperationalFeasibilityFilter.ts`
2. Ajouter votre cas de test dans la fonction appropriée
3. Suivre le pattern existant pour la structure de test
4. Exécuter le script pour valider

### Structure d'un test

```typescript
const monNouveauTest = (): void => {
  console.log('\n🔍 TEST X : Mon Nouveau Test');
  console.log('=' .repeat(50));
  
  const actionsToTest: ActionToValidate[] = [
    // Vos actions de test
  ];
  
  const results = operationalFeasibilityFilter.validateActionPlan(actionsToTest);
  
  // Affichage des résultats
};
```

## Notes importantes

- Les fichiers `.ts` nécessitent `ts-node` ou une compilation préalable
- Les tests utilisent les vrais services du système
- Les sorties incluent des émojis pour une meilleure lisibilité
- Chaque test est indépendant et peut être exécuté séparément
