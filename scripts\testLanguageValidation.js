/**
 * Script de test simple pour valider le système de validation de langue française
 * Utilisation: node scripts/testLanguageValidation.js
 */

// Import du service (adaptation pour Node.js)
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Simulation du service de validation (version simplifiée pour test)
class SimpleLanguageValidator {
    constructor() {
        this.stats = {
            totalValidations: 0,
            frenchResponses: 0,
            englishResponses: 0,
            mixedResponses: 0,
            unknownResponses: 0
        };
    }

    validateFrenchResponse(text) {
        this.stats.totalValidations++;
        
        // Détection simple basée sur des mots-clés
        const frenchKeywords = ['le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'mais', 'donc', 'car', 'ni', 'or', 'est', 'sont', 'être', 'avoir', 'faire', 'aller', 'vous', 'nous', 'ils', 'elles'];
        const englishKeywords = ['the', 'and', 'or', 'but', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would'];
        
        const words = text.toLowerCase().split(/\s+/);
        const frenchCount = words.filter(word => frenchKeywords.includes(word)).length;
        const englishCount = words.filter(word => englishKeywords.includes(word)).length;
        
        const frenchScore = (frenchCount / words.length) * 100;
        const englishScore = (englishCount / words.length) * 100;
        
        let detectedLanguage = 'unknown';
        let isValid = false;
        
        if (frenchScore > englishScore && frenchScore > 10) {
            detectedLanguage = 'french';
            isValid = true;
            this.stats.frenchResponses++;
        } else if (englishScore > frenchScore && englishScore > 10) {
            detectedLanguage = 'english';
            this.stats.englishResponses++;
        } else if (frenchScore > 5 && englishScore > 5) {
            detectedLanguage = 'mixed';
            this.stats.mixedResponses++;
        } else {
            this.stats.unknownResponses++;
        }
        
        return {
            isValid,
            detectedLanguage,
            confidence: Math.max(frenchScore, englishScore),
            frenchScore,
            englishScore,
            issues: isValid ? [] : [`Réponse détectée en ${detectedLanguage}`],
            suggestions: isValid ? [] : ['Ajouter des instructions pour forcer le français']
        };
    }

    getStats() {
        return { ...this.stats };
    }

    generateComplianceReport() {
        const frenchPercentage = this.stats.totalValidations > 0 
            ? (this.stats.frenchResponses / this.stats.totalValidations) * 100 
            : 0;
        
        return `
# 📊 Rapport de Conformité Linguistique

## Statistiques Globales
- **Total validations**: ${this.stats.totalValidations}
- **Réponses françaises**: ${this.stats.frenchResponses} (${frenchPercentage.toFixed(1)}%)
- **Réponses anglaises**: ${this.stats.englishResponses} (${((this.stats.englishResponses / this.stats.totalValidations) * 100).toFixed(1)}%)
- **Réponses mixtes**: ${this.stats.mixedResponses} (${((this.stats.mixedResponses / this.stats.totalValidations) * 100).toFixed(1)}%)

## 🎯 Statut de Conformité
${frenchPercentage >= 90 ? '✅ EXCELLENT' : frenchPercentage >= 70 ? '⚠️ ACCEPTABLE' : '❌ PROBLÉMATIQUE'}
        `;
    }
}

// Tests de validation
const validator = new SimpleLanguageValidator();

console.log('🧪 Test du Système de Validation de Langue Française\n');
console.log('=' .repeat(60));

// Textes de test
const testTexts = {
    french: [
        "Bonjour, comment allez-vous aujourd'hui ?",
        "L'analyse de ce problème complexe nécessite une approche méthodologique rigoureuse.",
        "Voici une réponse détaillée avec des caractères accentués : à, é, è, ê, ë.",
        "C'est l'occasion d'analyser qu'il n'y a pas d'autre solution."
    ],
    english: [
        "Hello, how are you doing today?",
        "The analysis of this complex problem requires a rigorous approach.",
        "This implementation uses advanced algorithms and machine learning.",
        "We need to evaluate the different possible solutions."
    ],
    mixed: [
        "Bonjour, this is a mixed response avec du français et de l'anglais.",
        "Hello, voici une réponse mixte with French et English words."
    ]
};

// Test des textes français
console.log('\n📝 Test des textes français:');
testTexts.french.forEach((text, index) => {
    const result = validator.validateFrenchResponse(text);
    const status = result.isValid ? '✅' : '❌';
    console.log(`${status} Test ${index + 1}: ${result.detectedLanguage} (confiance: ${result.confidence.toFixed(1)}%)`);
    if (!result.isValid) {
        console.log(`   Issues: ${result.issues.join(', ')}`);
    }
});

// Test des textes anglais
console.log('\n📝 Test des textes anglais:');
testTexts.english.forEach((text, index) => {
    const result = validator.validateFrenchResponse(text);
    const status = result.isValid ? '❌ ERREUR' : '✅';
    console.log(`${status} Test ${index + 1}: ${result.detectedLanguage} (confiance: ${result.confidence.toFixed(1)}%)`);
});

// Test des textes mixtes
console.log('\n📝 Test des textes mixtes:');
testTexts.mixed.forEach((text, index) => {
    const result = validator.validateFrenchResponse(text);
    const status = result.isValid ? '❌ ERREUR' : '✅';
    console.log(`${status} Test ${index + 1}: ${result.detectedLanguage} (confiance: ${result.confidence.toFixed(1)}%)`);
});

// Affichage des statistiques
console.log('\n📊 Statistiques finales:');
const stats = validator.getStats();
console.log(`Total validations: ${stats.totalValidations}`);
console.log(`Réponses françaises: ${stats.frenchResponses}`);
console.log(`Réponses anglaises: ${stats.englishResponses}`);
console.log(`Réponses mixtes: ${stats.mixedResponses}`);
console.log(`Réponses inconnues: ${stats.unknownResponses}`);

// Génération du rapport
console.log('\n📋 Rapport de conformité:');
console.log(validator.generateComplianceReport());

// Test de performance
console.log('\n⚡ Test de performance:');
const startTime = Date.now();
const longText = "Voici un texte français assez long pour tester les performances du système de validation. ".repeat(100);

for (let i = 0; i < 100; i++) {
    validator.validateFrenchResponse(longText);
}

const endTime = Date.now();
console.log(`100 validations en ${endTime - startTime}ms (${((endTime - startTime) / 100).toFixed(2)}ms par validation)`);

// Vérification des fichiers de service
console.log('\n🔍 Vérification des fichiers:');
const servicePath = path.join(__dirname, '../services/languageValidationService.ts');
const componentPath = path.join(__dirname, '../components/LanguageComplianceMonitor.tsx');

if (fs.existsSync(servicePath)) {
    console.log('✅ Service de validation trouvé');
} else {
    console.log('❌ Service de validation manquant');
}

if (fs.existsSync(componentPath)) {
    console.log('✅ Composant de monitoring trouvé');
} else {
    console.log('❌ Composant de monitoring manquant');
}

// Vérification de la configuration
const constantsPath = path.join(__dirname, '../constants.ts');
if (fs.existsSync(constantsPath)) {
    const constantsContent = fs.readFileSync(constantsPath, 'utf8');
    if (constantsContent.includes('LANGUAGE_VALIDATION_CONFIG')) {
        console.log('✅ Configuration de validation trouvée');
    } else {
        console.log('❌ Configuration de validation manquante');
    }
} else {
    console.log('❌ Fichier constants.ts manquant');
}

console.log('\n' + '=' .repeat(60));
console.log('🎉 Test terminé avec succès !');
console.log('\n💡 Prochaines étapes:');
console.log('1. Lancer l\'application: npm run dev');
console.log('2. Tester avec de vrais modèles IA');
console.log('3. Vérifier le monitoring dans l\'interface');
console.log('4. Ajuster les seuils si nécessaire');

// Recommandations basées sur les résultats
const frenchPercentage = (stats.frenchResponses / stats.totalValidations) * 100;
if (frenchPercentage < 90) {
    console.log('\n⚠️ ATTENTION: Taux de conformité faible détecté');
    console.log('   Recommandations:');
    console.log('   - Renforcer les instructions dans les prompts système');
    console.log('   - Activer la validation stricte avec retry');
    console.log('   - Surveiller les modèles problématiques');
}
