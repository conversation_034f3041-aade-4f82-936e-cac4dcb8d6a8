/**
 * Service de Monitoring Stratégique pour Rooney
 * Surveille et optimise l'application des capacités stratégiques de l'agent
 * 
 * MISSION : S'assurer que l'agent maintient constamment sa vision stratégique
 * et enrichit chaque interaction avec des pépites de valeur
 */

import { expertConsultantService } from './expertConsultantService';

export interface StrategicMetrics {
  strategicConsistency: number;    // Cohérence avec l'objectif central (0-100)
  valueEnrichment: number;         // Richesse des pépites ajoutées (0-100)
  terminologyPrecision: number;    // Précision terminologique (0-100)
  overallStrategicScore: number;   // Score stratégique global (0-100)
}

export interface StrategicRecommendation {
  priority: 'critical' | 'high' | 'medium' | 'low';
  category: 'strategic_thread' | 'value_gems' | 'terminology' | 'engagement';
  title: string;
  description: string;
  actionableSteps: string[];
  expectedImpact: string;
}

class StrategicMonitoringService {
  
  /**
   * Évalue la qualité stratégique d'une interaction complète
   */
  evaluateStrategicQuality(
    userInput: string,
    aiResponse: string,
    stepTitle: string
  ): {
    metrics: StrategicMetrics;
    recommendations: StrategicRecommendation[];
    strategicGaps: string[];
    strengthAreas: string[];
  } {
    
    // Analyser la qualité expert-conseil existante
    const expertQuality = expertConsultantService.analyzeResponseQuality(aiResponse);
    
    // Nouvelles métriques stratégiques
    const strategicMetrics = this.calculateStrategicMetrics(userInput, aiResponse, stepTitle);
    const strategicGaps = this.identifyStrategicGaps(aiResponse, strategicMetrics);
    const strengthAreas = this.identifyStrengthAreas(aiResponse, strategicMetrics);
    const recommendations = this.generateStrategicRecommendations(strategicGaps, strategicMetrics);

    return {
      metrics: strategicMetrics,
      recommendations,
      strategicGaps,
      strengthAreas
    };
  }

  /**
   * Calcule les métriques stratégiques
   */
  private calculateStrategicMetrics(userInput: string, aiResponse: string, stepTitle: string): StrategicMetrics {
    const strategicConsistency = this.measureStrategicConsistency(userInput, aiResponse);
    const valueEnrichment = this.measureValueEnrichment(aiResponse);
    const terminologyPrecision = this.measureTerminologyPrecision(aiResponse);
    
    const overallStrategicScore = Math.round(
      (strategicConsistency * 0.4 + valueEnrichment * 0.35 + terminologyPrecision * 0.25)
    );

    return {
      strategicConsistency,
      valueEnrichment,
      terminologyPrecision,
      overallStrategicScore
    };
  }

  /**
   * Mesure la cohérence stratégique avec l'objectif central
   */
  private measureStrategicConsistency(userInput: string, aiResponse: string): number {
    let score = 0;
    
    // Vérifier la mention de l'objectif central
    const objectiveKeywords = [
      'objectif principal', 'objectif central', 'but prioritaire',
      'votre objectif', 'ce que vous voulez', 'votre priorité'
    ];
    
    const mentionsObjective = objectiveKeywords.some(keyword => 
      aiResponse.toLowerCase().includes(keyword)
    );
    if (mentionsObjective) score += 30;

    // Vérifier la liaison des conseils à l'objectif
    const linkingPhrases = [
      'cela vous permettra de', 'pour atteindre', 'en vue de',
      'contribue à', 'vous rapproche de', 'essentiel pour'
    ];
    
    const linksToObjective = linkingPhrases.some(phrase => 
      aiResponse.toLowerCase().includes(phrase)
    );
    if (linksToObjective) score += 25;

    // Vérifier la cohérence du plan d'action
    const hasCoherentPlan = aiResponse.toLowerCase().includes('plan') &&
                           aiResponse.toLowerCase().includes('étape');
    if (hasCoherentPlan) score += 25;

    // Vérifier l'absence de dispersion
    const wordCount = aiResponse.split(' ').length;
    const focusRatio = this.calculateFocusRatio(aiResponse);
    if (focusRatio > 0.7 && wordCount > 200) score += 20;

    return Math.min(score, 100);
  }

  /**
   * Mesure l'enrichissement par des pépites de valeur
   */
  private measureValueEnrichment(aiResponse: string): number {
    let score = 0;
    
    // Vérifier la présence d'outils concrets
    const toolIndicators = [
      'modèle', 'template', 'formulaire', 'calculateur',
      'outil en ligne', 'site officiel', 'plateforme'
    ];
    
    const hasTools = toolIndicators.some(tool => 
      aiResponse.toLowerCase().includes(tool)
    );
    if (hasTools) score += 25;

    // Vérifier les conseils d'initié
    const insiderIndicators = [
      'astuce', 'conseil pratique', 'attention particulière',
      'point important', 'ne pas oublier', 'pensez à'
    ];
    
    const hasInsiderTips = insiderIndicators.some(tip => 
      aiResponse.toLowerCase().includes(tip)
    );
    if (hasInsiderTips) score += 20;

    // Vérifier les contacts/ressources
    const contactIndicators = [
      '.fr', '.gouv', 'téléphone', 'contact', 'service',
      'organisme', 'administration'
    ];
    
    const hasContacts = contactIndicators.some(contact => 
      aiResponse.toLowerCase().includes(contact)
    );
    if (hasContacts) score += 20;

    // Vérifier les exemples concrets
    const exampleIndicators = [
      'par exemple', 'concrètement', 'dans votre cas',
      'prenons le cas', 'illustration'
    ];
    
    const hasExamples = exampleIndicators.some(example => 
      aiResponse.toLowerCase().includes(example)
    );
    if (hasExamples) score += 20;

    // Vérifier la proactivité
    const proactiveIndicators = [
      'je vous suggère', 'mon conseil', 'je recommande',
      'n\'hésitez pas', 'pensez également'
    ];
    
    const isProactive = proactiveIndicators.some(indicator => 
      aiResponse.toLowerCase().includes(indicator)
    );
    if (isProactive) score += 15;

    return Math.min(score, 100);
  }

  /**
   * Mesure la précision terminologique
   */
  private measureTerminologyPrecision(aiResponse: string): number {
    let score = 100; // On commence à 100 et on retire des points pour les erreurs
    
    // Termes approximatifs à éviter
    const approximateTerms = [
      { wrong: 'papiers', correct: 'titre de séjour', penalty: 20 },
      { wrong: 'charges sociales', correct: 'cotisations sociales', penalty: 15 },
      { wrong: 'demande formelle', correct: 'mise en demeure', penalty: 15 },
      { wrong: 'ultimatum', correct: 'mise en demeure', penalty: 10 },
      { wrong: 'autorisation de séjour', correct: 'titre de séjour', penalty: 15 }
    ];
    
    approximateTerms.forEach(term => {
      if (aiResponse.toLowerCase().includes(term.wrong)) {
        score -= term.penalty;
      }
    });

    // Vérifier la présence de termes techniques appropriés
    const technicalTerms = [
      'procédure administrative', 'réglementation', 'dispositions légales',
      'article', 'décret', 'arrêté', 'circulaire'
    ];
    
    const hasTechnicalTerms = technicalTerms.some(term => 
      aiResponse.toLowerCase().includes(term)
    );
    if (!hasTechnicalTerms) score -= 15;

    // Vérifier l'usage approprié du vouvoiement et de la forme professionnelle
    const informalTerms = ['salut', 'coucou', 'ok', 'super', 'cool'];
    const hasInformalTerms = informalTerms.some(term => 
      aiResponse.toLowerCase().includes(term)
    );
    if (hasInformalTerms) score -= 20;

    return Math.max(score, 0);
  }

  /**
   * Calcule le ratio de concentration du message
   */
  private calculateFocusRatio(aiResponse: string): number {
    const sentences = aiResponse.split(/[.!?]+/).filter(s => s.trim().length > 10);
    const mainTopicKeywords = this.extractMainTopicKeywords(aiResponse);
    
    let focusedSentences = 0;
    sentences.forEach(sentence => {
      const hasMainKeyword = mainTopicKeywords.some(keyword => 
        sentence.toLowerCase().includes(keyword.toLowerCase())
      );
      if (hasMainKeyword) focusedSentences++;
    });

    return sentences.length > 0 ? focusedSentences / sentences.length : 0;
  }

  /**
   * Extrait les mots-clés du sujet principal
   */
  private extractMainTopicKeywords(aiResponse: string): string[] {
    // Simplifié : retourne les mots les plus fréquents significatifs
    const words = aiResponse.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 4);
    
    const wordCount: { [key: string]: number } = {};
    words.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1;
    });

    return Object.entries(wordCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word);
  }

  /**
   * Identifie les lacunes stratégiques
   */
  private identifyStrategicGaps(aiResponse: string, metrics: StrategicMetrics): string[] {
    const gaps: string[] = [];

    if (metrics.strategicConsistency < 60) {
      gaps.push("Manque de cohérence avec l'objectif central - les conseils semblent dispersés");
    }

    if (metrics.valueEnrichment < 50) {
      gaps.push("Insuffisance de pépites de valeur concrètes - manque d'outils, contacts et astuces");
    }

    if (metrics.terminologyPrecision < 70) {
      gaps.push("Imprécision terminologique - utilisation de termes approximatifs");
    }

    if (!aiResponse.toLowerCase().includes('objectif') && !aiResponse.toLowerCase().includes('but')) {
      gaps.push("Absence de référence à l'objectif prioritaire du client");
    }

    if (!aiResponse.toLowerCase().includes('recommande') && !aiResponse.toLowerCase().includes('conseil')) {
      gaps.push("Manque de dimension conseil proactif");
    }

    return gaps;
  }

  /**
   * Identifie les points forts
   */
  private identifyStrengthAreas(aiResponse: string, metrics: StrategicMetrics): string[] {
    const strengths: string[] = [];

    if (metrics.strategicConsistency >= 80) {
      strengths.push("Excellente cohérence stratégique - maintien du fil rouge");
    }

    if (metrics.valueEnrichment >= 70) {
      strengths.push("Riche en pépites de valeur - conseils concrets et actionnables");
    }

    if (metrics.terminologyPrecision >= 85) {
      strengths.push("Terminologie experte irréprochable - crédibilité technique");
    }

    if (aiResponse.includes('plan') && aiResponse.includes('étape')) {
      strengths.push("Structure d'action claire et méthodique");
    }

    return strengths;
  }

  /**
   * Génère des recommandations stratégiques d'amélioration
   */
  private generateStrategicRecommendations(gaps: string[], metrics: StrategicMetrics): StrategicRecommendation[] {
    const recommendations: StrategicRecommendation[] = [];

    if (metrics.strategicConsistency < 60) {
      recommendations.push({
        priority: 'critical',
        category: 'strategic_thread',
        title: 'Renforcer le Fil Rouge Stratégique',
        description: 'Chaque conseil doit explicitement contribuer à l\'objectif central du client',
        actionableSteps: [
          'Identifier et énoncer l\'objectif prioritaire en début de réponse',
          'Relier chaque recommandation à cet objectif avec des phrases de liaison',
          'Terminer en rappelant comment l\'ensemble contribue au but principal'
        ],
        expectedImpact: 'Amélioration de la cohérence stratégique de 40-60 points'
      });
    }

    if (metrics.valueEnrichment < 50) {
      recommendations.push({
        priority: 'high',
        category: 'value_gems',
        title: 'Enrichir avec des Pépites de Valeur',
        description: 'Intégrer systématiquement des outils concrets, contacts et astuces d\'initié',
        actionableSteps: [
          'Ajouter au moins un modèle/template utilisable',
          'Fournir des contacts officiels pertinents',
          'Partager une astuce d\'initié ou un point d\'attention crucial'
        ],
        expectedImpact: 'Augmentation de la valeur perçue et de l\'actionnabilité'
      });
    }

    if (metrics.terminologyPrecision < 70) {
      recommendations.push({
        priority: 'high',
        category: 'terminology',
        title: 'Préciser la Terminologie Experte',
        description: 'Utiliser uniquement les termes techniques précis du domaine',
        actionableSteps: [
          'Remplacer les termes approximatifs par leur équivalent technique',
          'Vérifier la précision des références légales et administratives',
          'Maintenir un niveau de vocabulaire professionnel'
        ],
        expectedImpact: 'Renforcement de la crédibilité experte'
      });
    }

    return recommendations;
  }

  /**
   * Génère un rapport d'amélioration stratégique
   */
  generateStrategicImprovementReport(
    userInput: string,
    aiResponse: string,
    stepTitle: string
  ): string {
    const evaluation = this.evaluateStrategicQuality(userInput, aiResponse, stepTitle);
    
    let report = `=== RAPPORT D'ÉVALUATION STRATÉGIQUE ===\n\n`;
    
    // Métriques
    report += `📊 MÉTRIQUES STRATÉGIQUES :\n`;
    report += `• Cohérence Stratégique: ${evaluation.metrics.strategicConsistency}%\n`;
    report += `• Enrichissement Valeur: ${evaluation.metrics.valueEnrichment}%\n`;
    report += `• Précision Terminologique: ${evaluation.metrics.terminologyPrecision}%\n`;
    report += `• Score Stratégique Global: ${evaluation.metrics.overallStrategicScore}%\n\n`;

    // Points forts
    if (evaluation.strengthAreas.length > 0) {
      report += `✅ POINTS FORTS :\n`;
      evaluation.strengthAreas.forEach(strength => {
        report += `• ${strength}\n`;
      });
      report += '\n';
    }

    // Lacunes
    if (evaluation.strategicGaps.length > 0) {
      report += `⚠️ LACUNES STRATÉGIQUES :\n`;
      evaluation.strategicGaps.forEach(gap => {
        report += `• ${gap}\n`;
      });
      report += '\n';
    }

    // Recommandations
    if (evaluation.recommendations.length > 0) {
      report += `🎯 RECOMMANDATIONS D'AMÉLIORATION :\n\n`;
      evaluation.recommendations.forEach((rec, index) => {
        report += `${index + 1}. [${rec.priority.toUpperCase()}] ${rec.title}\n`;
        report += `   ${rec.description}\n`;
        report += `   Actions:\n`;
        rec.actionableSteps.forEach(step => {
          report += `   - ${step}\n`;
        });
        report += `   Impact attendu: ${rec.expectedImpact}\n\n`;
      });
    }

    return report;
  }
}

// Instance singleton
export const strategicMonitoringService = new StrategicMonitoringService();
