/**
 * Tests pour le service de fichiers contextuels
 */

import { contextFileService } from '../src/services/contextFileService';

// Simulation d'un fichier pour les tests
const createMockFile = (name: string, content: string, type: string = 'text/plain'): File => {
  const blob = new Blob([content], { type });
  const file = new File([blob], name, { type });
  return file;
};

// Test 1: Ajout d'un fichier texte
console.log('🧪 Test 1: Ajout d\'un fichier texte');
const testFile = createMockFile('test.txt', 'Ceci est un contenu de test pour l\'expert-conseil');

contextFileService.addContextFile(testFile)
  .then(contextFile => {
    console.log('✅ Fichier ajouté avec succès:', contextFile.name);
    console.log('📄 Contenu extrait:', contextFile.extractedText?.substring(0, 50) + '...');
    
    // Test 2: Génération du contexte global
    console.log('\n🧪 Test 2: Génération du contexte global');
    const globalContext = contextFileService.generateGlobalContext();
    console.log('📋 Contexte global généré:', globalContext.length, 'caractères');
    console.log('🔍 Aperçu:', globalContext.substring(0, 200) + '...');
    
    // Test 3: Statistiques
    console.log('\n🧪 Test 3: Statistiques des fichiers');
    const stats = contextFileService.getFileStats();
    console.log('📊 Statistiques:', stats);
    
    // Test 4: Analyse du fichier
    console.log('\n🧪 Test 4: Analyse du fichier');
    return contextFileService.analyzeFile(contextFile.id);
  })
  .then(analysis => {
    if (analysis) {
      console.log('🔬 Analyse réussie:', analysis.summary);
      console.log('🎯 Points clés:', analysis.keyPoints);
    }
    
    console.log('\n✅ Tous les tests sont terminés avec succès !');
  })
  .catch(error => {
    console.error('❌ Erreur dans les tests:', error);
  });

// Test 5: Validation des types de fichiers
console.log('\n🧪 Test 5: Validation des types de fichiers');

const testFiles = [
  { name: 'valid.txt', shouldPass: true },
  { name: 'valid.jpg', shouldPass: true },
  { name: 'valid.pdf', shouldPass: true },
  { name: 'invalid.exe', shouldPass: false },
  { name: 'invalid.xyz', shouldPass: false }
];

testFiles.forEach(({ name, shouldPass }) => {
  try {
    const mockFile = createMockFile(name, 'test content');
    // Cette méthode privée ne peut pas être testée directement,
    // mais on peut tester l'ajout du fichier
    console.log(`🔍 ${name}: ${shouldPass ? 'devrait passer' : 'devrait échouer'}`);
  } catch (error) {
    console.log(`❌ ${name}: échec attendu`);
  }
});

export const runContextFileTests = () => {
  console.log('🚀 Lancement des tests de fichiers contextuels...');
  
  // Ces tests peuvent être appelés depuis la console du navigateur
  // ou intégrés dans un framework de test
  
  return {
    addFile: (file: File) => contextFileService.addContextFile(file),
    getStats: () => contextFileService.getFileStats(),
    getContext: () => contextFileService.generateGlobalContext(),
    clear: () => contextFileService.clearAllContextFiles()
  };
};
