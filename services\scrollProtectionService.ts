/**
 * Service de protection contre les blocages de scroll
 * Surveille et corrige automatiquement les problèmes de scroll causés par les animations ou erreurs API
 */

class ScrollProtectionService {
  private monitoringInterval: NodeJS.Timeout | null = null;
  private isMonitoring = false;

  /**
   * Démarre la surveillance du scroll
   */
  public startMonitoring(): void {
    if (this.isMonitoring) return;

    console.log('🛡️ Démarrage de la protection du scroll');
    this.isMonitoring = true;

    // Vérification toutes les 2 secondes
    this.monitoringInterval = setInterval(() => {
      this.checkAndFixScrollIssues();
    }, 2000);

    // Vérification immédiate
    this.checkAndFixScrollIssues();
  }

  /**
   * Arrête la surveillance du scroll
   */
  public stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    console.log('🛡️ Arrêt de la protection du scroll');
  }

  /**
   * Vérifie et corrige les problèmes de scroll
   */
  private checkAndFixScrollIssues(): void {
    try {
      let issuesFixed = false;

      // Vérifier si le scroll est bloqué sur le body
      if (document.body.style.overflow === 'hidden') {
        console.warn('🔧 Scroll bloqué détecté sur body, correction...');
        document.body.style.overflow = '';
        issuesFixed = true;
      }

      // Vérifier si le scroll est bloqué sur html
      if (document.documentElement.style.overflow === 'hidden') {
        console.warn('🔧 Scroll bloqué détecté sur html, correction...');
        document.documentElement.style.overflow = '';
        issuesFixed = true;
      }

      // Vérifier les conteneurs avec overflow hidden problématiques
      const containers = document.querySelectorAll('.overflow-hidden');
      let problematicContainers = 0;

      containers.forEach((container, index) => {
        const element = container as HTMLElement;
        // Ne pas toucher aux containers qui doivent légitimement avoir overflow hidden
        if (!element.classList.contains('rounded-2xl') &&
            !element.classList.contains('shadow-') &&
            !element.classList.contains('bg-slate-') &&
            !element.classList.contains('border-') &&
            element.scrollHeight > element.clientHeight) {

          // Seulement signaler si c'est vraiment problématique
          const computedStyle = getComputedStyle(element);
          if (computedStyle.overflowY === 'hidden' && element.scrollHeight > element.clientHeight + 10) {
            problematicContainers++;
            // Corriger seulement si c'est clairement un problème
            if (element.scrollHeight > element.clientHeight + 50) {
              element.style.overflowY = 'auto';
              console.warn(`🔧 Container ${index} corrigé: overflow-y restauré`);
              issuesFixed = true;
            }
          }
        }
      });

      // Réduire le spam de logs - seulement signaler si des corrections ont été faites
      if (issuesFixed) {
        console.log(`✅ Problèmes de scroll corrigés (${problematicContainers} containers vérifiés)`);
      }

    } catch (error) {
      console.error('❌ Erreur lors de la vérification du scroll:', error);
    }
  }

  /**
   * Force la restauration du scroll normal
   */
  public forceRestoreScroll(): void {
    console.log('🔧 Restauration forcée du scroll');
    
    // Restaurer le scroll sur tous les éléments principaux
    document.body.style.overflow = '';
    document.documentElement.style.overflow = '';
    
    // Restaurer le scroll sur le root
    const root = document.getElementById('root');
    if (root) {
      root.style.overflow = '';
    }

    // S'assurer que le CSS global n'interfère pas
    const style = document.createElement('style');
    style.textContent = `
      html, body {
        overflow-x: hidden !important;
        overflow-y: auto !important;
      }
    `;
    document.head.appendChild(style);
    
    // Supprimer le style après un délai pour éviter les conflits
    setTimeout(() => {
      document.head.removeChild(style);
    }, 1000);
  }

  /**
   * Gestionnaire d'erreur pour les problèmes de scroll liés aux erreurs API
   */
  public handleApiError(): void {
    console.log('🔧 Correction du scroll après erreur API');
    this.forceRestoreScroll();
  }

  /**
   * Gestionnaire pour les problèmes de scroll liés aux animations GSAP
   */
  public handleGsapConflict(): void {
    console.log('🔧 Correction du scroll après animation GSAP');
    this.forceRestoreScroll();
  }
}

// Instance singleton
export const scrollProtectionService = new ScrollProtectionService();

export default scrollProtectionService;
