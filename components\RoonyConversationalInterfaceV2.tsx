import React, { useState, useEffect, useRef } from 'react';
import { Brain, MessageCircle, CheckCircle, ArrowRight } from 'lucide-react';
import { ChatInterface } from './ChatInterface';
import { ContextFileUploader } from './ContextFileUploader';
import RoonyMascot from './RoonyMascot';
import { roonyConversationService, type ConversationContext } from '../services/roonyConversationService';
import type { Message } from '../types';
import type { ContextFile } from '../src/services/contextFileService';

interface RoonyConversationalInterfaceProps {
  onAnalysisReady: (analysisData: PROFData, contextFiles?: ContextFile[]) => void;
  isProcessing: boolean;
}

interface PROFData {
  personnage: string;
  role: string;
  objectif: string;
  format: string;
  rawConversation: Message[];
}

export const RoonyConversationalInterface: React.FC<RoonyConversationalInterfaceProps> = ({ 
  onAnalysisReady, 
  isProcessing 
}) => {
  const [conversationContext, setConversationContext] = useState<ConversationContext>({
    stage: 'welcome',
    collectedData: {},
    conversation: []
  });
  const [conversation, setConversation] = useState<Message[]>([]);
  const [contextFiles, setContextFiles] = useState<ContextFile[]>([]);
  const [showFileUploader, setShowFileUploader] = useState(false);
  const [isRoonyProcessing, setIsRoonyProcessing] = useState(false);
  const chatEndRef = useRef<HTMLDivElement>(null);

  // Message d'accueil initial
  useEffect(() => {
    const welcomeMessage: Message = {
      sender: 'ai',
      text: roonyConversationService.getInitialMessage()
    };
    setConversation([welcomeMessage]);
  }, []);

  const handleSendMessage = async (messageText: string) => {
    if (isRoonyProcessing || conversationContext.stage === 'ready') return;

    // Ajouter le message utilisateur
    const userMessage: Message = { sender: 'user', text: messageText };
    const updatedConversation = [...conversation, userMessage];
    setConversation(updatedConversation);
    setIsRoonyProcessing(true);

    try {
      // Mettre à jour le contexte de conversation
      const updatedContext: ConversationContext = {
        ...conversationContext,
        conversation: updatedConversation.map(msg => ({ sender: msg.sender, text: msg.text }))
      };

      // Obtenir la réponse de Rooney via le service IA
      const { roonyResponse, nextStage, updatedData } = await roonyConversationService.processUserMessage(
        messageText,
        updatedContext
      );
      
      // Ajouter la réponse de Rooney
      const aiMessage: Message = { sender: 'ai', text: roonyResponse };
      const finalConversation = [...updatedConversation, aiMessage];
      setConversation(finalConversation);

      // Mettre à jour le contexte
      setConversationContext({
        stage: nextStage,
        collectedData: updatedData,
        conversation: finalConversation.map(msg => ({ sender: msg.sender, text: msg.text }))
      });

      // Si nous avons atteint l'étape finale, déclencher l'analyse
      if (nextStage === 'ready') {
        setTimeout(() => {
          const finalProfData: PROFData = {
            personnage: updatedData.personnage || '',
            role: updatedData.role || '',
            objectif: updatedData.objectif || '',
            format: updatedData.format || '',
            rawConversation: finalConversation
          };
          onAnalysisReady(finalProfData, contextFiles.length > 0 ? contextFiles : undefined);
        }, 2000);
      }

    } catch (error) {
      console.error('Erreur lors de la communication avec Rooney:', error);
      const errorMessage: Message = { 
        sender: 'ai', 
        text: "Désolé, j'ai rencontré un problème technique. Pouvez-vous répéter votre dernière réponse ?" 
      };
      setConversation(prev => [...prev, errorMessage]);
    } finally {
      setIsRoonyProcessing(false);
    }
  };

  const getStageProgress = (): number => {
    return roonyConversationService.getStageProgress(conversationContext.stage);
  };

  const getStageTitle = (): string => {
    return roonyConversationService.getStageTitle(conversationContext.stage);
  };

  return (
    <div className="flex-grow flex flex-col overflow-hidden">
      {/* En-tête avec progression */}
      <div className="bg-gradient-to-r from-purple-900/30 to-pink-900/30 p-4 border-b border-slate-600 flex-shrink-0">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3">
            <Brain className="w-6 h-6 text-purple-400" />
            <h2 className="text-lg font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
              Entretien Stratégique avec Rooney
            </h2>
          </div>
          <div className="flex items-center gap-2">
            <MessageCircle className="w-4 h-4 text-slate-400" />
            <span className="text-sm text-slate-300">{getStageTitle()}</span>
          </div>
        </div>
        
        {/* Barre de progression */}
        <div className="w-full bg-slate-700 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500"
            style={{ width: `${getStageProgress()}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-slate-400 mt-1">
          <span>Début</span>
          <span>{getStageProgress()}% complété</span>
          <span>Prêt pour l'analyse</span>
        </div>
      </div>

      {/* Zone de conversation */}
      <div className="flex-grow flex flex-col min-h-0">
        <div className="flex-grow p-4" style={{ overflow: 'auto', scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
          <style>
            {`
              .conversation-area::-webkit-scrollbar {
                display: none;
              }
            `}
          </style>
          <div className="conversation-area">
            <ChatInterface 
              conversation={conversation} 
              isProcessing={isRoonyProcessing}
            />
            <div ref={chatEndRef} />
          </div>
        </div>

        {/* Zone de saisie utilisateur */}
        {conversationContext.stage !== 'ready' && (
          <div className="p-4 border-t border-slate-600 bg-slate-900/50 flex-shrink-0">
            <div className="flex flex-col gap-3">
              {/* Saisie de message */}
              <div className="flex gap-3">
                <input
                  type="text"
                  placeholder="Tapez votre réponse..."
                  className="flex-grow bg-slate-700 border border-slate-600 rounded-xl px-4 py-3 text-slate-200 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                      handleSendMessage(e.currentTarget.value);
                      e.currentTarget.value = '';
                    }
                  }}
                  disabled={isRoonyProcessing}
                />
                <button
                  onClick={() => {
                    const input = document.querySelector('input') as HTMLInputElement;
                    if (input?.value.trim()) {
                      handleSendMessage(input.value);
                      input.value = '';
                    }
                  }}
                  disabled={isRoonyProcessing}
                  className="bg-purple-600 hover:bg-purple-500 disabled:bg-slate-600 text-white px-6 py-3 rounded-xl transition-colors flex items-center gap-2"
                >
                  <ArrowRight className="w-4 h-4" />
                </button>
              </div>

              {/* Fichiers contextuels (optionnel) */}
              <div className="flex items-center justify-between">
                <button
                  type="button"
                  onClick={() => setShowFileUploader(!showFileUploader)}
                  className="text-sm text-slate-400 hover:text-slate-300 transition-colors flex items-center gap-2"
                >
                  📎 Fichiers contextuels {contextFiles.length > 0 && `(${contextFiles.length})`}
                </button>
                {conversationContext.stage === 'synthesis' && (
                  <div className="flex items-center gap-2 text-sm text-green-400">
                    <CheckCircle className="w-4 h-4" />
                    Informations collectées
                  </div>
                )}
              </div>

              {showFileUploader && (
                <div className="bg-slate-800/70 p-3 rounded-xl border border-slate-700">
                  <ContextFileUploader onFilesChange={setContextFiles} />
                </div>
              )}
            </div>
          </div>
        )}

        {/* Animation de fin */}
        {conversationContext.stage === 'ready' && (
          <div className="p-6 text-center flex-shrink-0">
            <RoonyMascot 
              animation="typing" 
              size={120} 
              duration={0}
              className="mx-auto mb-4"
            />
            <p className="text-slate-300 text-lg">
              Analyse en cours... Rooney traite vos informations 🧠
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default RoonyConversationalInterface;
