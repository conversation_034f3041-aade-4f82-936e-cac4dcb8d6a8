import type { Message } from '../types';
import { WORKFLOW_STEPS } from '../constants';
import { enhancedReportGenerationService, type StrategicSynthesisReport } from '../src/services/enhancedReportGenerationService';
import { workflowMemoryService } from './workflowMemoryService';

// Interface pour les données du rapport
export interface ReportData {
  initialProblem: string;
  conversation: Message[];
  finalPrompt: string;
  reasoningLog: string[];
  currentStepIndex: number;
}

// FONCTION PUBLIQUE PRINCIPALE : Utilise le nouveau système par défaut
export const generateStrategicReport = (data: ReportData): string => {
  return generateEnhancedStrategicReport(data);
};

// NOUVELLE FONCTION : Génération selon les principes de Gemini
export const generateEnhancedStrategicReport = (data: ReportData): string => {
  const { initialProblem, conversation, finalPrompt, currentStepIndex } = data;
  
  try {
    // S'assurer que le service a des données à analyser
    if (conversation.length < 2) {
      return generateLegacyStrategicReport(data);
    }

    // Utiliser le nouveau service amélioré
    const insights = workflowMemoryService.getAllInsights();
    
    // Auto-track des corrections dans la conversation
    conversation.forEach((message, index) => {
      if (message.sender === 'user' && message.text.toLowerCase().includes('correction')) {
        const technicalValues = message.text.match(/(\d+[\s]*[kKWwHh]+)/g);
        if (technicalValues) {
          technicalValues.forEach(value => {
            enhancedReportGenerationService.trackDataState(
              `correction_${index}`,
              value,
              'USER'
            );
          });
        }
      }
    });

    const strategicReport = enhancedReportGenerationService.generateStrategicSynthesisReport(
      conversation,
      initialProblem,
      finalPrompt,
      insights
    );
    
    return enhancedReportGenerationService.formatReportAsMarkdown(strategicReport);
  } catch (error) {
    console.warn('Échec de génération du rapport amélioré, fallback vers l\'ancien système:', error);
    return generateLegacyStrategicReport(data);
  }
};

// FONCTION LEGACY : Ancien système (conservé comme fallback)
export const generateLegacyStrategicReport = (data: ReportData): string => {
  const { initialProblem, conversation, finalPrompt, reasoningLog, currentStepIndex } = data;
  
  // Extraire les recommandations de la conversation
  const recommendations = extractRecommendations(conversation);
  
  // Extraire les étapes complétées
  const completedSteps = WORKFLOW_STEPS.slice(0, currentStepIndex);
  
  const report = `# RAPPORT STRATÉGIQUE - STUDIO AGENTIQUE ROONY (Legacy)

## 📋 RÉSUMÉ EXÉCUTIF

**Problème Initial :** ${initialProblem}

**Étapes Complétées :** ${currentStepIndex}/${WORKFLOW_STEPS.length}

**Date de Génération :** ${new Date().toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })}

⚠️ *Ce rapport utilise l'ancien système. Pour une meilleure synthèse, utilisez le générateur amélioré.*

---

## 🎯 ANALYSE DU WORKFLOW

### Étapes Réalisées

${completedSteps.map((step, index) => `
**${step.id}. ${step.title}**
- *Description :* ${step.description}
- *Techniques utilisées :* ${step.techniques.join(', ')}
- *Type de tâche :* ${step.task}
`).join('\n')}

---

## 💡 RECOMMANDATIONS STRATÉGIQUES

${recommendations.length > 0 ? recommendations.map((rec, index) => `
### ${index + 1}. ${rec.title}

**Contexte :** ${rec.context}

**Recommandation :** ${rec.recommendation}

**Actions suggérées :**
${rec.actions.map(action => `- ${action}`).join('\n')}

**Priorité :** ${rec.priority}

---
`).join('\n') : 'Aucune recommandation spécifique extraite de la conversation.'}

## 🤖 MODÈLES IA UTILISÉS

${reasoningLog.map(log => `- ${log}`).join('\n')}

---

## 📊 ANALYSE DE LA CONVERSATION

**Nombre total d'échanges :** ${conversation.length}

**Messages utilisateur :** ${conversation.filter(msg => msg.sender === 'user').length}

**Réponses IA :** ${conversation.filter(msg => msg.sender === 'ai').length}

### Progression du Dialogue

${conversation.slice(0, 10).map((msg, index) => `
**${index + 1}. ${msg.sender === 'user' ? 'UTILISATEUR' : 'IA AGENT'} :**
${msg.text.substring(0, 200)}${msg.text.length > 200 ? '...' : ''}
`).join('\n')}

${conversation.length > 10 ? `\n*[${conversation.length - 10} messages supplémentaires dans la conversation complète]*` : ''}

---

## 🎯 PROMPT FINAL OPTIMISÉ

${finalPrompt.split('---')[0]?.replace('### Le Prompt Optimisé', '').trim() || 'Prompt en cours de génération...'}

---

## 📈 MÉTA-ANALYSE

${finalPrompt.split('---')[1]?.replace('### Méta-Analyse de la Construction', '').trim() || 'Méta-analyse en cours de génération...'}

---

## 🚀 PROCHAINES ÉTAPES RECOMMANDÉES

1. **Tester le prompt généré** dans votre contexte spécifique
2. **Itérer sur les résultats** en utilisant la fonction "Affiner ce Prompt"
3. **Documenter les performances** pour optimisations futures
4. **Partager les apprentissages** avec votre équipe
5. **Planifier l'implémentation** des recommandations stratégiques

---

## 📞 SUPPORT & RESSOURCES

**Développé par :** FlexoDiv - Studio Agentique
**Version :** Roony 3.0
**Contact :** <EMAIL>
**Documentation :** https://github.com/cisco-03/Studio-Agentique-Roony

---

*Ce rapport a été généré automatiquement par le Studio Agentique Roony. Pour toute question ou amélioration, n'hésitez pas à nous contacter.*`;

  return report;
};

// Fonction pour extraire les recommandations de la conversation
const extractRecommendations = (conversation: Message[]): Array<{
  title: string;
  context: string;
  recommendation: string;
  actions: string[];
  priority: 'Haute' | 'Moyenne' | 'Faible';
}> => {
  const recommendations: Array<{
    title: string;
    context: string;
    recommendation: string;
    actions: string[];
    priority: 'Haute' | 'Moyenne' | 'Faible';
  }> = [];

  // Analyser les messages IA pour extraire les recommandations
  conversation.forEach((message, index) => {
    if (message.sender === 'ai') {
      const text = message.text.toLowerCase();
      
      // Détecter les mots-clés de recommandations
      const recommendationKeywords = [
        'recommande', 'suggère', 'conseille', 'propose', 'devriez',
        'il faut', 'nécessaire', 'important', 'crucial', 'essentiel'
      ];
      
      const hasRecommendation = recommendationKeywords.some(keyword => 
        text.includes(keyword)
      );
      
      if (hasRecommendation) {
        // Extraire le contexte (50 premiers mots)
        const words = message.text.split(' ');
        const context = words.slice(0, 50).join(' ') + (words.length > 50 ? '...' : '');
        
        // Déterminer la priorité basée sur les mots-clés
        let priority: 'Haute' | 'Moyenne' | 'Faible' = 'Moyenne';
        if (text.includes('crucial') || text.includes('essentiel') || text.includes('urgent')) {
          priority = 'Haute';
        } else if (text.includes('pourrait') || text.includes('éventuellement')) {
          priority = 'Faible';
        }
        
        recommendations.push({
          title: `Recommandation ${recommendations.length + 1}`,
          context,
          recommendation: message.text,
          actions: extractActions(message.text),
          priority
        });
      }
    }
  });

  return recommendations.slice(0, 10); // Limiter à 10 recommandations max
};

// Fonction pour extraire les actions d'un texte
const extractActions = (text: string): string[] => {
  const actions: string[] = [];
  const actionPatterns = [
    /(?:vous devriez|il faut|nécessaire de|important de)\s+([^.!?]+)/gi,
    /(?:action|étape|mesure)\s*:\s*([^.!?]+)/gi,
    /(?:^|\n)\s*[-•]\s*([^.!?\n]+)/gm
  ];
  
  actionPatterns.forEach(pattern => {
    const matches = text.matchAll(pattern);
    for (const match of matches) {
      if (match[1] && match[1].trim().length > 10) {
        actions.push(match[1].trim());
      }
    }
  });
  
  return actions.slice(0, 5); // Limiter à 5 actions par recommandation
};

// Fonction pour exporter en PDF (utilise la génération HTML puis conversion)
export const exportToPDF = (reportContent: string, filename: string = 'rapport-strategique-roony') => {
  // Convertir le markdown en HTML basique pour l'impression
  const htmlContent = markdownToHTML(reportContent);
  
  // Créer une nouvelle fenêtre pour l'impression
  const printWindow = window.open('', '_blank');
  if (printWindow) {
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>${filename}</title>
        <meta charset="utf-8">
        <style>
          body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            max-width: 800px; 
            margin: 0 auto; 
            padding: 20px;
          }
          h1 { color: #2563eb; border-bottom: 3px solid #2563eb; padding-bottom: 10px; }
          h2 { color: #1e40af; margin-top: 30px; }
          h3 { color: #1e3a8a; }
          .meta-info { background: #f8fafc; padding: 15px; border-left: 4px solid #2563eb; margin: 20px 0; }
          .recommendation { background: #f0f9ff; padding: 15px; margin: 15px 0; border-radius: 8px; }
          .priority-high { border-left: 4px solid #dc2626; }
          .priority-medium { border-left: 4px solid #f59e0b; }
          .priority-low { border-left: 4px solid #10b981; }
          code { background: #f1f5f9; padding: 2px 4px; border-radius: 3px; }
          pre { background: #f8fafc; padding: 15px; border-radius: 8px; overflow-x: auto; }
          @media print {
            body { font-size: 12px; }
            h1 { font-size: 18px; }
            h2 { font-size: 16px; }
            h3 { font-size: 14px; }
          }
        </style>
      </head>
      <body>
        ${htmlContent}
      </body>
      </html>
    `);
    printWindow.document.close();
    
    // Attendre que le contenu soit chargé puis déclencher l'impression
    setTimeout(() => {
      printWindow.print();
    }, 500);
  }
};

// Fonction simple pour convertir markdown en HTML
const markdownToHTML = (markdown: string): string => {
  return markdown
    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    .replace(/^\*\*(.*)\*\*/gm, '<strong>$1</strong>')
    .replace(/^\*(.*)\*/gm, '<em>$1</em>')
    .replace(/^- (.*$)/gm, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')
    .replace(/^---$/gm, '<hr>')
    .replace(/\n\n/g, '</p><p>')
    .replace(/^(.*)$/gm, '<p>$1</p>')
    .replace(/<p><h/g, '<h')
    .replace(/<\/h([1-6])><\/p>/g, '</h$1>')
    .replace(/<p><hr><\/p>/g, '<hr>')
    .replace(/<p><ul>/g, '<ul>')
    .replace(/<\/ul><\/p>/g, '</ul>');
};
