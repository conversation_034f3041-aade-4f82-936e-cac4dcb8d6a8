import { useState, useCallback, useRef, useEffect } from 'react';
import type { RoonyAnimationType } from '../components/RoonyMascot';

interface RoonyAnimationState {
  currentAnimation: RoonyAnimationType;
  isVisible: boolean;
  position: { x: number; y: number } | null;
}

interface UseRoonyAnimationsReturn {
  /** État actuel de l'animation */
  animationState: RoonyAnimationState;
  /** Déclencher une animation de Roony */
  triggerAnimation: (animation: RoonyAnimationType, duration?: number, position?: { x: number; y: number }) => void;
  /** Masquer Roony */
  hideRoony: () => void;
  /** Déclencher une animation contextuelle basée sur l'événement */
  triggerContextualAnimation: (context: RoonyContextType, stepIndex?: number) => void;
}

// Types de contextes pour déclencher automatiquement les bonnes animations
export type RoonyContextType =
  | 'workflow-start'      // Début du workflow
  | 'step-complete'       // Étape terminée avec succès
  | 'idea-found'          // Une idée ou solution trouvée
  | 'processing'          // En cours de traitement/recherche
  | 'error'               // Erreur ou problème
  | 'final-result'        // Résultat final généré
  | 'user-input'          // Utilisateur saisit quelque chose
  | 'pointing-suggestion' // Pointer vers une suggestion
  | 'celebration'         // Célébration d'un succès
  | 'thinking';           // Réflexion en cours

// Mapping des étapes du workflow vers des animations spécifiques (une animation par étape)
const STEP_TO_ANIMATION: Record<number, RoonyAnimationType> = {
  0: 'idea',           // Définition du Problème - idée
  1: 'typing',         // Diagnostic Initial - recherche
  2: 'pointing-up',    // Exploration Arborescente - inspiration
  3: 'idea',           // Génération de Solutions - idée brillante
  4: 'proud',          // Validation Éthique - fier du travail
  5: 'typing',         // Simulation Prédictive - calculs
  6: 'pointing-up',    // Prototypage Adaptatif - construction
  7: 'idea',           // Déploiement Contextuel - adaptation
  8: 'typing',         // Suivi Dynamique - surveillance
  9: 'proud',          // Boucle d'Amélioration - satisfaction
  10: 'idea',          // Capitalisation Cognitive - archivage
  11: 'typing',        // Validation Transversale - vérification
  12: 'pointing-up',   // Communication Stratégique - présentation
  13: 'proud'          // Synthèse et Plan d'Action - fierté finale
};

// Index global pour la rotation des animations de réflexion - SUPPRIMÉ
// let thinkingAnimationIndex = 0;

/**
 * Hook personnalisé pour gérer les animations contextuelles de Roony
 * Permet de déclencher automatiquement les bonnes animations selon le contexte
 */
export const useRoonyAnimations = (): UseRoonyAnimationsReturn => {
  const [animationState, setAnimationState] = useState<RoonyAnimationState>({
    currentAnimation: 'idle',
    isVisible: false,
    position: null
  });

  const animationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // CORRECTIF: Protection contre les conflits de scroll lors des animations
  useEffect(() => {
    // S'assurer que le scroll n'est jamais bloqué par les animations Roony
    const preventScrollBlocking = () => {
      if (document.body.style.overflow === 'hidden') {
        console.warn('🔧 Correction automatique: Scroll bloqué détecté, restauration...');
        document.body.style.overflow = '';
        document.documentElement.style.overflow = '';
      }
    };

    // Vérification périodique pour éviter les blocages
    const scrollCheckInterval = setInterval(preventScrollBlocking, 1000);

    return () => {
      clearInterval(scrollCheckInterval);
      // Nettoyage au démontage du hook
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
    };
  }, []);

  // Fonction pour déclencher une animation spécifique
  const triggerAnimation = useCallback((
    animation: RoonyAnimationType, 
    duration: number = 3000,
    position?: { x: number; y: number }
  ) => {
    // Nettoyer le timeout précédent si il existe
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
    }

    // Définir la nouvelle animation
    setAnimationState({
      currentAnimation: animation,
      isVisible: animation !== 'idle',
      position: position || null
    });

    // Programmer la fin de l'animation si une durée est spécifiée
    if (duration > 0 && animation !== 'idle') {
      animationTimeoutRef.current = setTimeout(() => {
        setAnimationState(prev => ({
          ...prev,
          currentAnimation: 'idle',
          isVisible: false
        }));
      }, duration);
    }
  }, []);

  // Fonction pour masquer Roony immédiatement
  const hideRoony = useCallback(() => {
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
    }
    setAnimationState({
      currentAnimation: 'idle',
      isVisible: false,
      position: null
    });
  }, []);

  // Fonction pour obtenir l'animation selon l'étape du workflow (plus de rotation)
  const getAnimationForStep = useCallback((stepIndex: number): RoonyAnimationType => {
    const animation = STEP_TO_ANIMATION[stepIndex] || 'idea'; // animation par défaut
    console.log(`🎭 Animation de réflexion pour l'étape ${stepIndex}: ${animation}`);
    return animation;
  }, []);

  // Fonction pour déclencher des animations contextuelles
  const triggerContextualAnimation = useCallback((context: RoonyContextType, stepIndex: number = 0) => {
    switch (context) {
      case 'thinking':
        // Roony utilise une animation spécifique selon l'étape du workflow
        const thinkingAnimation = getAnimationForStep(stepIndex);
        triggerAnimation(thinkingAnimation, 3000);
        break;

      default:
        // Toutes les autres animations sont désactivées
        hideRoony();
        break;
    }
  }, [triggerAnimation, hideRoony, getAnimationForStep]);

  return {
    animationState,
    triggerAnimation,
    hideRoony,
    triggerContextualAnimation
  };
};

export default useRoonyAnimations;
