import React, { useState, useEffect } from 'react';
import { Brain, ArrowR<PERSON>, <PERSON>rk<PERSON>, Target, MessageCircle, ArrowLeft } from 'lucide-react';
import { ContextFileUploader } from './ContextFileUploader';
import { WorkflowTracker } from './WorkflowTracker';
import { UserGuidance } from './UserGuidance';
import { EngagementPrompts } from './EngagementPrompts';
import { FinalActionPlanGenerator } from './FinalActionPlanGenerator';
import { ReportGenerator } from './ReportGenerator';
import { ChatInterface } from './ChatInterface';
import { UserInput } from './UserInput';
import { ThinkingSpace } from './ThinkingSpace';
import { ModelMonitor } from './ModelMonitor';
import LanguageComplianceMonitor from './LanguageComplianceMonitor';
import TranslationMonitor from './TranslationMonitor';
import { ProfileBackup } from './ProfileBackup';
import ApiKeyStats from './ApiKeyStats';
import ModelUpdateNotification from './ModelUpdateNotification';
import { WORKFLOW_STEPS } from '../constants';
import type { ContextFile } from '../src/services/contextFileService';
import type { Message } from '../types';

interface CompleteAnalysisInterfaceProps {
  onSubmit: (prompt: string, contextFiles?: ContextFile[]) => void;
  isProcessing: boolean;
  conversation?: Message[];
  reasoningLog?: string[];
  currentStepIndex?: number;
  onSendMessage?: (message: string) => void;
  onBackToSelection?: () => void;
  error?: string;
}

export const CompleteAnalysisInterface: React.FC<CompleteAnalysisInterfaceProps> = ({ 
  onSubmit, 
  isProcessing,
  conversation = [],
  reasoningLog = [],
  currentStepIndex = 0,
  onSendMessage,
  onBackToSelection,
  error
}) => {
  const [isAnalysisStarted, setIsAnalysisStarted] = useState(false);
  const [showApiStats, setShowApiStats] = useState(false);
  const [initialProblem, setInitialProblem] = useState('');
  const [directProblemText, setDirectProblemText] = useState('');
  const [showContextFiles, setShowContextFiles] = useState(false);

  const handleDirectSubmit = () => {
    if (directProblemText.trim()) {
      setInitialProblem(directProblemText.trim());
      setIsAnalysisStarted(true);
      onSubmit(directProblemText.trim());
    }
  };

  const handleSuggestedAction = (suggestion: string) => {
    if (onSendMessage) {
      onSendMessage(suggestion);
    }
  };

  // Détecter quand l'analyse démarre automatiquement
  useEffect(() => {
    // Si on a une conversation avec du contenu et qu'on n'est pas encore en analyse
    if (conversation.length > 0 && !isAnalysisStarted) {
      setIsAnalysisStarted(true);
      // Extraire le problème initial de la première interaction utilisateur
      const userMessages = conversation.filter(msg => msg.sender === 'user');
      if (userMessages.length > 0) {
        setInitialProblem(userMessages[0].text);
      }
    }
  }, [conversation, isAnalysisStarted]);

  // Si l'analyse a démarré, afficher l'interface 3 colonnes
  if (isAnalysisStarted) {
    return (
      <div className="flex-grow flex container mx-auto p-4 gap-4 overflow-hidden">
        {/* COLONNE GAUCHE - Progression + Suggestions + Outils (25%) */}
        <aside className="w-1/4 flex flex-col gap-4">
          <div className="bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 overflow-hidden">
            <WorkflowTracker steps={WORKFLOW_STEPS} currentStepIndex={currentStepIndex || 0} />
          </div>

          <div className="bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 p-4">
            <UserGuidance
              currentStep={currentStepIndex || 0}
              totalSteps={WORKFLOW_STEPS.length}
              isProcessing={isProcessing}
            />
          </div>

          {/* Suggestions d'engagement */}
          {!isProcessing && (currentStepIndex || 0) < WORKFLOW_STEPS.length - 1 && (currentStepIndex || 0) > 0 && (
            <div className="bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 p-4">
              <EngagementPrompts
                currentStep={currentStepIndex || 0}
                totalSteps={WORKFLOW_STEPS.length}
                onSuggestedAction={handleSuggestedAction}
              />
            </div>
          )}

          {/* Générateur de Plan d'Action Final */}
          <FinalActionPlanGenerator
            initialProblem={initialProblem}
            currentStepIndex={currentStepIndex || 0}
            className="flex-shrink-0"
          />

          {/* Générateur de rapport stratégique */}
          {initialProblem && (currentStepIndex || 0) > 1 && (
            <div className="bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 p-4">
              <ReportGenerator
                initialProblem={initialProblem}
                conversation={conversation}
                reasoningLog={reasoningLog}
                currentStepIndex={currentStepIndex || 0}
              />
            </div>
          )}
        </aside>

        {/* COLONNE CENTRE - Chat + Input (50%) */}
        <div className="flex-1 flex flex-col bg-slate-800/50 rounded-2xl shadow-2xl border border-slate-700 overflow-hidden">
          <ChatInterface conversation={conversation} isProcessing={isProcessing} />
          <div className="p-4 border-t border-slate-700">
            {error && <p className="text-red-400 text-center mb-2">{error}</p>}
            {onSendMessage && (
              <UserInput
                isProcessing={isProcessing}
                onSendMessage={onSendMessage}
                isWorkflowStarted={true}
                isWorkflowComplete={(currentStepIndex || 0) >= WORKFLOW_STEPS.length - 1}
                lastAiMessage={conversation.length > 0 ? conversation[conversation.length - 1]?.sender === 'ai' ? conversation[conversation.length - 1]?.text : undefined : undefined}
              />
            )}
          </div>
        </div>

        {/* COLONNE DROITE - Raisonnement + Modèles + Outils (25%) */}
        <aside className="w-1/4 flex flex-col gap-4">
          {/* Bouton de retour à l'accueil */}
          {onBackToSelection && (
            <div className="flex-shrink-0">
              <button
                onClick={onBackToSelection}
                className="w-full bg-slate-700 hover:bg-slate-600 text-slate-200 text-sm font-semibold py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Retour à l'accueil
              </button>
            </div>
          )}

          <ThinkingSpace log={reasoningLog} className="flex-grow" />
          <ModelMonitor className="flex-shrink-0" />
          <LanguageComplianceMonitor className="flex-shrink-0" />
          <TranslationMonitor className="flex-shrink-0" />

          {/* Composant de sauvegarde du profil utilisateur */}
          <ProfileBackup className="flex-shrink-0" />

          {/* Bouton pour afficher les statistiques des clés API */}
          <div className="flex-shrink-0">
            <button
              onClick={() => setShowApiStats(true)}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-lg"
            >
              📊 Statistiques API
            </button>
          </div>

          {/* Notification de mise à jour des modèles */}
          <ModelUpdateNotification className="flex-shrink-0" />

          <footer className="flex-shrink-0 text-center py-2">
            <a href="https://flexodiv.com" target="_blank" rel="noopener noreferrer" className="inline-block">
              <img src="https://res.cloudinary.com/dte5zykne/image/upload/v1750807840/02-Logo-FlexoDiv_uoxcao.png" alt="Logo FlexoDiv" className="h-10 mx-auto opacity-60 hover:opacity-100 transition-opacity" />
            </a>
          </footer>
        </aside>

        {/* Modal des statistiques API */}
        <ApiKeyStats
          isVisible={showApiStats}
          onClose={() => setShowApiStats(false)}
        />
      </div>
    );
  }

  // Interface de démarrage simple : JUSTE "Décrire le Problème" + bouton "Fichiers Contextuels"
  return (
    <div className="flex-grow flex flex-col justify-center items-center p-4 text-center w-full">
      
      <textarea
        value={directProblemText}
        onChange={(e) => setDirectProblemText(e.target.value)}
        placeholder="Décrivez votre problème, situation ou défi en détail..."
        className="w-full max-w-none h-80 bg-slate-700/50 border border-slate-600 rounded-xl p-6 text-slate-200 placeholder-slate-400 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent mb-6 text-lg"
        disabled={isProcessing}
      />
      
      <div className="flex flex-col items-center gap-4">
        <button
          onClick={handleDirectSubmit}
          disabled={!directProblemText.trim() || isProcessing}
          className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 disabled:from-slate-600 disabled:to-slate-600 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100 flex items-center gap-2"
        >
          {isProcessing ? (
            <>
              <svg className="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Analyse en cours...
            </>
          ) : (
            <>
              Lancer l'Analyse
              <ArrowRight className="w-5 h-5" />
            </>
          )}
        </button>

        <button
          onClick={() => setShowContextFiles(!showContextFiles)}
          className="text-slate-400 hover:text-slate-200 text-sm underline transition-colors"
        >
          📎 Fichiers Contextuels (Optionnel)
        </button>
      </div>

      {/* Fichiers Contextuels - Affichage conditionnel */}
      {showContextFiles && (
        <div className="mt-6 w-full max-w-2xl">
          <ContextFileUploader onFilesChange={() => {}} />
        </div>
      )}
    </div>
  );
};

export default CompleteAnalysisInterface;
