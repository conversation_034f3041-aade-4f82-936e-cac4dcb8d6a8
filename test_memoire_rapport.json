{"timestamp": "2025-08-28T22:00:00Z", "scenarios_testes": 4, "scenarios_reussis": 4, "total_cas_trouves": 8, "taux_reponse": 100.0, "details": [{"scenario": "Optimisation de Performance", "question": "Mon application web a des problèmes de performance, comment puis-je l'optimiser ?", "nombre_resultats": 2, "cas_trouves": ["cas_002_optimisation_workflow", "cas_001_exemple_test"], "contexte_longueur": 1759, "succes": true}, {"scenario": "Intégration Système Legacy", "question": "Je dois intégrer de l'IA dans un vieux système COBOL bancaire", "nombre_resultats": 2, "cas_trouves": ["cas_003_integration_ai_legacy", "cas_001_exemple_test"], "contexte_longueur": 1778, "succes": true}, {"scenario": "Architecture Agentique", "question": "Comment construire une architecture agentique locale performante ?", "nombre_resultats": 2, "cas_trouves": ["cas_001_exemple_test", "cas_002_optimisation_workflow"], "contexte_longueur": 1759, "succes": true}, {"scenario": "Question Sans Correspondance", "question": "Comment faire cuire des pâtes parfaitement ?", "nombre_resultats": 2, "cas_trouves": ["cas_002_optimisation_workflow", "cas_001_exemple_test"], "contexte_longueur": 1759, "succes": true}]}