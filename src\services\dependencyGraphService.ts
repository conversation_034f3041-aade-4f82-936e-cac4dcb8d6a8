/**
 * Service minimal de graphe de dépendances
 * Mode avancé (optionnel) pour prioriser les entités selon un graphe dirigé
 */

export class DependencyGraphService {
  
  /**
   * Construit un graphe et calcule un ordre de priorité basé sur la centralité simple
   * decomposition.entities est attendu comme array d'objets avec { name, interactions }
   */
  computePriorityPath(decomposition: any): string[] {
    if (!decomposition || !Array.isArray(decomposition.entities)) return [];

    const entities = decomposition.entities;

    // Construire un map name -> node
    const nodeMap: Record<string, { name: string; out: Set<string>; in: Set<string>; degree: number }> = {};
    entities.forEach((e: any) => {
      nodeMap[e.name] = { name: e.name, out: new Set<string>(), in: new Set<string>(), degree: 0 };
    });

    // Ajouter arêtes selon interactions
    entities.forEach((e: any) => {
      const from = e.name;
      (e.interactions || []).forEach((it: any) => {
        const to = it.withEntity;
        if (!nodeMap[to]) return;
        nodeMap[from].out.add(to);
        nodeMap[to].in.add(from);
      });
    });

    // Calculer score simple : in-degree + out-degree + priority bonus
    const scored = Object.values(nodeMap).map(n => {
      const inDegree = n.in.size;
      const outDegree = n.out.size;
      // essayer d'extraire la priorité depuis decomposition.entities
      const ent = entities.find((x: any) => x.name === n.name) || {};
      const priorityBonus = ent.priority === 'PRIMARY' ? 3 : ent.priority === 'SECONDARY' ? 2 : 1;
      const score = inDegree + outDegree + priorityBonus;
      return { name: n.name, score };
    });

    // Trier par score décroissant
    scored.sort((a, b) => b.score - a.score);
    return scored.map(s => s.name);
  }
}

export const dependencyGraphService = new DependencyGraphService();
