import type { Message } from '../types';
import { operationalFeasibilityFilter, type ActionToValidate } from '../src/services/operationalFeasibilityFilter';

export interface WorkflowInsight {
  stepIndex: number;
  stepTitle: string;
  userInput: string;
  aiAnalysis: string;
  keyRecommendations: string[];
  timestamp: Date;
}

export interface FinalActionPlan {
  executiveSummary: string;
  keyFindings: string[];
  prioritizedActions: {
    priority: 'HIGH' | 'MEDIUM' | 'LOW';
    action: string;
    rationale: string;
    timeline: string;
    feasibilityValidated?: boolean;
    originalAction?: string; // Pour garder une trace de l'action originale
  }[];
  nextSteps: string[];
  successMetrics: string[];
  potentialRisks: string[];
  feasibilityReport?: string; // Rapport du filtre de faisabilité
  overallFeasibilityScore?: 'HIGH' | 'MEDIUM' | 'LOW';
}

class WorkflowMemoryService {
  private insights: WorkflowInsight[] = [];
  private finalPlan: FinalActionPlan | null = null;

  /**
   * Ajouter une nouvelle analyse d'étape
   */
  addInsight(insight: WorkflowInsight): void {
    this.insights.push(insight);
    console.log(`📝 Nouvelle analyse ajoutée pour l'étape ${insight.stepIndex}: ${insight.stepTitle}`);
  }

  /**
   * Extraire automatiquement les recommandations d'un texte IA
   */
  extractRecommendations(aiText: string): string[] {
    const recommendations: string[] = [];
    
    // Rechercher des patterns de recommandations
    const patterns = [
      /(?:je recommande|je suggère|il faudrait|vous devriez|il serait bien de|considérez)\s+([^.!?]+)/gi,
      /(?:recommandation|suggestion|conseil)\s*:\s*([^.!?]+)/gi,
      /(?:étape suivante|prochaine étape|action)\s*:\s*([^.!?]+)/gi,
      /(?:important|crucial|essentiel)\s+(?:de|d')\s+([^.!?]+)/gi
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(aiText)) !== null) {
        const recommendation = match[1].trim();
        if (recommendation.length > 10 && !recommendations.includes(recommendation)) {
          recommendations.push(recommendation);
        }
      }
    });

    return recommendations.slice(0, 5); // Limiter à 5 recommandations max
  }

  /**
   * Analyser une conversation et extraire les insights
   */
  analyzeConversationStep(
    stepIndex: number,
    stepTitle: string,
    userMessage: string,
    aiResponse: string
  ): void {
    const recommendations = this.extractRecommendations(aiResponse);
    
    const insight: WorkflowInsight = {
      stepIndex,
      stepTitle,
      userInput: userMessage,
      aiAnalysis: aiResponse,
      keyRecommendations: recommendations,
      timestamp: new Date()
    };

    this.addInsight(insight);
  }

  /**
   * Générer le plan d'action final basé sur tous les insights
   * VERSION AMÉLIORÉE - PLAN DÉTAILLÉ ET CONCRET
   */
  async generateFinalActionPlan(initialProblem: string): Promise<FinalActionPlan> {
    if (this.insights.length === 0) {
      throw new Error('Aucune analyse disponible pour générer le plan final');
    }

    // Consolider toutes les recommandations avec contexte
    const allRecommendations = this.insights.flatMap(insight => 
      insight.keyRecommendations.map(rec => ({
        recommendation: rec,
        stepTitle: insight.stepTitle,
        stepIndex: insight.stepIndex,
        context: insight.userInput.substring(0, 100)
      }))
    );

    // Déduplication intelligente (garder le contexte le plus récent)
    const uniqueRecommendations = allRecommendations.reduce((acc, current) => {
      const existing = acc.find(item => 
        item.recommendation.toLowerCase().includes(current.recommendation.toLowerCase().substring(0, 30))
      );
      if (!existing) {
        acc.push(current);
      } else if (current.stepIndex > existing.stepIndex) {
        // Remplacer par la version plus récente
        acc[acc.indexOf(existing)] = current;
      }
      return acc;
    }, [] as typeof allRecommendations);

    // Analyser les patterns et priorités avec plus de nuance
    const highPriorityKeywords = ['urgent', 'critique', 'immédiat', 'essentiel', 'crucial', 'd\'abord', 'priorité'];
    const mediumPriorityKeywords = ['important', 'recommandé', 'suggéré', 'devrait', 'conseillé', 'nécessaire'];

    const prioritizedActions = uniqueRecommendations.map((item, index) => {
      let priority: 'HIGH' | 'MEDIUM' | 'LOW' = 'LOW';
      const text = item.recommendation.toLowerCase();
      
      // Logique de priorité améliorée
      if (highPriorityKeywords.some(keyword => text.includes(keyword)) || 
          item.stepIndex < 3) { // Les premières étapes sont généralement plus prioritaires
        priority = 'HIGH';
      } else if (mediumPriorityKeywords.some(keyword => text.includes(keyword)) ||
                 item.stepIndex < 7) {
        priority = 'MEDIUM';
      }

      // Générer une justification basée sur le contexte
      const rationale = this.generateActionRationale(item.recommendation, item.stepTitle, item.context);
      
      // Déterminer un timeline réaliste
      const timeline = this.determineRealisticTimeline(item.recommendation, priority);

      return {
        priority,
        action: this.enhanceActionDescription(item.recommendation),
        rationale,
        timeline,
        originalAction: item.recommendation // Garder une trace de l'action originale
      };
    }).sort((a, b) => {
      // Tri par priorité puis par réalisme
      const priorityOrder = { 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });

    // APPLICATION DU PRINCIPE 5 : Filtre de Faisabilité Opérationnelle
    const actionsToValidate: ActionToValidate[] = prioritizedActions.map((action, index) => ({
      title: `Action ${index + 1}`,
      description: action.action,
      timeline: action.timeline,
      priority: action.priority,
      order: index + 1,
      context: {
        domain: this.extractDomainFromInsights(),
        userSituation: initialProblem,
        countries: this.extractCountriesFromInsights(),
        sectors: this.extractSectorsFromInsights()
      }
    }));

    // Validation de faisabilité
    const feasibilityResults = operationalFeasibilityFilter.validateActionPlan(actionsToValidate);
    const feasibilityReport = operationalFeasibilityFilter.generateFeasibilityReport(actionsToValidate);

    // Mise à jour des actions avec les améliorations
    const validatedActions = prioritizedActions.map((action, index) => {
      const result = feasibilityResults[index];
      
      return {
        ...action,
        action: result.improvedAction || action.action,
        feasibilityValidated: result.isValid,
        originalAction: result.improvedAction ? action.action : undefined
      };
    });

    // Calcul du score global de faisabilité
    const criticalIssues = feasibilityResults.filter(r => 
      r.issues.some(i => i.severity === 'CRITICAL')
    ).length;
    const totalIssues = feasibilityResults.reduce((sum, result) => sum + result.issues.length, 0);
    
    let overallFeasibilityScore: 'HIGH' | 'MEDIUM' | 'LOW' = 'HIGH';
    if (criticalIssues > 0) {
      overallFeasibilityScore = 'LOW';
    } else if (totalIssues > 3) {
      overallFeasibilityScore = 'MEDIUM';
    }

    // Générer un résumé exécutif détaillé et contextuel
    const feasibilityNumericScore = overallFeasibilityScore === 'HIGH' ? 0.8 : 
                                   overallFeasibilityScore === 'MEDIUM' ? 0.6 : 0.4;
    
    const executiveSummary = this.generateDetailedExecutiveSummary(
      initialProblem, 
      uniqueRecommendations.length, 
      prioritizedActions, 
      feasibilityNumericScore
    );

    // Extraire les principales découvertes avec plus de contexte
    const keyFindings = this.generateKeyFindings();

    // Générer des étapes suivantes contextuelles et pratiques
    const nextSteps = this.generateContextualNextSteps(prioritizedActions);

    this.finalPlan = {
      executiveSummary,
      keyFindings,
      prioritizedActions: validatedActions.slice(0, 10), // Top 10 actions
      nextSteps: [
        'Réviser et valider le plan d\'action proposé',
        'Prioriser les actions selon les ressources disponibles',
        'Définir les responsabilités et échéances',
        'Mettre en place un système de suivi des progrès'
      ],
      successMetrics: [
        'Résolution du problème initial',
        'Mise en œuvre des actions prioritaires',
        'Amélioration mesurable des indicateurs clés',
        'Satisfaction des parties prenantes'
      ],
      potentialRisks: [
        'Manque de ressources pour l\'exécution',
        'Résistance au changement',
        'Complexité technique sous-estimée',
        'Délais trop optimistes'
      ],
      feasibilityReport,
      overallFeasibilityScore
    };

    return this.finalPlan;
  }

    /**
   * Extrait le domaine principal des insights collectés
   */
  private extractDomainFromInsights(): string {
    const domains = ['immigration', 'social', 'juridique', 'technique', 'stratégique', 'personnel'];
    
    for (const domain of domains) {
      const matches = this.insights.filter(insight => 
        insight.userInput.toLowerCase().includes(domain) ||
        insight.aiAnalysis.toLowerCase().includes(domain)
      );
      
      if (matches.length > 0) {
        return domain;
      }
    }
    
    return 'général';
  }

  /**
   * Génère une justification détaillée pour une action
   */
  private generateActionRationale(recommendation: string, stepTitle: string, context: string): string {
    const contextSummary = context.length > 50 ? context.substring(0, 50) + '...' : context;
    
    if (recommendation.toLowerCase().includes('urgent') || recommendation.toLowerCase().includes('immédiat')) {
      return `Action critique identifiée lors de l'étape "${stepTitle}". Contexte: ${contextSummary}`;
    } else if (recommendation.toLowerCase().includes('document') || recommendation.toLowerCase().includes('dossier')) {
      return `Préparation documentaire nécessaire suite à l'analyse "${stepTitle}". Base: ${contextSummary}`;
    } else if (recommendation.toLowerCase().includes('contact') || recommendation.toLowerCase().includes('appel')) {
      return `Communication stratégique recommandée par l'étape "${stepTitle}". Situation: ${contextSummary}`;
    } else {
      return `Mesure stratégique dérivée de l'analyse "${stepTitle}" pour traiter: ${contextSummary}`;
    }
  }

  /**
   * Détermine un timeline réaliste pour une action
   */
  private determineRealisticTimeline(recommendation: string, priority: 'HIGH' | 'MEDIUM' | 'LOW'): string {
    const text = recommendation.toLowerCase();
    
    // Timeline basé sur le type d'action
    if (text.includes('document') || text.includes('rassembl') || text.includes('organis')) {
      return priority === 'HIGH' ? '24-48h' : '3-5 jours';
    } else if (text.includes('contact') || text.includes('appel') || text.includes('rendez-vous')) {
      return priority === 'HIGH' ? 'Immédiat (aujourd\'hui)' : '2-3 jours';
    } else if (text.includes('dépôt') || text.includes('soumiss') || text.includes('envoi')) {
      return priority === 'HIGH' ? '1 semaine' : '2-3 semaines';
    } else if (text.includes('suivi') || text.includes('contrôl') || text.includes('vérif')) {
      return 'En continu (weekly)';
    } else {
      // Timeline par défaut basé sur la priorité
      switch (priority) {
        case 'HIGH': return 'Immédiat (0-72h)';
        case 'MEDIUM': return 'Court terme (1-2 semaines)';
        case 'LOW': return 'Moyen terme (3-4 semaines)';
        default: return '1 mois';
      }
    }
  }

  /**
   * Améliore la description d'une action pour la rendre plus concrète
   */
  private enhanceActionDescription(originalAction: string): string {
    let enhanced = originalAction;
    
    // Ajouter des détails concrets selon le type d'action
    if (enhanced.toLowerCase().includes('document') && !enhanced.includes('spécifique')) {
      enhanced += ' (pièces d\'identité, justificatifs de revenus, attestations)';
    } else if (enhanced.toLowerCase().includes('contact') && !enhanced.includes('service')) {
      enhanced += ' avec le service compétent ou un conseiller spécialisé';
    } else if (enhanced.toLowerCase().includes('plan') && !enhanced.includes('étapes')) {
      enhanced += ' avec échéancier détaillé et points de contrôle';
    } else if (enhanced.toLowerCase().includes('suivi') && !enhanced.includes('régulier')) {
      enhanced += ' régulier avec reporting hebdomadaire';
    }
    
    // S'assurer que l'action commence par un verbe d'action
    if (!enhanced.match(/^(Prépar|Organis|Contact|Planif|Effectu|Réalis|Constitu|Rassembl|Envoy|Dépos|Suiv|Contrôl|Vérif|Valid|Implement)/i)) {
      if (enhanced.toLowerCase().includes('document')) {
        enhanced = 'Constituer ' + enhanced.toLowerCase();
      } else if (enhanced.toLowerCase().includes('contact')) {
        enhanced = 'Contacter ' + enhanced.toLowerCase();
      } else if (enhanced.toLowerCase().includes('plan')) {
        enhanced = 'Élaborer ' + enhanced.toLowerCase();
      } else {
        enhanced = 'Réaliser ' + enhanced.toLowerCase();
      }
    }
    
    return enhanced;
  }

  /**
   * Extrait les pays mentionnés dans les insights
   */
  private extractCountriesFromInsights(): string[] {
    const countries: string[] = [];
    const allText = this.insights.map(i => `${i.userInput} ${i.aiAnalysis}`).join(' ').toLowerCase();
    
    const countryKeywords = [
      'france', 'allemagne', 'espagne', 'italie', 'belgique', 
      'suisse', 'canada', 'maroc', 'algérie', 'tunisie'
    ];
    
    countryKeywords.forEach(country => {
      if (allText.includes(country)) {
        countries.push(country);
      }
    });
    
    return countries;
  }

  /**
   * Extrait les secteurs mentionnés dans les insights
   */
  private extractSectorsFromInsights(): string[] {
    const sectors: string[] = [];
    const allText = this.insights.map(i => `${i.userInput} ${i.aiAnalysis}`).join(' ').toLowerCase();
    
    const sectorKeywords = [
      'santé', 'éducation', 'finance', 'technologie', 'immobilier',
      'commerce', 'industrie', 'agriculture', 'tourisme', 'transport'
    ];
    
    sectorKeywords.forEach(sector => {
      if (allText.includes(sector)) {
        sectors.push(sector);
      }
    });
    
    return sectors;
  }

  /**
   * Obtenir tous les insights
   */
  getAllInsights(): WorkflowInsight[] {
    return [...this.insights];
  }

  /**
   * Obtenir le plan final
   */
  getFinalPlan(): FinalActionPlan | null {
    return this.finalPlan;
  }

  /**
   * Réinitialiser la mémoire
   */
  reset(): void {
    this.insights = [];
    this.finalPlan = null;
    console.log('🔄 Mémoire du workflow réinitialisée');
  }

  /**
   * Exporter les données pour sauvegarde
   */
  exportData(): { insights: WorkflowInsight[], finalPlan: FinalActionPlan | null } {
    return {
      insights: this.insights,
      finalPlan: this.finalPlan
    };
  }

  /**
   * Importer des données sauvegardées
   */
  importData(data: { insights: WorkflowInsight[], finalPlan: FinalActionPlan | null }): void {
    this.insights = data.insights || [];
    this.finalPlan = data.finalPlan;
    console.log(`📥 Données importées: ${this.insights.length} insights`);
  }

  private generateDetailedExecutiveSummary(
    problem: string, 
    recommendationCount: number, 
    actions: any[], 
    feasibilityScore: number
  ): string {
    const highPriorityActions = actions.filter(a => a.priority === 'HIGH').length;
    const mediumPriorityActions = actions.filter(a => a.priority === 'MEDIUM').length;
    const totalSteps = this.insights.length;
    
    return `Analyse complète du problème "${problem}" à travers ${totalSteps} étapes de workflow stratégique. 
    
Notre analyse approfondie a identifié ${recommendationCount} recommandations structurées avec une faisabilité globale de ${(feasibilityScore * 100).toFixed(1)}%. 

Plan d'action prioritaire : ${highPriorityActions} actions critiques à implémenter immédiatement, ${mediumPriorityActions} actions de développement à moyen terme. 

Cette analyse s'appuie sur un processus méthodique d'évaluation multi-dimensionnelle incluant l'analyse de faisabilité, l'impact stratégique, et la priorisation basée sur les ressources disponibles.`;
  }

  private generateKeyFindings(): string[] {
    const significantInsights = this.insights
      .filter(insight => insight.aiAnalysis && insight.aiAnalysis.length > 50)
      .slice(0, 5);

    return significantInsights.map((insight, index) => {
      const analysisPreview = insight.aiAnalysis.substring(0, 200);
      const hasActionableContent = insight.aiAnalysis.includes('recommand') || 
                                  insight.aiAnalysis.includes('suggér') || 
                                  insight.aiAnalysis.includes('stratégie') ||
                                  insight.aiAnalysis.includes('solution');
      
      const findingType = hasActionableContent ? "Recommandation stratégique" : "Analyse exploratoire";
      
      return `${findingType} - Étape ${insight.stepIndex} (${insight.stepTitle}): ${analysisPreview}${analysisPreview.length >= 200 ? '...' : ''}`;
    });
  }

  private generateContextualNextSteps(actions: any[]): string[] {
    const nextSteps: string[] = [];
    
    // Étapes immédiates (actions HIGH priority)
    const immediateActions = actions.filter(a => a.priority === 'HIGH').slice(0, 3);
    if (immediateActions.length > 0) {
      nextSteps.push(`Phase 1 - Actions immédiates (0-30 jours) : ${immediateActions.map(a => a.action).join(', ')}`);
    }
    
    // Étapes à moyen terme (actions MEDIUM priority)
    const mediumTermActions = actions.filter(a => a.priority === 'MEDIUM').slice(0, 3);
    if (mediumTermActions.length > 0) {
      nextSteps.push(`Phase 2 - Développement à moyen terme (1-3 mois) : ${mediumTermActions.map(a => a.action).join(', ')}`);
    }
    
    // Étapes de suivi et évaluation
    nextSteps.push("Phase 3 - Suivi et évaluation : Mise en place d'indicateurs de performance et de mécanismes de retour d'information pour mesurer l'efficacité des actions mises en œuvre");
    
    // Étapes d'optimisation continue
    nextSteps.push("Phase 4 - Optimisation continue : Analyse des résultats obtenus et ajustement des stratégies en fonction des performances observées et des évolutions du contexte");
    
    return nextSteps;
  }
}

// Instance singleton
export const workflowMemoryService = new WorkflowMemoryService();
