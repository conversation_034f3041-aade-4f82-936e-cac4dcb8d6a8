/**
 * Service de Génération de Rapport Amélioré - Rooney v4.3
 * Implémentation des Instructions de Gemini pour une Synthèse Stratégique à Valeur Ajoutée
 * 
 * PRINCIPE FONDAMENTAL : Transformer d'un simple journal chronologique 
 * en une véritable synthèse stratégique à valeur ajoutée
 */

import type { Message } from '../../types';
import { workflowMemoryService, type WorkflowInsight } from '../../services/workflowMemoryService';

// Types pour le nouveau système de rapport stratégique
export interface ThematicCluster {
  id: string;
  theme: string;
  description: string;
  initialProblem: string;
  finalValidatedSolution: string;
  keyElements: string[];
  confidence: number;
}

export interface StateTrackedData {
  key: string;
  currentValue: string;
  previousValues: Array<{
    value: string;
    timestamp: Date;
    status: 'OBSOLETE' | 'CORRIGEE' | 'VALIDEE';
  }>;
  validatedBy: 'USER' | 'SYSTEM';
  finalStatus: 'VALIDATED' | 'PENDING';
}

export interface StrategicSynthesisReport {
  metadata: {
    title: string;
    date: string;
    sessionSummary: string;
    totalThemes: number;
  };
  thematicAnalysis: ThematicCluster[];
  validatedSolutions: Array<{
    theme: string;
    problem: string;
    solution: string;
    configuration?: any;
    recommendations: string[];
  }>;
  nextSteps: string[];
  conclusion: string;
}

class EnhancedReportGenerationService {
  private stateTracker: Map<string, StateTrackedData> = new Map();
  private thematicClusters: ThematicCluster[] = [];

  /**
   * PRINCIPE N°1 : STATE TRACKING - Primauté de la Solution Validée
   * Implémente un système de tracking pour chaque donnée clé
   */
  trackDataState(key: string, value: string, validatedBy: 'USER' | 'SYSTEM' = 'SYSTEM'): void {
    const existing = this.stateTracker.get(key);
    
    if (existing) {
      // Marquer la valeur précédente comme OBSOLETE ou CORRIGEE
      existing.previousValues.push({
        value: existing.currentValue,
        timestamp: new Date(),
        status: 'CORRIGEE'
      });
      existing.currentValue = value;
      existing.validatedBy = validatedBy;
    } else {
      this.stateTracker.set(key, {
        key,
        currentValue: value,
        previousValues: [],
        validatedBy,
        finalStatus: validatedBy === 'USER' ? 'VALIDATED' : 'PENDING'
      });
    }
  }

  /**
   * Valide explicitement une donnée par l'utilisateur
   */
  validateDataByUser(key: string): void {
    const data = this.stateTracker.get(key);
    if (data) {
      data.validatedBy = 'USER';
      data.finalStatus = 'VALIDATED';
    }
  }

  /**
   * PRINCIPE N°2 : ANALYSE THÉMATIQUE sur CHRONOLOGIE BRUTE
   * Identifie les "clusters" thématiques distincts dans la conversation
   */
  identifyThematicClusters(conversation: Message[]): ThematicCluster[] {
    if (conversation.length < 2) {
      return [];
    }

    // Analyser tous les messages pour identifier des patterns thématiques
    const messageTexts = conversation.map(m => m.text).join(' ').toLowerCase();
    
    // Mots-clés thématiques étendus pour différents domaines
    const thematicPatterns = [
      {
        theme: "Analyse et Réflexion Stratégique",
        keywords: ["analyse", "réflexion", "stratégie", "approche", "méthode", "plan", "objectif", "but", "mission", "vision"],
        description: "Processus de réflexion et d'analyse stratégique"
      },
      {
        theme: "Problématique Identifiée",
        keywords: ["problème", "défi", "difficulté", "obstacle", "enjeu", "question", "situation", "contexte", "besoin"],
        description: "Identification et compréhension du problème à résoudre"
      },
      {
        theme: "Solutions et Recommandations",
        keywords: ["solution", "recommandation", "conseil", "suggestion", "proposition", "action", "mesure", "étape", "procédure"],
        description: "Solutions proposées et recommandations d'actions"
      },
      {
        theme: "Dimensionnement Technique",
        keywords: ["technique", "système", "dimensionnement", "batterie", "panneau", "solaire", "watts", "autonomie", "calcul", "configuration", "capacité"],
        description: "Aspects techniques et d'ingénierie"
      },
      {
        theme: "Blocage Créatif et Développement Personnel",
        keywords: ["créatif", "écriture", "blocage", "motivation", "peur", "échec", "identité", "roman", "pause", "vide", "personnel", "émotionnel"],
        description: "Défis liés à la créativité et au développement personnel"
      },
      {
        theme: "Démarches Administratives",
        keywords: ["administratif", "dossier", "document", "préfecture", "démarche", "formulaire", "juridique", "procédure", "officiel"],
        description: "Questions administratives et réglementaires"
      },
      {
        theme: "Validation et Mise en Œuvre",
        keywords: ["validation", "mise en œuvre", "implémentation", "réalisation", "exécution", "suivi", "contrôle", "vérification"],
        description: "Phase de validation et d'implémentation des solutions"
      }
    ];

    const clusters: ThematicCluster[] = [];
    
    thematicPatterns.forEach((pattern, index) => {
      const matchScore = pattern.keywords.reduce((score, keyword) => {
        const regex = new RegExp(keyword, 'gi');
        const matches = messageTexts.match(regex);
        return score + (matches ? matches.length : 0);
      }, 0);

      if (matchScore > 1) { // Seuil de pertinence abaissé pour capturer plus de thèmes
        // Extraire le problème initial et la solution finale pour ce thème
        const { initialProblem, finalSolution, keyDiscussionPoints } = this.extractDetailedThemeAnalysis(conversation, pattern.keywords, pattern.theme);
        
        if (initialProblem.length > 20 || finalSolution.length > 20) { // Filtrer les thèmes avec du contenu substantiel
          clusters.push({
            id: `theme_${index}`,
            theme: pattern.theme,
            description: pattern.description,
            initialProblem,
            finalValidatedSolution: finalSolution,
            keyElements: keyDiscussionPoints,
            confidence: Math.min(matchScore / 15, 1) // Normaliser entre 0 et 1
          });
        }
      }
    });

    // Si aucun thème spécifique n'est détecté, créer un thème générique basé sur le contenu
    if (clusters.length === 0) {
      const generalTheme = this.createGeneralThemeFromConversation(conversation);
      if (generalTheme) {
        clusters.push(generalTheme);
      }
    }

    this.thematicClusters = clusters.sort((a, b) => b.confidence - a.confidence);
    return this.thematicClusters;
  }

  /**
   * Créer un thème général quand aucun thème spécifique n'est détecté
   */
  private createGeneralThemeFromConversation(conversation: Message[]): ThematicCluster | null {
    if (conversation.length < 2) return null;

    const userMessages = conversation.filter(msg => msg.sender === 'user');
    const aiMessages = conversation.filter(msg => msg.sender === 'ai');

    const firstUserMessage = userMessages[0]?.text || '';
    const lastAiMessage = aiMessages[aiMessages.length - 1]?.text || '';

    // Extraire les points clés de discussion
    const keyPoints = this.extractKeyDiscussionPoints(conversation);

    return {
      id: 'theme_general',
      theme: 'Analyse Générale et Conseil',
      description: 'Discussion générale avec analyse et recommandations',
      initialProblem: firstUserMessage.length > 200 ? firstUserMessage.substring(0, 200) + '...' : firstUserMessage,
      finalValidatedSolution: lastAiMessage.length > 300 ? lastAiMessage.substring(0, 300) + '...' : lastAiMessage,
      keyElements: keyPoints,
      confidence: 0.5
    };
  }

  /**
   * Extrait une analyse détaillée pour un thème donné
   */
  private extractDetailedThemeAnalysis(conversation: Message[], keywords: string[], theme: string): 
    { initialProblem: string; finalSolution: string; keyDiscussionPoints: string[] } {
    
    const relevantMessages = conversation.filter(msg => 
      keywords.some(keyword => 
        msg.text.toLowerCase().includes(keyword.toLowerCase())
      )
    );

    if (relevantMessages.length === 0) {
      // Fallback : utiliser toute la conversation
      return this.extractGeneralAnalysis(conversation);
    }

    // Problème initial : premier message utilisateur pertinent OU premier message général
    const userMessages = relevantMessages.filter(msg => msg.sender === 'user');
    const aiMessages = relevantMessages.filter(msg => msg.sender === 'ai');
    
    const initialProblem = userMessages.length > 0 
      ? userMessages[0].text.substring(0, 400)
      : conversation.find(msg => msg.sender === 'user')?.text.substring(0, 400) || "Besoin d'analyse non spécifié clairement";

    // Solution finale : dernier message IA pertinent avec validation
    const finalSolution = aiMessages.length > 0
      ? aiMessages[aiMessages.length - 1].text.substring(0, 500)
      : conversation.filter(msg => msg.sender === 'ai').pop()?.text.substring(0, 500) || "Solution en cours d'élaboration";

    // Points clés de discussion
    const keyDiscussionPoints = this.extractKeyDiscussionPoints(relevantMessages.length > 0 ? relevantMessages : conversation);

    return { initialProblem, finalSolution, keyDiscussionPoints };
  }

  /**
   * Extrait une analyse générale quand aucun thème spécifique n'est trouvé
   */
  private extractGeneralAnalysis(conversation: Message[]): 
    { initialProblem: string; finalSolution: string; keyDiscussionPoints: string[] } {
    
    const userMessages = conversation.filter(msg => msg.sender === 'user');
    const aiMessages = conversation.filter(msg => msg.sender === 'ai');

    const initialProblem = userMessages.length > 0 
      ? userMessages[0].text.substring(0, 400)
      : "Problème non défini";

    const finalSolution = aiMessages.length > 0
      ? aiMessages[aiMessages.length - 1].text.substring(0, 500)
      : "Analyse en cours";

    const keyDiscussionPoints = this.extractKeyDiscussionPoints(conversation);

    return { initialProblem, finalSolution, keyDiscussionPoints };
  }

  /**
   * Extrait le problème initial et la solution finale pour un thème donné
   */
  private extractProblemSolutionForTheme(conversation: Message[], keywords: string[]): 
    { initialProblem: string; finalSolution: string } {
    
    const relevantMessages = conversation.filter(msg => 
      keywords.some(keyword => 
        msg.text.toLowerCase().includes(keyword.toLowerCase())
      )
    );

    // Problème initial : premier message utilisateur pertinent
    const initialProblem = relevantMessages
      .find(msg => msg.sender === 'user')?.text.substring(0, 200) || 
      "Problème non identifié clairement";

    // Solution finale : dernier message IA pertinent avec des données validées
    const finalSolution = relevantMessages
      .reverse()
      .find(msg => msg.sender === 'ai' && 
        (msg.text.includes('recommand') || msg.text.includes('solution') || msg.text.includes('configuration')))
      ?.text.substring(0, 300) || 
      "Solution en cours d'élaboration";

    return { initialProblem, finalSolution };
  }

  /**
   * Extrait les éléments clés validés pour un thème
   */
  private extractKeyElementsForTheme(conversation: Message[], keywords: string[]): string[] {
    const relevantMessages = conversation.filter(msg => 
      keywords.some(keyword => 
        msg.text.toLowerCase().includes(keyword.toLowerCase())
      ) && msg.sender === 'ai'
    );

    const keyElements: string[] = [];
    
    relevantMessages.forEach(msg => {
      // Rechercher des patterns de données validées
      const patterns = [
        /(\d+[\s]*[kKWwHh]+)/g, // Valeurs techniques
        /([A-Z][a-z]+\s*:\s*[^.!?]+)/g, // Configurations
        /Recommandation[^.!?]+/gi, // Recommandations
      ];

      patterns.forEach(pattern => {
        const matches = msg.text.match(pattern);
        if (matches) {
          keyElements.push(...matches.slice(0, 3)); // Limiter à 3 éléments par pattern
        }
      });
    });

    return [...new Set(keyElements)].slice(0, 5); // Dédupliqué et limité à 5
  }

  /**
   * Extrait les points clés de discussion d'une conversation
   */
  private extractKeyDiscussionPoints(conversation: Message[]): string[] {
    const keyPoints: string[] = [];
    
    conversation.forEach(msg => {
      if (msg.sender === 'ai') {
        // Extraire les points importants des réponses AI
        const patterns = [
          /(?:Il faut|Vous devez|Recommandation|Important|Crucial|Essentiel)[^.!?]*[.!?]/gi,
          /(?:Première étape|Deuxième étape|Ensuite|Puis|Enfin)[^.!?]*[.!?]/gi,
          /(?:Solution|Stratégie|Approche|Méthode)[^.!?]*[.!?]/gi,
          /(?:\d+[\.,]\s*[^.!?]*[.!?])/g, // Points numérotés
        ];

        patterns.forEach(pattern => {
          const matches = msg.text.match(pattern);
          if (matches) {
            keyPoints.push(...matches.slice(0, 2));
          }
        });
      } else if (msg.sender === 'user') {
        // Extraire les questions et préoccupations clés de l'utilisateur
        if (msg.text.includes('?') || msg.text.length > 50) {
          const summary = msg.text.length > 100 
            ? msg.text.substring(0, 100) + '...'
            : msg.text;
          keyPoints.push(`Question utilisateur: ${summary}`);
        }
      }
    });

    return [...new Set(keyPoints)].slice(0, 8); // Dédupliqué et limité à 8 points
  }

  /**
   * PRINCIPE N°3 : PERTINENCE sur EXHAUSTIVITÉ
   * Génère un rapport dynamique, supprime les sections vides
   */
  generateStrategicSynthesisReport(
    conversation: Message[],
    initialProblem: string,
    finalPrompt: string,
    workflowInsights: WorkflowInsight[]
  ): StrategicSynthesisReport {
    
    // Phase 1 : Analyse Thématique
    const thematicClusters = this.identifyThematicClusters(conversation);
    
    if (thematicClusters.length === 0) {
      throw new Error("Aucun arc thématique identifié - conversation insuffisante pour génération de rapport");
    }

    // Phase 2 : Extraction Problème/Solution par Thème avec State Tracking
    const validatedSolutions = thematicClusters.map(cluster => {
      // Utiliser seulement les données avec le statut VALIDATED
      const validatedData = Array.from(this.stateTracker.values())
        .filter(data => data.finalStatus === 'VALIDATED');

      return {
        theme: cluster.theme,
        problem: cluster.initialProblem,
        solution: cluster.finalValidatedSolution,
        configuration: this.extractValidatedConfiguration(cluster.theme, validatedData),
        recommendations: this.extractValidatedRecommendations(cluster.theme, conversation)
      };
    });

    // Phase 3 : Rédaction Synthétique
    const sessionSummary = this.generateSessionSummary(thematicClusters, conversation.length);
    
    // Phase 4 : Auto-Validation
    const reportChecks = this.performAutoValidation(thematicClusters, validatedSolutions);
    if (!reportChecks.isValid) {
      console.warn('Rapport auto-validation failed:', reportChecks.issues);
    }

    const report: StrategicSynthesisReport = {
      metadata: {
        title: "RAPPORT STRATÉGIQUE DE SYNTHÈSE - ROONY",
        date: new Date().toLocaleDateString('fr-FR', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }),
        sessionSummary,
        totalThemes: thematicClusters.length
      },
      thematicAnalysis: thematicClusters,
      validatedSolutions: validatedSolutions.filter(sol => sol.solution !== "Solution en cours d'élaboration"),
      nextSteps: this.generateNextSteps(thematicClusters, workflowInsights),
      conclusion: this.generateConclusion(thematicClusters, validatedSolutions)
    };

    return report;
  }

  /**
   * Génère un résumé de session intelligent
   */
  private generateSessionSummary(clusters: ThematicCluster[], messageCount: number): string {
    if (clusters.length === 1) {
      return `Cette session a traité un défi principal autour de ${clusters[0].theme.toLowerCase()}.`;
    } else {
      const themes = clusters.map(c => c.theme.toLowerCase()).join(', ');
      return `Cette session a couvert ${clusters.length} défis distincts : ${themes}. L'analyse a progressé d'une approche globale vers des solutions spécifiques et concrètes.`;
    }
  }

  /**
   * Extrait la configuration validée pour un thème
   */
  private extractValidatedConfiguration(theme: string, validatedData: StateTrackedData[]): any {
    const config: any = {};
    
    validatedData.forEach(data => {
      if (data.currentValue.match(/\d+[\s]*[kKWwHh]+/)) {
        // Données techniques
        if (theme.includes("Technique")) {
          const key = data.key.toLowerCase();
          config[key] = data.currentValue;
        }
      }
    });

    return Object.keys(config).length > 0 ? config : undefined;
  }

  /**
   * Extrait les recommandations validées pour un thème
   */
  private extractValidatedRecommendations(theme: string, conversation: Message[]): string[] {
    const aiMessages = conversation.filter(msg => msg.sender === 'ai');
    const recommendations: string[] = [];

    aiMessages.forEach(msg => {
      if (msg.text.toLowerCase().includes(theme.toLowerCase().split(' ')[0])) {
        const patterns = [
          /recommand[e|ation|é][^.!?]+/gi,
          /il faut[^.!?]+/gi,
          /stratégie[^.!?]+/gi
        ];

        patterns.forEach(pattern => {
          const matches = msg.text.match(pattern);
          if (matches) {
            recommendations.push(...matches.slice(0, 2));
          }
        });
      }
    });

    return [...new Set(recommendations)].slice(0, 3);
  }

  /**
   * Auto-validation selon les principes de Gemini
   */
  private performAutoValidation(clusters: ThematicCluster[], solutions: any[]): 
    { isValid: boolean; issues: string[] } {
    
    const issues: string[] = [];

    // Check 1 : Tous les arcs thématiques inclus ?
    if (clusters.length === 0) {
      issues.push("Aucun arc thématique identifié");
    }

    // Check 2 : Informations corrigées exclues ?
    const obsoleteData = Array.from(this.stateTracker.values())
      .filter(data => data.previousValues.some(prev => prev.status === 'OBSOLETE'));
    
    if (obsoleteData.length > 0) {
      issues.push(`${obsoleteData.length} données obsolètes détectées mais exclues`);
    }

    // Check 3 : Sections vides ou génériques ?
    const emptySolutions = solutions.filter(sol => 
      sol.solution === "Solution en cours d'élaboration" || 
      sol.recommendations.length === 0
    );

    if (emptySolutions.length > 0) {
      issues.push(`${emptySolutions.length} sections avec contenu générique détectées`);
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * Génère les prochaines étapes basées sur l'analyse
   */
  private generateNextSteps(clusters: ThematicCluster[], insights: WorkflowInsight[]): string[] {
    const steps: string[] = [];

    clusters.forEach(cluster => {
      if (cluster.confidence > 0.7) {
        steps.push(`Mise en œuvre de la solution validée pour ${cluster.theme}`);
      } else {
        steps.push(`Approfondissement de l'analyse pour ${cluster.theme}`);
      }
    });

    // Ajouter des étapes génériques si pertinentes
    if (insights.length > 5) {
      steps.push("Consolidation des apprentissages en base de connaissances");
    }

    return steps.slice(0, 4); // Maximum 4 étapes
  }

  /**
   * Génère une conclusion synthétique
   */
  private generateConclusion(clusters: ThematicCluster[], solutions: any[]): string {
    const validatedSolutionsCount = solutions.filter(sol => 
      sol.solution !== "Solution en cours d'élaboration"
    ).length;

    const highConfidenceClusters = clusters.filter(c => c.confidence > 0.7).length;

    return `Le parcours a démontré l'importance de la clarification progressive et de la correction itérative pour aboutir à ${validatedSolutionsCount} solution(s) robuste(s). ${highConfidenceClusters} thème(s) à forte confiance sont prêts pour la phase d'implémentation.`;
  }

  /**
   * Convertit le rapport en format Markdown selon le template de Gemini
   * VERSION COMPLÈTE ET DÉTAILLÉE
   */
  formatReportAsMarkdown(report: StrategicSynthesisReport): string {
    let markdown = `# ${report.metadata.title}

**Date :** ${report.metadata.date}
**Concerne :** Synthèse complète de la session d'analyse stratégique
**Durée d'analyse :** ${report.thematicAnalysis.length} axes thématiques identifiés

---

## 📋 1. Résumé Exécutif du Parcours Analytique

${report.metadata.sessionSummary}

### Métriques de la Session
- **Nombre total d'échanges analysés :** ${report.metadata.totalThemes > 0 ? 'Session complète' : 'Session partielle'}
- **Thèmes principaux identifiés :** ${report.thematicAnalysis.length}
- **Solutions validées :** ${report.validatedSolutions.length}
- **Niveau de confiance moyen :** ${this.calculateAverageConfidence(report.thematicAnalysis)}%

---

`;

    // === SECTION DÉTAILLÉE DES AXES THÉMATIQUES ===
    if (report.thematicAnalysis.length > 0) {
      report.thematicAnalysis.forEach((cluster, index) => {
        const correspondingSolution = report.validatedSolutions.find(sol => 
          sol.theme === cluster.theme
        );

        markdown += `## ${index + 2}. Axe Thématique ${index + 1} : ${cluster.theme}

*${cluster.description}*
**Niveau de confiance :** ${Math.round(cluster.confidence * 100)}%

### 🎯 Problématique Initiale Identifiée
${cluster.initialProblem}

### 💡 Solution Stratégique Développée
${correspondingSolution?.solution || cluster.finalValidatedSolution}

`;

        // Configuration technique si disponible
        if (correspondingSolution?.configuration && Object.keys(correspondingSolution.configuration).length > 0) {
          markdown += `### ⚙️ Configuration Technique Validée
`;
          Object.entries(correspondingSolution.configuration).forEach(([key, value]) => {
            markdown += `- **${key}** : ${value}\n`;
          });
          markdown += '\n';
        }

        // Points clés de discussion
        if (cluster.keyElements && cluster.keyElements.length > 0) {
          markdown += `### 🔍 Points Clés de la Discussion
${cluster.keyElements.map(element => `- ${element}`).join('\n')}

`;
        }

        // Recommandations spécifiques
        if (correspondingSolution?.recommendations && correspondingSolution.recommendations.length > 0) {
          markdown += `### 📌 Recommandations Spécifiques
${correspondingSolution.recommendations.map(rec => `- ${rec}`).join('\n')}

`;
        }

        markdown += '---\n\n';
      });
    }

    // === PLAN D'ACTION COMPLET ===
    markdown += `## ${report.thematicAnalysis.length + 2}. Plan d'Action Stratégique Détaillé

### 🚀 Actions Immédiates Prioritaires
${report.nextSteps.map((step, index) => `${index + 1}. **${step}**`).join('\n')}

### 📊 Synthèse des Solutions Validées
${report.validatedSolutions.length > 0 ? 
  report.validatedSolutions.map((solution, index) => 
    `#### Solution ${index + 1} : ${solution.theme}
- **Problème traité :** ${solution.problem.substring(0, 150)}${solution.problem.length > 150 ? '...' : ''}
- **Solution retenue :** ${solution.solution.substring(0, 200)}${solution.solution.length > 200 ? '...' : ''}
${solution.recommendations.length > 0 ? `- **Actions recommandées :** ${solution.recommendations.slice(0, 3).join(', ')}` : ''}`
  ).join('\n\n') : 
  'Aucune solution complètement validée - Analyse en cours'
}

---

## ${report.thematicAnalysis.length + 3}. Analyse Détaillée de la Progression

### 🔄 Évolution de la Réflexion
La session a progressé à travers plusieurs phases d'analyse :

${this.generateProgressionAnalysis(report)}

### 📈 Évaluation de la Qualité des Solutions
${this.generateSolutionQualityAnalysis(report)}

---

## ${report.thematicAnalysis.length + 4}. Conclusion et Perspectives

${report.conclusion}

### 🎯 Prochaines Étapes Recommandées (Par Ordre de Priorité)
${report.nextSteps.map((step, index) => `${index + 1}. **${step}**
   - *Objectif :* Progression structurée vers la résolution
   - *Délai recommandé :* ${this.getRecommendedTimeframe(index)}`).join('\n')}

### 🔮 Recommandations pour la Suite
- **Suivi immédiat :** Validation et mise en œuvre des solutions prioritaires
- **Monitoring :** Évaluation régulière des résultats obtenus
- **Ajustements :** Adaptation des stratégies selon les retours d'expérience
- **Documentation :** Capitalisation des apprentissages pour futures situations similaires

---

## 📊 Annexes et Métriques

### État du Système de Suivi (State Tracking)
- **Données suivies :** ${this.stateTracker.size} éléments
- **Validations utilisateur :** ${Array.from(this.stateTracker.values()).filter(d => d.finalStatus === 'VALIDATED').length}
- **Corrections appliquées :** ${Array.from(this.stateTracker.values()).filter(d => d.previousValues.length > 0).length}

### Confiance dans les Recommandations
${report.thematicAnalysis.map(cluster => 
  `- **${cluster.theme} :** ${Math.round(cluster.confidence * 100)}% de confiance`
).join('\n')}

---

*Rapport généré automatiquement par Roony v4.3 - Studio Agentique*
*Système d'Analyse Thématique et State Tracking activé*
*Généré le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}*

---

**🔗 Pour aller plus loin :**
- Utilisez ce rapport comme base de travail pour vos prochaines actions
- Consultez les sections spécifiques selon vos priorités immédiates  
- N'hésitez pas à revenir vers Roony pour des précisions ou approfondissements
`;

    return markdown;
  }

  /**
   * Calcule la confiance moyenne des analyses
   */
  private calculateAverageConfidence(clusters: ThematicCluster[]): number {
    if (clusters.length === 0) return 0;
    const total = clusters.reduce((sum, cluster) => sum + cluster.confidence, 0);
    return Math.round((total / clusters.length) * 100);
  }

  /**
   * Génère une analyse de progression
   */
  private generateProgressionAnalysis(report: StrategicSynthesisReport): string {
    const phases = [
      "1. **Phase d'exploration :** Identification et compréhension des enjeux",
      "2. **Phase d'analyse :** Approfondissement et structuration de la réflexion", 
      "3. **Phase de synthèse :** Développement des solutions et recommandations",
      "4. **Phase de validation :** Consolidation et validation des approches retenues"
    ];
    
    return phases.join('\n');
  }

  /**
   * Génère une analyse de la qualité des solutions
   */
  private generateSolutionQualityAnalysis(report: StrategicSynthesisReport): string {
    const validatedCount = report.validatedSolutions.length;
    const totalThemes = report.thematicAnalysis.length;
    
    if (validatedCount === 0) {
      return "⚠️ **Analyse en cours** - Les solutions sont encore en phase d'exploration et nécessitent des validations supplémentaires.";
    }
    
    const completionRate = Math.round((validatedCount / totalThemes) * 100);
    
    if (completionRate >= 80) {
      return `✅ **Excellent niveau de résolution** (${completionRate}%) - La majorité des thématiques ont des solutions validées et opérationnelles.`;
    } else if (completionRate >= 50) {
      return `🟡 **Bon niveau de progression** (${completionRate}%) - Plusieurs solutions sont identifiées, certaines nécessitent encore un approfondissement.`;
    } else {
      return `🔄 **Phase d'exploration active** (${completionRate}%) - L'analyse est en cours avec des pistes prometteuses à développer.`;
    }
  }

  /**
   * Recommande un délai pour chaque étape
   */
  private getRecommendedTimeframe(stepIndex: number): string {
    const timeframes = [
      "Immédiat (0-48h)",
      "Court terme (1 semaine)", 
      "Moyen terme (2-4 semaines)",
      "Long terme (1-3 mois)"
    ];
    return timeframes[Math.min(stepIndex, timeframes.length - 1)];
  }

  /**
   * Réinitialise le service pour une nouvelle session
   */
  reset(): void {
    this.stateTracker.clear();
    this.thematicClusters = [];
  }

  /**
   * Obtient les statistiques du state tracking
   */
  getStateTrackingStats(): {
    totalTracked: number;
    validated: number;
    pending: number;
    corrected: number;
  } {
    const data = Array.from(this.stateTracker.values());
    return {
      totalTracked: data.length,
      validated: data.filter(d => d.finalStatus === 'VALIDATED').length,
      pending: data.filter(d => d.finalStatus === 'PENDING').length,
      corrected: data.filter(d => d.previousValues.length > 0).length
    };
  }
}

// Instance singleton
export const enhancedReportGenerationService = new EnhancedReportGenerationService();
