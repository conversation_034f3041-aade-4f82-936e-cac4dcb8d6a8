/**
 * Script pour générer automatiquement les constantes mises à jour
 * Août 2025 - Détection automatique des nouveaux modèles OpenRouter
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Simulation de l'appel API (en production, ceci utiliserait le vrai service)
async function fetchModelsFromAPI() {
    try {
        // Utiliser node-fetch ou une alternative pour Node.js
        const { default: fetch } = await import('node-fetch');
        
        console.log('🔍 Récupération des modèles depuis OpenRouter API...');
        const response = await fetch('https://openrouter.ai/api/v1/models');
        if (!response.ok) {
            throw new Error(`Erreur API: ${response.status}`);
        }
        const data = await response.json();
        return data.data || [];
    } catch (error) {
        console.error('❌ Erreur récupération API:', error.message);
        
        // Fallback avec des données statiques pour tester le système
        console.log('⚠️ Utilisation des données de fallback pour les tests...');
        return generateFallbackData();
    }
}

// Données de fallback pour tester le système
function generateFallbackData() {
    return [
        // Exemples de modèles gratuits
        {
            id: "openai/gpt-oss-20b:free",
            name: "OpenAI GPT OSS 20B (Free)",
            pricing: { prompt: "0", completion: "0" },
            context_length: 8192,
            created: 1735689600,
            description: "Open source GPT model for free usage",
            architecture: { input_modalities: "text", output_modalities: "text" }
        },
        {
            id: "deepseek/deepseek-r1:free",
            name: "DeepSeek R1 (Free)",
            pricing: { prompt: "0", completion: "0" },
            context_length: 32768,
            created: 1735689600,
            description: "Advanced reasoning model with chain-of-thought capabilities",
            architecture: { input_modalities: "text", output_modalities: "text" }
        },
        {
            id: "qwen/qwen3-coder:free",
            name: "Qwen 3 Coder (Free)",
            pricing: { prompt: "0", completion: "0" },
            context_length: 16384,
            created: 1735689600,
            description: "Specialized coding model for programming tasks",
            architecture: { input_modalities: "text", output_modalities: "text" }
        },
        // Exemples de modèles premium
        {
            id: "openai/gpt-4o-mini",
            name: "OpenAI GPT-4O Mini",
            pricing: { prompt: "0.00000015", completion: "0.0000006" },
            context_length: 128000,
            created: 1735689600,
            description: "Efficient version of GPT-4O with reduced cost",
            architecture: { input_modalities: "text", output_modalities: "text" }
        },
        {
            id: "anthropic/claude-3.5-haiku",
            name: "Anthropic Claude 3.5 Haiku",
            pricing: { prompt: "0.00000025", completion: "0.00000125" },
            context_length: 200000,
            created: 1735689600,
            description: "Fast and efficient Claude model for quick responses",
            architecture: { input_modalities: "text", output_modalities: "text" }
        }
    ];
}

// Classification des modèles
function classifyModel(model) {
    const promptPrice = parseFloat(model.pricing.prompt || '0');
    const completionPrice = parseFloat(model.pricing.completion || '0');
    
    const isFree = model.id.includes(':free') || (promptPrice === 0 && completionPrice === 0);
    const isPremium = !isFree && promptPrice >= 0.0000001 && promptPrice <= 0.000001; // 0.1$ à 1$ par million
    
    const modelIdLower = model.id.toLowerCase();
    const descriptionLower = model.description.toLowerCase();
    
    const capabilities = {
        reasoning: modelIdLower.includes('deepseek-r1') || 
                  modelIdLower.includes('qwq') || 
                  modelIdLower.includes('thinking') ||
                  descriptionLower.includes('reasoning'),
        
        coding: modelIdLower.includes('coder') || 
               modelIdLower.includes('code') || 
               modelIdLower.includes('devstral'),
        
        multimodal: modelIdLower.includes('vision') || 
                   modelIdLower.includes('vl') ||
                   model.architecture?.input_modalities?.includes('image'),
        
        large: modelIdLower.includes('405b') || 
              modelIdLower.includes('235b') || 
              modelIdLower.includes('72b') || 
              modelIdLower.includes('70b')
    };
    
    // Recommandation de tâches
    const recommendedTasks = [];
    if (capabilities.reasoning) recommendedTasks.push('analyse', 'validation');
    if (capabilities.coding) recommendedTasks.push('génération');
    if (capabilities.large || capabilities.reasoning) recommendedTasks.push('synthèse');
    if (recommendedTasks.length === 0) recommendedTasks.push('analyse');
    
    return {
        isFree,
        isPremium,
        recommendedTasks,
        capabilities,
        pricePerMillion: {
            prompt: promptPrice * 1000000,
            completion: completionPrice * 1000000
        }
    };
}

// Génération du fichier constants.ts mis à jour
async function generateUpdatedConstants() {
    console.log('🔄 Génération des constantes mises à jour...');
    
    try {
        // Récupérer les modèles depuis l'API
        const allModels = await fetchModelsFromAPI();
        console.log(`📊 ${allModels.length} modèles récupérés depuis l'API`);
        
        // Classification
        const freeModelsByTask = {
            analyse: [],
            génération: [],
            validation: [],
            synthèse: []
        };
        
        const premiumModelsByTask = {
            analyse: [],
            génération: [],
            validation: [],
            synthèse: []
        };
        
        let freeCount = 0;
        let premiumCount = 0;
        
        allModels.forEach(model => {
            const classification = classifyModel(model);
            
            if (classification.isFree) {
                freeCount++;
                classification.recommendedTasks.forEach(task => {
                    if (!freeModelsByTask[task].includes(model.id)) {
                        freeModelsByTask[task].push(model.id);
                    }
                });
            }
            
            if (classification.isPremium) {
                premiumCount++;
                classification.recommendedTasks.forEach(task => {
                    if (!premiumModelsByTask[task].includes(model.id)) {
                        premiumModelsByTask[task].push(model.id);
                    }
                });
            }
        });
        
        console.log(`✅ Classification terminée: ${freeCount} gratuits, ${premiumCount} premium`);
        
        // Génération du contenu du fichier
        const timestamp = new Date().toISOString();
        const constantsContent = `import type { Step } from './types';

export const WORKFLOW_STEPS: Step[] = [
  // ... (étapes du workflow inchangées)
  {
    id: 1,
    title: "Définition du Problème",
    description: "Clarifier le problème, ses contraintes et ses objectifs.",
    techniques: ["CSP", "ToT", "Role-Playing"],
    task: "analyse",
  },
  {
    id: 2,
    title: "Diagnostic Initial",
    description: "Identifier les causes profondes et les facteurs influents.",
    techniques: ["Analyse Pareto", "GoT", "5 Pourquoi"],
    task: "analyse",
  },
  {
    id: 3,
    title: "Exploration Arborescente",
    description: "Générer et évaluer plusieurs scénarios et pistes de solution.",
    techniques: ["Tree-of-Thoughts", "Monte Carlo Search", "Scaffolding"],
    task: "analyse",
  },
  {
    id: 4,
    title: "Génération de Solutions",
    description: "Proposer des solutions innovantes basées sur les scénarios.",
    techniques: ["Algorithmes Génétiques", "Adversarial Prompting", "Few-Shot"],
    task: "génération",
  },
  {
    id: 5,
    title: "Validation Éthique et Sociale",
    description: "Évaluer l'impact des solutions au regard des ODD et critères éthiques.",
    techniques: ["Ethical Prompting", "Counterfactual", "Bias Detection"],
    task: "validation",
  },
  {
    id: 6,
    title: "Simulation Prédictive",
    description: "Tester la robustesse des solutions face à des scénarios extrêmes.",
    techniques: ["Monte Carlo", "Digital Twin", "TDA"],
    task: "validation",
  },
  {
    id: 7,
    title: "Prototypage Adaptatif",
    description: "Créer un prototype minimal viable (MVP) de la solution.",
    techniques: ["ReAct", "Greedy Search", "Hill Climbing"],
    task: "génération",
  },
  {
    id: 8,
    title: "Déploiement Contextuel",
    description: "Adapter la solution aux spécificités géographiques et culturelles.",
    techniques: ["Voronoi Tessellation", "Dynamic Prompting", "KNN"],
    task: "analyse",
  },
  {
    id: 9,
    title: "Suivi Dynamique",
    description: "Mettre en place un système de monitorage en temps réel.",
    techniques: ["IoT Integration", "Process Mining", "SPC"],
    task: "analyse",
  },
  {
    id: 10,
    title: "Boucle d'Amélioration Continue",
    description: "Ajuster progressivement la solution en fonction des retours.",
    techniques: ["Kaizen", "Feedback-Driven Prompting", "PDCA"],
    task: "synthèse",
  },
  {
    id: 11,
    title: "Capitalisation Cognitive",
    description: "Archiver les apprentissages clés dans une base de connaissances.",
    techniques: ["Chain-of-Knowledge (CoK)", "Knowledge-Enriched"],
    task: "synthèse",
  },
  {
    id: 12,
    title: "Validation Transversale",
    description: "Croiser les résultats avec différentes approches pour garantir la robustesse.",
    techniques: ["Self-Consistency", "Ensemble Prompting", "Cross-Modal"],
    task: "validation",
  },
    {
    id: 13,
    title: "Communication Stratégique",
    description: "Présenter les résultats de manière claire et adaptée aux parties prenantes.",
    techniques: ["Role-Playing Prompting", "Contrastive Prompting"],
    task: "synthèse",
  },
  {
    id: 14,
    title: "Synthèse et Plan d'Action",
    description: "Compiler toutes les informations en un plan d'action.",
    techniques: ["Action Plan", "Summary"],
    task: "synthèse",
  },
  {
    id: 15,
    title: "Génération du Prompt Final",
    description: "Créer le prompt final optimisé et sa méta-analyse.",
    techniques: ["Final Prompt Generation", "Meta-Analysis"],
    task: "génération",
  }
];

// Modèles OpenRouter gratuits - Mise à jour automatique ${timestamp}
// Total: ${allModels.length} modèles (${freeCount} gratuits, ${premiumCount} premium)
export const OPENROUTER_MODELS = {
  "analyse": [
${freeModelsByTask.analyse.map(id => `    "${id}"`).join(',\n')}
  ],
  "génération": [
${freeModelsByTask.génération.map(id => `    "${id}"`).join(',\n')}
  ],
  "validation": [
${freeModelsByTask.validation.map(id => `    "${id}"`).join(',\n')}
  ],
  "synthèse": [
${freeModelsByTask.synthèse.map(id => `    "${id}"`).join(',\n')}
  ]
};

// Modèles Premium recommandés par catégorie de tâche - Mise à jour automatique ${timestamp}
export const PREMIUM_MODELS_BY_TASK = {
  "analyse": [
${premiumModelsByTask.analyse.slice(0, 10).map(id => `    "${id}"`).join(',\n')}
  ],
  "génération": [
${premiumModelsByTask.génération.slice(0, 10).map(id => `    "${id}"`).join(',\n')}
  ],
  "validation": [
${premiumModelsByTask.validation.slice(0, 10).map(id => `    "${id}"`).join(',\n')}
  ],
  "synthèse": [
${premiumModelsByTask.synthèse.slice(0, 10).map(id => `    "${id}"`).join(',\n')}
  ]
};

// URLs pour les API OpenRouter
export const OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions";
export const OPENROUTER_MODELS_API = "https://openrouter.ai/api/v1/models";

// Configuration pour la détection automatique des modèles (AMÉLIORÉE - Août 2025)
export const MODEL_DETECTION_CONFIG = {
  // Intervalle de mise à jour des modèles (en millisecondes) - 1 semaine
  UPDATE_INTERVAL: 7 * 24 * 60 * 60 * 1000,
  // Clé de stockage local pour la cache des modèles (legacy)
  CACHE_KEY: "openrouter_models_cache",
  // Clé pour la dernière mise à jour (legacy)
  LAST_UPDATE_KEY: "openrouter_models_last_update",
  // Nombre maximum de tentatives par modèle
  MAX_RETRIES: 3,
  // Délai entre les tentatives (en millisecondes)
  RETRY_DELAY: 1000,
  // Configuration pour le nouveau service de mise à jour automatique
  AUTO_UPDATE: {
    // Vérifie quotidiennement, met à jour hebdomadairement
    CHECK_INTERVAL: 24 * 60 * 60 * 1000, // 24 heures
    UPDATE_INTERVAL: 7 * 24 * 60 * 60 * 1000, // 7 jours
    // Seuil minimum de nouveaux modèles pour déclencher une notification
    NEW_MODELS_NOTIFICATION_THRESHOLD: 5,
    // Activer les notifications automatiques de nouveaux modèles
    ENABLE_AUTO_NOTIFICATIONS: true,
    // Sauvegarde automatique de la configuration mise à jour
    AUTO_SAVE_CONFIG: true
  }
};

// Configuration pour la validation de langue française avec traduction automatique
export const LANGUAGE_VALIDATION_CONFIG = {
  // Nombre maximum de tentatives si réponse non française (DÉSACTIVÉ - on traduit maintenant)
  MAX_LANGUAGE_RETRIES: 0,
  // Seuil minimum de confiance pour accepter une réponse française
  MIN_FRENCH_CONFIDENCE: 60,
  // Active la validation stricte de langue (avec traduction automatique)
  ENABLE_STRICT_VALIDATION: true,
  // Seuil d'alerte pour les modèles problématiques (% de réponses françaises)
  PROBLEMATIC_MODEL_THRESHOLD: 80,
  // Nombre minimum de tentatives avant de considérer un modèle comme problématique
  MIN_ATTEMPTS_FOR_EVALUATION: 3,
  // Active la traduction automatique pour les réponses non françaises
  ENABLE_AUTO_TRANSLATION: true
};

export const DATA_SOURCES = [
    {
      "id": "mcp_context7",
      "name": "MCP Server Context7",
      "type": "database",
      "description": "Base de données principale pour les contextes projets, les métriques de performance et les données historiques."
    },
    {
      "id": "web_search",
      "name": "Web Search API",
      "type": "api",
      "description": "API pour la recherche d'informations générales, de tendances et de données factuelles récentes sur le web."
    }
];

// Configuration pour le mode Premium OpenRouter (AMÉLIORÉE - Août 2025)
export const PREMIUM_CONFIG = {
  // URL pour récupérer les modèles premium avec filtre de prix
  MODELS_API_URL: "https://openrouter.ai/api/v1/models",
  // Filtres pour les modèles premium (prix entre 0.1$ et 1$ par million de tokens)
  PRICE_FILTER: {
    MIN_PRICE: 0.1,
    MAX_PRICE: 1.0
  },
  // Clé de stockage pour l'authentification Premium
  AUTH_STORAGE_KEY: "roony_premium_auth",
  // Clé de stockage pour les modèles Premium en cache
  MODELS_CACHE_KEY: "roony_premium_models",
  // Durée de cache des modèles (24 heures)
  MODELS_CACHE_DURATION: 24 * 60 * 60 * 1000,
  // Limite de crédits d'alerte
  LOW_CREDITS_THRESHOLD: 5.0,
  // Configuration pour la détection automatique des modèles Premium
  AUTO_UPDATE: {
    // Intervalle de vérification des nouveaux modèles Premium (quotidien)
    CHECK_INTERVAL: 24 * 60 * 60 * 1000,
    // Seuil de prix maximum acceptable (dollars par million de tokens)
    MAX_ACCEPTABLE_PRICE: 2.0,
    // Modèles premium recommandés par performance
    PERFORMANCE_TIERS: {
      HIGH: ['openai/gpt-4o-mini', 'anthropic/claude-3.5-haiku'],
      MEDIUM: ['google/gemini-flash-1.5', 'mistralai/mistral-7b-instruct'],
      BUDGET: ['meta-llama/llama-3.1-8b-instruct']
    }
  }
};
`;

        // Sauvegarder le fichier
        const constantsPath = path.join(__dirname, '..', 'constants_updated.ts');
        await fs.writeFile(constantsPath, constantsContent);
        
        console.log(`✅ Constantes mises à jour sauvegardées: ${constantsPath}`);
        
        // Générer un rapport de statistiques
        const reportContent = `# Rapport de mise à jour des modèles OpenRouter
Généré le: ${new Date().toLocaleString('fr-FR')}

## Statistiques globales
- **Total modèles**: ${allModels.length}
- **Modèles gratuits**: ${freeCount}
- **Modèles premium**: ${premiumCount}

## Répartition par tâche (gratuits)
${Object.entries(freeModelsByTask).map(([task, models]) => 
    `- **${task}**: ${models.length} modèles`
).join('\n')}

## Répartition par tâche (premium)
${Object.entries(premiumModelsByTask).map(([task, models]) => 
    `- **${task}**: ${models.length} modèles`
).join('\n')}

## Modèles gratuits remarquables
${Object.entries(freeModelsByTask).map(([task, models]) => 
    `### ${task.charAt(0).toUpperCase() + task.slice(1)}\n` +
    models.slice(0, 5).map(id => `- \`${id}\``).join('\n')
).join('\n\n')}

## Prochaines étapes
1. Examiner le fichier \`constants_updated.ts\`
2. Remplacer le fichier \`constants.ts\` existant si approprié
3. Tester les nouveaux modèles
4. Mettre à jour la documentation

---
*Généré automatiquement par le système de détection des modèles de Studio Agentique Roony*
`;

        const reportPath = path.join(__dirname, '..', 'model_update_report.md');
        await fs.writeFile(reportPath, reportContent);
        
        console.log(`📊 Rapport généré: ${reportPath}`);
        
        return {
            success: true,
            totalModels: allModels.length,
            freeModels: freeCount,
            premiumModels: premiumCount,
            constantsPath,
            reportPath
        };
        
    } catch (error) {
        console.error('❌ Erreur lors de la génération:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Exécution
const isMainModule = process.argv[1] && process.argv[1].includes('generateUpdatedConstants.js');

console.log('🚀 Démarrage du script de génération des constantes...');
console.log(`   Script: ${import.meta.url}`);
console.log(`   Args: ${process.argv[1]}`);
console.log(`   isMainModule: ${isMainModule}`);

if (isMainModule) {
    console.log('✅ Script principal détecté, début de la génération...');
    generateUpdatedConstants().then(result => {
        if (result.success) {
            console.log('\n🎉 Génération terminée avec succès !');
            console.log(`   📁 Fichier: ${result.constantsPath}`);
            console.log(`   📊 Rapport: ${result.reportPath}`);
            console.log(`   🤖 ${result.totalModels} modèles (${result.freeModels} gratuits, ${result.premiumModels} premium)`);
        } else {
            console.error(`\n❌ Échec: ${result.error}`);
            process.exit(1);
        }
    }).catch(error => {
        console.error('💥 Erreur inattendue:', error);
        process.exit(1);
    });
} else {
    console.log('ℹ️ Script importé comme module');
}

export { generateUpdatedConstants };
