/**
 * Service de gestion des fichiers contextuels pour l'Expert-Conseil
 * Permet d'ajouter du contexte via des fichiers (documents, images, etc.)
 */

export interface ContextFile {
  id: string;
  name: string;
  type: string;
  size: number;
  content: string; // Base64 ou texte extrait
  contentType: 'text' | 'image' | 'document';
  extractedText?: string; // Texte extrait pour les images (OCR) ou documents
  uploadedAt: Date;
  isAnalyzed: boolean;
}

export interface FileAnalysis {
  summary: string;
  keyPoints: string[];
  relevantContext: string;
  suggestedQuestions: string[];
}

class ContextFileService {
  private contextFiles: Map<string, ContextFile> = new Map();
  private maxFileSize = 100 * 1024 * 1024; // 100MB
  private supportedFileTypes = {
    text: ['.txt', '.md', '.json', '.csv', '.log'],
    image: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
    document: ['.pdf', '.doc', '.docx', '.rtf']
  };

  /**
   * Ajoute un fichier contextuel
   */
  async addContextFile(file: File): Promise<ContextFile> {
    // Vérifications de base
    this.validateFile(file);

    const fileId = this.generateFileId();
    const fileContent = await this.readFileContent(file);
    const contentType = this.determineContentType(file);

    const contextFile: ContextFile = {
      id: fileId,
      name: file.name,
      type: file.type,
      size: file.size,
      content: fileContent,
      contentType,
      uploadedAt: new Date(),
      isAnalyzed: false
    };

    // Extraire le texte si nécessaire
    if (contentType === 'image') {
      contextFile.extractedText = await this.extractTextFromImage(fileContent);
    } else if (contentType === 'document') {
      contextFile.extractedText = await this.extractTextFromDocument(file);
    } else if (contentType === 'text') {
      contextFile.extractedText = fileContent;
    }

    this.contextFiles.set(fileId, contextFile);
    return contextFile;
  }

  /**
   * Supprime un fichier contextuel
   */
  removeContextFile(fileId: string): boolean {
    return this.contextFiles.delete(fileId);
  }

  /**
   * Récupère tous les fichiers contextuels
   */
  getAllContextFiles(): ContextFile[] {
    return Array.from(this.contextFiles.values()).sort((a, b) => 
      b.uploadedAt.getTime() - a.uploadedAt.getTime()
    );
  }

  /**
   * Récupère un fichier contextuel par ID
   */
  getContextFile(fileId: string): ContextFile | undefined {
    return this.contextFiles.get(fileId);
  }

  /**
   * Génère un contexte global à partir de tous les fichiers
   */
  generateGlobalContext(): string {
    const files = this.getAllContextFiles();
    if (files.length === 0) {
      return '';
    }

    let context = '\n\n=== CONTEXTE ADDITIONNEL FOURNI PAR L\'UTILISATEUR ===\n\n';
    
    files.forEach((file, index) => {
      context += `📄 FICHIER ${index + 1}: ${file.name}\n`;
      context += `Type: ${file.contentType}\n`;
      context += `Taille: ${this.formatFileSize(file.size)}\n`;
      
      if (file.extractedText) {
        const truncatedText = file.extractedText.length > 2000 
          ? file.extractedText.substring(0, 2000) + '...[contenu tronqué]'
          : file.extractedText;
        
        context += `Contenu:\n${truncatedText}\n\n`;
      }
      
      context += '---\n\n';
    });

    context += '=== FIN DU CONTEXTE ADDITIONNEL ===\n\n';
    context += `INSTRUCTION: Tenez compte de ces ${files.length} fichier(s) dans votre analyse et vos recommandations. `;
    context += 'Référencez-vous aux informations pertinentes contenues dans ces documents pour enrichir vos conseils.\n\n';

    return context;
  }

  /**
   * Analyse un fichier et génère un résumé
   */
  async analyzeFile(fileId: string): Promise<FileAnalysis | null> {
    const file = this.contextFiles.get(fileId);
    if (!file || !file.extractedText) {
      return null;
    }

    // Simulation d'analyse - dans un vrai cas, on ferait appel à une IA
    const words = file.extractedText.split(/\s+/);
    const wordCount = words.length;
    
    // Extraire des mots-clés simples (mots de plus de 5 caractères, fréquents)
    const wordFreq = new Map<string, number>();
    words.forEach(word => {
      const cleanWord = word.toLowerCase().replace(/[^a-zàéèêë]/g, '');
      if (cleanWord.length > 5) {
        wordFreq.set(cleanWord, (wordFreq.get(cleanWord) || 0) + 1);
      }
    });

    const keyWords = Array.from(wordFreq.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word);

    const analysis: FileAnalysis = {
      summary: `Document de ${wordCount} mots contenant des informations sur: ${keyWords.slice(0, 3).join(', ')}`,
      keyPoints: keyWords.slice(0, 5).map(word => `Information sur: ${word}`),
      relevantContext: file.extractedText.substring(0, 500) + '...',
      suggestedQuestions: [
        'Pouvez-vous analyser ce document ?',
        'Quelles sont les implications de ces informations ?',
        'Comment utiliser ces données dans ma situation ?'
      ]
    };

    file.isAnalyzed = true;
    return analysis;
  }

  /**
   * Efface tous les fichiers contextuels
   */
  clearAllContextFiles(): void {
    this.contextFiles.clear();
  }

  /**
   * Valide un fichier avant upload
   */
  private validateFile(file: File): void {
    if (file.size > this.maxFileSize) {
      throw new Error(`Le fichier est trop volumineux. Taille maximale: ${this.formatFileSize(this.maxFileSize)}`);
    }

    const extension = this.getFileExtension(file.name);
    const allSupportedTypes = [
      ...this.supportedFileTypes.text,
      ...this.supportedFileTypes.image,
      ...this.supportedFileTypes.document
    ];

    if (!allSupportedTypes.includes(extension)) {
      throw new Error(`Type de fichier non supporté: ${extension}. Types supportés: ${allSupportedTypes.join(', ')}`);
    }
  }

  /**
   * Détermine le type de contenu d'un fichier
   */
  private determineContentType(file: File): 'text' | 'image' | 'document' {
    const extension = this.getFileExtension(file.name);
    
    if (this.supportedFileTypes.text.includes(extension)) return 'text';
    if (this.supportedFileTypes.image.includes(extension)) return 'image';
    if (this.supportedFileTypes.document.includes(extension)) return 'document';
    
    return 'text'; // Par défaut
  }

  /**
   * Lit le contenu d'un fichier
   */
  private async readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        const result = e.target?.result;
        if (typeof result === 'string') {
          // Si c'est du texte
          resolve(result);
        } else if (result instanceof ArrayBuffer) {
          // Si c'est binaire, convertir en base64
          const base64 = btoa(String.fromCharCode(...new Uint8Array(result)));
          resolve(base64);
        } else {
          reject(new Error('Format de fichier non supporté'));
        }
      };
      
      reader.onerror = () => reject(new Error('Erreur lors de la lecture du fichier'));
      
      // Lire selon le type de fichier
      const extension = this.getFileExtension(file.name);
      if (this.supportedFileTypes.text.includes(extension)) {
        reader.readAsText(file, 'UTF-8');
      } else {
        reader.readAsArrayBuffer(file);
      }
    });
  }

  /**
   * Extrait le texte d'une image (simulation OCR)
   */
  private async extractTextFromImage(base64Content: string): Promise<string> {
    // Dans une vraie implémentation, on utiliserait Tesseract.js ou une API OCR
    // Pour l'instant, on simule
    return `[IMAGE DÉTECTÉE - ${new Date().toLocaleTimeString()}]\nContenu visuel disponible pour analyse. Dans une version future, le texte visible dans l'image sera automatiquement extrait.`;
  }

  /**
   * Extrait le texte d'un document (simulation)
   */
  private async extractTextFromDocument(file: File): Promise<string> {
    // Dans une vraie implémentation, on utiliserait pdf-parse ou similar
    // Pour l'instant, on simule
    return `[DOCUMENT ${file.name.toUpperCase()} DÉTECTÉ - ${new Date().toLocaleTimeString()}]\nDocument téléchargé avec succès. Dans une version future, le contenu du document sera automatiquement extrait et analysé.`;
  }

  /**
   * Obtient l'extension d'un fichier
   */
  private getFileExtension(filename: string): string {
    return filename.toLowerCase().substring(filename.lastIndexOf('.'));
  }

  /**
   * Formate la taille d'un fichier
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Génère un ID unique pour un fichier
   */
  private generateFileId(): string {
    return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Obtient les statistiques des fichiers
   */
  getFileStats(): {
    totalFiles: number;
    totalSize: number;
    typeBreakdown: Record<string, number>;
  } {
    const files = this.getAllContextFiles();
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    const typeBreakdown: Record<string, number> = {};

    files.forEach(file => {
      typeBreakdown[file.contentType] = (typeBreakdown[file.contentType] || 0) + 1;
    });

    return {
      totalFiles: files.length,
      totalSize,
      typeBreakdown
    };
  }
}

// Instance singleton
export const contextFileService = new ContextFileService();
