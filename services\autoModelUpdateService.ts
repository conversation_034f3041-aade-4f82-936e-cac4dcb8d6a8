/**
 * Service de mise à jour automatique des modèles gratuits et premium
 * Détecte hebdomadairement les nouveaux modèles disponibles sur OpenRouter
 * Août 2025 - 319 modèles total, 58 modèles gratuits détectés
 */

import { OPENROUTER_MODELS_API, PREMIUM_CONFIG } from '../constants';
import type { Step } from '../types';

// URLs spécialisées pour les modèles gratuits et payants (Août 2025)
const OPENROUTER_FREE_MODELS_URL = 'https://openrouter.ai/api/v1/models'; // Filtrage côté client pour les gratuits
const OPENROUTER_PREMIUM_MODELS_URL = 'https://openrouter.ai/api/v1/models'; // Filtrage côté client pour les premium
const FREE_MODELS_WEB_URL = 'https://openrouter.ai/models?max_price=0'; // 58 modèles gratuits actuellement
const PREMIUM_MODELS_WEB_URL = 'https://openrouter.ai/models?max_price=10'; // 310 modèles payants actuellement

export interface ModelInfo { 
    id: string;
    name: string;
    pricing: {
        prompt: string;
        completion: string;
    };
    context_length: number;
    created: number;
    description: string;
    architecture: {
        modality: string;
        input_modalities: string;
        output_modalities: string;
    };
}

export interface ModelClassification {
    isFree: boolean;
    isPremium: boolean;
    recommendedTask: Step['task'][];
    capabilities: {
        reasoning: boolean;
        coding: boolean;
        multimodal: boolean;
        large: boolean;
        new: boolean; // Nouveau modèle détecté
    };
    pricePerMillionTokens: {
        prompt: number;
        completion: number;
    };
}

export interface ModelUpdateReport {
    timestamp: number;
    totalModels: number;
    freeModels: number;
    premiumModels: number;
    newFreeModels: ModelInfo[];
    newPremiumModels: ModelInfo[];
    removedModels: string[];
    classificationByTask: {
        analyse: ModelInfo[];
        génération: ModelInfo[];
        validation: ModelInfo[];
        synthèse: ModelInfo[];
    };
}

class AutoModelUpdateService {
    private static instance: AutoModelUpdateService;
    private updateInterval: NodeJS.Timeout | null = null;
    private lastUpdateTimestamp = 0;
    private cachedModels: ModelInfo[] = [];
    private updateCallbacks: ((report: ModelUpdateReport) => void)[] = [];

    // Configuration
    private readonly UPDATE_INTERVAL_MS = 7 * 24 * 60 * 60 * 1000; // 7 jours
    private readonly STORAGE_KEY = 'auto_model_update_cache';
    private readonly LAST_UPDATE_KEY = 'auto_model_update_timestamp';

    private constructor() {
        this.loadFromStorage();
        this.startPeriodicUpdates();
    }

    public static getInstance(): AutoModelUpdateService {
        if (!AutoModelUpdateService.instance) {
            AutoModelUpdateService.instance = new AutoModelUpdateService();
        }
        return AutoModelUpdateService.instance;
    }

    /**
     * Charge les données en cache depuis le localStorage
     */
    private loadFromStorage(): void {
        try {
            const cachedData = localStorage.getItem(this.STORAGE_KEY);
            const lastUpdate = localStorage.getItem(this.LAST_UPDATE_KEY);

            if (cachedData && lastUpdate) {
                this.cachedModels = JSON.parse(cachedData);
                this.lastUpdateTimestamp = parseInt(lastUpdate, 10);
                console.log(`📦 Cache modèles chargée: ${this.cachedModels.length} modèles (dernière MAJ: ${new Date(this.lastUpdateTimestamp).toLocaleDateString()})`);
            }
        } catch (error) {
            console.warn('⚠️ Erreur lors du chargement du cache modèles:', error);
        }
    }

    /**
     * Sauvegarde les données dans le localStorage
     */
    private saveToStorage(): void {
        try {
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.cachedModels));
            localStorage.setItem(this.LAST_UPDATE_KEY, this.lastUpdateTimestamp.toString());
            console.log(`💾 Cache modèles sauvegardée: ${this.cachedModels.length} modèles`);
        } catch (error) {
            console.warn('⚠️ Erreur lors de la sauvegarde du cache modèles:', error);
        }
    }

    /**
     * Récupère tous les modèles depuis l'API OpenRouter avec classification améliorée
     */
    private async fetchAllModels(): Promise<ModelInfo[]> {
        try {
            console.log('🔍 Récupération des modèles depuis OpenRouter API (méthode améliorée)...');
            
            // Récupérer tous les modèles via l'API principale
            const response = await fetch(OPENROUTER_MODELS_API);

            if (!response.ok) {
                throw new Error(`Erreur API: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            const allModels = data.data || [];

            console.log(`✅ ${allModels.length} modèles récupérés depuis l'API OpenRouter`);
            
            // Classification améliorée basée sur les critères d'août 2025
            const freeModels = allModels.filter((model: ModelInfo) => this.isModelFreeAdvanced(model));
            const premiumModels = allModels.filter((model: ModelInfo) => this.isModelPremiumAdvanced(model));
            
            console.log(`📊 Classification: ${freeModels.length} gratuits, ${premiumModels.length} premium`);
            console.log(`🎯 Objectif attendu: ~58 gratuits, ~310 premium (selon URLs de filtrage)`);
            
            return allModels;
        } catch (error) {
            console.error('❌ Erreur lors de la récupération des modèles:', error);
            throw error;
        }
    }

    /**
     * Classification avancée pour déterminer si un modèle est gratuit
     * Basée sur les critères d'août 2025 (58 modèles gratuits attendus)
     */
    private isModelFreeAdvanced(model: ModelInfo): boolean {
        // Critère 1: Modèles avec ":free" dans l'ID
        if (model.id.includes(':free')) {
            return true;
        }
        
        // Critère 2: Prix exactement à 0 pour prompt et completion
        const promptPrice = parseFloat(model.pricing.prompt || '0');
        const completionPrice = parseFloat(model.pricing.completion || '0');
        
        if (promptPrice === 0 && completionPrice === 0) {
            return true;
        }
        
        // Critère 3: Modèles spécialement marqués comme gratuits
        const freeIndicators = [
            'free', 'gratis', 'complimentary', 'no-cost'
        ];
        
        const modelIdLower = model.id.toLowerCase();
        const descriptionLower = model.description.toLowerCase();
        
        return freeIndicators.some(indicator => 
            modelIdLower.includes(indicator) || descriptionLower.includes(indicator)
        );
    }

    /**
     * Classification avancée pour déterminer si un modèle est premium abordable
     * Basée sur les critères d'août 2025 (310 modèles payants avec max_price=10)
     */
    private isModelPremiumAdvanced(model: ModelInfo): boolean {
        const promptPrice = parseFloat(model.pricing.prompt || '0');
        const completionPrice = parseFloat(model.pricing.completion || '0');
        
        // Exclure les modèles gratuits
        if (this.isModelFreeAdvanced(model)) {
            return false;
        }
        
        // Critère: Prix raisonnable (jusqu'à 10$ par million de tokens)
        const maxPricePerMillion = 10 / 1000000; // 10$ = 0.00001 par token
        
        return promptPrice > 0 && 
               promptPrice <= maxPricePerMillion && 
               completionPrice <= maxPricePerMillion;
    }

    /**
     * Classifie un modèle (gratuit, premium, tâches recommandées, etc.)
     * Version améliorée avec critères d'août 2025
     */
    private classifyModel(model: ModelInfo, existingModelIds: Set<string>): ModelClassification {
        // Utiliser les nouvelles méthodes de classification avancées
        const isFree = this.isModelFreeAdvanced(model);
        const isPremium = this.isModelPremiumAdvanced(model);

        const promptPrice = parseFloat(model.pricing.prompt || '0');
        const completionPrice = parseFloat(model.pricing.completion || '0');

        // Détection des capacités basées sur l'ID et la description
        const modelIdLower = model.id.toLowerCase();
        const descriptionLower = model.description.toLowerCase();

        const capabilities = {
            reasoning: this.hasReasoningCapability(modelIdLower, descriptionLower),
            coding: this.hasCodingCapability(modelIdLower, descriptionLower),
            multimodal: this.hasMultimodalCapability(model, modelIdLower, descriptionLower),
            large: this.hasLargeModelCapability(modelIdLower, model.context_length),
            new: !existingModelIds.has(model.id) // Nouveau modèle
        };

        // Recommandation de tâches basée sur les capacités (logique améliorée)
        const recommendedTask: Step['task'][] = this.getRecommendedTasks(capabilities, model);

        return {
            isFree,
            isPremium,
            recommendedTask,
            capabilities,
            pricePerMillionTokens: {
                prompt: promptPrice * 1000000,
                completion: completionPrice * 1000000
            }
        };
    }

    /**
     * Détecte les capacités de raisonnement avancé
     */
    private hasReasoningCapability(modelIdLower: string, descriptionLower: string): boolean {
        const reasoningIndicators = [
            'deepseek-r1', 'qwq', 'thinking', 'nemotron', 'reasoning',
            'logic', 'analysis', 'problem-solving', 'cognitive'
        ];
        
        return reasoningIndicators.some(indicator => 
            modelIdLower.includes(indicator) || descriptionLower.includes(indicator)
        );
    }

    /**
     * Détecte les capacités de programmation
     */
    private hasCodingCapability(modelIdLower: string, descriptionLower: string): boolean {
        const codingIndicators = [
            'coder', 'code', 'devstral', 'programming', 'development',
            'coding', 'software', 'algorithm', 'deepcoder'
        ];
        
        return codingIndicators.some(indicator => 
            modelIdLower.includes(indicator) || descriptionLower.includes(indicator)
        );
    }

    /**
     * Détecte les capacités multimodales
     */
    private hasMultimodalCapability(model: ModelInfo, modelIdLower: string, descriptionLower: string): boolean {
        const multimodalIndicators = [
            'vision', 'vl', 'multimodal', 'image', 'visual', 'kimi-vl'
        ];
        
        const hasMultimodalId = multimodalIndicators.some(indicator => 
            modelIdLower.includes(indicator) || descriptionLower.includes(indicator)
        );
        
        const hasImageInput = model.architecture?.input_modalities?.includes('image') ||
                             model.architecture?.modality?.includes('image');
        
        return hasMultimodalId || hasImageInput;
    }

    /**
     * Détecte les modèles de grande taille
     */
    private hasLargeModelCapability(modelIdLower: string, contextLength: number): boolean {
        const largeModelIndicators = [
            '405b', '235b', '253b', '72b', '70b', '120b'
        ];
        
        const hasLargeId = largeModelIndicators.some(indicator => 
            modelIdLower.includes(indicator)
        );
        
        const hasLargeContext = contextLength && contextLength >= 100000; // 100K+ tokens
        
        return hasLargeId || !!hasLargeContext;
    }

    /**
     * Détermine les tâches recommandées basées sur les capacités
     */
    private getRecommendedTasks(capabilities: any, model: ModelInfo): Step['task'][] {
        const tasks: Step['task'][] = [];
        
        // Logique de recommandation améliorée
        if (capabilities.reasoning) {
            tasks.push('analyse', 'validation');
        }
        
        if (capabilities.coding) {
            tasks.push('génération');
        }
        
        if (capabilities.large || capabilities.reasoning) {
            tasks.push('synthèse');
        }
        
        if (capabilities.multimodal) {
            tasks.push('analyse'); // Les modèles multimodaux sont excellents pour l'analyse
        }
        
        // Si aucune spécialité détectée, recommander selon le nom/contexte
        if (tasks.length === 0) {
            const modelIdLower = model.id.toLowerCase();
            
            if (modelIdLower.includes('instruct') || modelIdLower.includes('chat')) {
                tasks.push('analyse', 'synthèse');
            } else if (modelIdLower.includes('small') || modelIdLower.includes('mini')) {
                tasks.push('analyse');
            } else {
                tasks.push('analyse'); // Par défaut
            }
        }
        
        return [...new Set(tasks)]; // Éliminer les doublons
    }

    /**
     * Génère un rapport de mise à jour complet
     */
    private generateUpdateReport(
        newModels: ModelInfo[], 
        previousModels: ModelInfo[]
    ): ModelUpdateReport {
        const existingModelIds = new Set(previousModels.map(m => m.id));
        const newModelIds = new Set(newModels.map(m => m.id));

        // Identifier les nouveaux et supprimés
        const newFreeModels: ModelInfo[] = [];
        const newPremiumModels: ModelInfo[] = [];
        const removedModels: string[] = [];

        // Classification par tâche
        const classificationByTask: ModelUpdateReport['classificationByTask'] = {
            analyse: [],
            génération: [],
            validation: [],
            synthèse: []
        };

        // Analyser chaque nouveau modèle
        newModels.forEach(model => {
            const classification = this.classifyModel(model, existingModelIds);

            if (classification.capabilities.new) {
                if (classification.isFree) {
                    newFreeModels.push(model);
                } else if (classification.isPremium) {
                    newPremiumModels.push(model);
                }
            }

            // Classer par tâche recommandée
            if (classification.isFree || classification.isPremium) {
                classification.recommendedTask.forEach(task => {
                    classificationByTask[task].push(model);
                });
            }
        });

        // Identifier les modèles supprimés
        previousModels.forEach(model => {
            if (!newModelIds.has(model.id)) {
                removedModels.push(model.id);
            }
        });

        const freeModels = newModels.filter(m => 
            this.classifyModel(m, new Set()).isFree
        );
        const premiumModels = newModels.filter(m => 
            this.classifyModel(m, new Set()).isPremium
        );

        return {
            timestamp: Date.now(),
            totalModels: newModels.length,
            freeModels: freeModels.length,
            premiumModels: premiumModels.length,
            newFreeModels,
            newPremiumModels,
            removedModels,
            classificationByTask
        };
    }

    /**
     * Effectue une mise à jour complète des modèles
     */
    public async performUpdate(forceUpdate = false): Promise<ModelUpdateReport> {
        const now = Date.now();
        const shouldUpdate = forceUpdate || 
                           this.cachedModels.length === 0 || 
                           (now - this.lastUpdateTimestamp) > this.UPDATE_INTERVAL_MS;

        if (!shouldUpdate) {
            throw new Error('Mise à jour non nécessaire. Utilisez forceUpdate=true pour forcer.');
        }

        try {
            console.log('🔄 Début de la mise à jour automatique des modèles...');
            
            const previousModels = [...this.cachedModels];
            const newModels = await this.fetchAllModels();
            
            // Générer le rapport de mise à jour
            const report = this.generateUpdateReport(newModels, previousModels);
            
            // Mettre à jour le cache
            this.cachedModels = newModels;
            this.lastUpdateTimestamp = now;
            this.saveToStorage();

            // Notifier les callbacks
            this.updateCallbacks.forEach(callback => {
                try {
                    callback(report);
                } catch (error) {
                    console.warn('⚠️ Erreur dans un callback de mise à jour:', error);
                }
            });

            console.log(`✅ Mise à jour terminée: ${report.totalModels} modèles (${report.freeModels} gratuits, ${report.premiumModels} premium)`);
            console.log(`🆕 Nouveaux modèles: ${report.newFreeModels.length} gratuits, ${report.newPremiumModels.length} premium`);
            
            if (report.removedModels.length > 0) {
                console.log(`🗑️ Modèles supprimés: ${report.removedModels.length}`);
            }

            return report;
        } catch (error) {
            console.error('❌ Erreur lors de la mise à jour des modèles:', error);
            throw error;
        }
    }

    /**
     * Démarre les mises à jour périodiques automatiques
     */
    private startPeriodicUpdates(): void {
        // Vérifier immédiatement si une mise à jour est nécessaire
        const now = Date.now();
        if (this.cachedModels.length === 0 || (now - this.lastUpdateTimestamp) > this.UPDATE_INTERVAL_MS) {
            // Mise à jour dans 5 secondes pour laisser l'app se charger
            setTimeout(() => {
                this.performUpdate().catch(error => {
                    console.warn('⚠️ Échec de la mise à jour automatique au démarrage:', error);
                });
            }, 5000);
        }

        // Planifier les vérifications quotidiennes
        this.updateInterval = setInterval(async () => {
            try {
                await this.performUpdate();
            } catch (error) {
                // Ignore les erreurs "mise à jour non nécessaire"
                if (error instanceof Error && !error.message.includes('non nécessaire')) {
                    console.warn('⚠️ Échec de la mise à jour périodique:', error);
                }
            }
        }, 24 * 60 * 60 * 1000); // Vérification quotidienne

        console.log('🕐 Mise à jour automatique programmée (vérification quotidienne, mise à jour hebdomadaire)');
    }

    /**
     * Enregistre un callback pour les notifications de mise à jour
     */
    public onUpdate(callback: (report: ModelUpdateReport) => void): void {
        this.updateCallbacks.push(callback);
    }

    /**
     * Obtient le statut de la prochaine mise à jour
     */
    public getUpdateStatus(): {
        lastUpdate: Date;
        nextUpdate: Date;
        daysUntilNextUpdate: number;
        modelsCount: number;
    } {
        const lastUpdate = new Date(this.lastUpdateTimestamp);
        const nextUpdate = new Date(this.lastUpdateTimestamp + this.UPDATE_INTERVAL_MS);
        const daysUntilNextUpdate = Math.max(0, Math.ceil((nextUpdate.getTime() - Date.now()) / (24 * 60 * 60 * 1000)));

        return {
            lastUpdate,
            nextUpdate,
            daysUntilNextUpdate,
            modelsCount: this.cachedModels.length
        };
    }

    /**
     * Obtient les modèles gratuits pour une tâche donnée
     */
    public getFreeModelsForTask(task: Step['task']): ModelInfo[] {
        return this.cachedModels.filter(model => {
            const classification = this.classifyModel(model, new Set());
            return classification.isFree && classification.recommendedTask.includes(task);
        });
    }

    /**
     * Obtient les modèles premium pour une tâche donnée
     */
    public getPremiumModelsForTask(task: Step['task']): ModelInfo[] {
        return this.cachedModels.filter(model => {
            const classification = this.classifyModel(model, new Set());
            return classification.isPremium && classification.recommendedTask.includes(task);
        });
    }

    /**
     * Génère une configuration mise à jour pour constants.ts avec validation des comptages
     */
    public generateUpdatedConstants(): {
        freeModels: Record<Step['task'], string[]>;
        premiumModels: Record<Step['task'], string[]>;
        validationReport: {
            expectedFreeCount: number;
            actualFreeCount: number;
            expectedPremiumCount: number;
            actualPremiumCount: number;
            accuracyScore: number;
        };
    } {
        const freeModels: Record<Step['task'], string[]> = {
            analyse: [],
            génération: [],
            validation: [],
            synthèse: []
        };

        const premiumModels: Record<Step['task'], string[]> = {
            analyse: [],
            génération: [],
            validation: [],
            synthèse: []
        };

        let actualFreeCount = 0;
        let actualPremiumCount = 0;

        this.cachedModels.forEach(model => {
            const classification = this.classifyModel(model, new Set());
            
            if (classification.isFree) {
                actualFreeCount++;
                classification.recommendedTask.forEach(task => {
                    if (!freeModels[task].includes(model.id)) {
                        freeModels[task].push(model.id);
                    }
                });
            }
            
            if (classification.isPremium) {
                actualPremiumCount++;
                classification.recommendedTask.forEach(task => {
                    if (!premiumModels[task].includes(model.id)) {
                        premiumModels[task].push(model.id);
                    }
                });
            }
        });

        // Validation par rapport aux chiffres de référence (Août 2025)
        const expectedFreeCount = 58; // Selon https://openrouter.ai/models?max_price=0
        const expectedPremiumCount = 310; // Selon https://openrouter.ai/models?max_price=10

        // Calcul du score de précision
        const freeAccuracy = Math.min(1, actualFreeCount / expectedFreeCount);
        const premiumAccuracy = Math.min(1, actualPremiumCount / expectedPremiumCount);
        const accuracyScore = (freeAccuracy + premiumAccuracy) / 2;

        const validationReport = {
            expectedFreeCount,
            actualFreeCount,
            expectedPremiumCount,
            actualPremiumCount,
            accuracyScore: Math.round(accuracyScore * 100) / 100
        };

        // Log du rapport de validation
        console.log('📊 Rapport de validation des classifications:');
        console.log(`   💝 Gratuits: ${actualFreeCount}/${expectedFreeCount} (${Math.round(freeAccuracy * 100)}%)`);
        console.log(`   🌟 Premium: ${actualPremiumCount}/${expectedPremiumCount} (${Math.round(premiumAccuracy * 100)}%)`);
        console.log(`   🎯 Score global: ${Math.round(accuracyScore * 100)}%`);

        return { freeModels, premiumModels, validationReport };
    }

    /**
     * Effectue une validation avancée en comparant avec les URLs de référence
     */
    public async validateClassificationAccuracy(): Promise<{
        success: boolean;
        freeModelsValidation: { expected: number; actual: number; accuracy: number };
        premiumModelsValidation: { expected: number; actual: number; accuracy: number };
        recommendations: string[];
    }> {
        try {
            console.log('🧪 Validation avancée des classifications...');
            
            const freeModels = this.cachedModels.filter(m => this.isModelFreeAdvanced(m));
            const premiumModels = this.cachedModels.filter(m => this.isModelPremiumAdvanced(m));
            
            const expectedFree = 58;
            const expectedPremium = 310;
            
            const freeAccuracy = freeModels.length / expectedFree;
            const premiumAccuracy = premiumModels.length / expectedPremium;
            
            const recommendations: string[] = [];
            
            if (freeAccuracy < 0.8) {
                recommendations.push(`🔍 Réviser les critères de détection des modèles gratuits (${freeModels.length}/${expectedFree})`);
            }
            
            if (premiumAccuracy < 0.8) {
                recommendations.push(`🔍 Réviser les critères de détection des modèles premium (${premiumModels.length}/${expectedPremium})`);
            }
            
            if (freeAccuracy > 1.2) {
                recommendations.push(`⚠️ Trop de modèles classés comme gratuits - critères trop permissifs`);
            }
            
            if (premiumAccuracy > 1.2) {
                recommendations.push(`⚠️ Trop de modèles classés comme premium - critères trop permissifs`);
            }
            
            if (recommendations.length === 0) {
                recommendations.push(`✅ Classifications conformes aux attentes d'août 2025`);
            }
            
            return {
                success: true,
                freeModelsValidation: {
                    expected: expectedFree,
                    actual: freeModels.length,
                    accuracy: Math.round(freeAccuracy * 100) / 100
                },
                premiumModelsValidation: {
                    expected: expectedPremium,
                    actual: premiumModels.length,
                    accuracy: Math.round(premiumAccuracy * 100) / 100
                },
                recommendations
            };
            
        } catch (error) {
            console.error('❌ Erreur lors de la validation:', error);
            return {
                success: false,
                freeModelsValidation: { expected: 58, actual: 0, accuracy: 0 },
                premiumModelsValidation: { expected: 310, actual: 0, accuracy: 0 },
                recommendations: ['❌ Erreur lors de la validation - vérifier la connectivité API']
            };
        }
    }

    /**
     * Nettoie les ressources
     */
    public cleanup(): void {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }
}

// Instance singleton
export const autoModelUpdateService = AutoModelUpdateService.getInstance();
