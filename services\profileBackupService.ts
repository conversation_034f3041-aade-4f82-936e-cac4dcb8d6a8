/**
 * Service de sauvegarde automatique du profil utilisateur
 * Sauvegarde UNIQUEMENT les données personnelles de l'utilisateur
 * EXCLUT toutes les données système/API/sensibles
 */

import type { Message } from '../types';

// Interface pour les préférences utilisateur
export interface UserPreferences {
  theme?: 'light' | 'dark' | 'auto';
  language?: 'fr' | 'en';
  autoSave?: boolean;
  saveInterval?: number; // en secondes
  notifications?: boolean;
  compactMode?: boolean;
}

// Interface pour l'historique des workflows
export interface WorkflowHistory {
  id: string;
  timestamp: number;
  problemDescription: string;
  stepsCompleted: number;
  totalSteps: number;
  finalOutput?: string;
  userSatisfaction?: number; // 1-5
}

// Interface pour les données personnelles saisies
export interface PersonalData {
  recentProblems: string[];
  favoritePrompts: string[];
  customInstructions?: string;
  notes?: string[];
}

// Interface principale du profil utilisateur
export interface UserProfile {
  metadata: {
    version: string;
    exportDate: string;
    userId?: string;
    appVersion: string;
  };
  conversations: Message[];
  workflows: WorkflowHistory[];
  preferences: UserPreferences;
  personalData: PersonalData;
}

// Configuration du service de sauvegarde
const BACKUP_CONFIG = {
  STORAGE_KEY: 'user_profile_backup',
  AUTO_SAVE_INTERVAL: 30000, // 30 secondes
  MAX_CONVERSATIONS: 100, // Limite pour éviter des fichiers trop volumineux
  MAX_WORKFLOWS: 50,
  VERSION: '1.0.0'
};

/**
 * Service de gestion des sauvegardes du profil utilisateur
 */
export class ProfileBackupService {
  private static instance: ProfileBackupService;
  private autoSaveInterval: NodeJS.Timeout | null = null;
  private currentProfile: UserProfile;
  private saveCallbacks: ((profile: UserProfile) => void)[] = [];
  private lastSaveTime = 0;

  private constructor() {
    this.currentProfile = this.initializeProfile();
    this.loadFromLocalStorage();
    this.startAutoSave();
  }

  public static getInstance(): ProfileBackupService {
    if (!ProfileBackupService.instance) {
      ProfileBackupService.instance = new ProfileBackupService();
    }
    return ProfileBackupService.instance;
  }

  /**
   * Initialise un profil utilisateur vide
   */
  private initializeProfile(): UserProfile {
    return {
      metadata: {
        version: BACKUP_CONFIG.VERSION,
        exportDate: new Date().toISOString(),
        appVersion: '3.1.0',
      },
      conversations: [],
      workflows: [],
      preferences: {
        theme: 'auto',
        language: 'fr',
        autoSave: true,
        saveInterval: 30,
        notifications: true,
        compactMode: false
      },
      personalData: {
        recentProblems: [],
        favoritePrompts: [],
        notes: []
      }
    };
  }

  /**
   * Charge le profil depuis le localStorage
   */
  private loadFromLocalStorage(): void {
    try {
      const savedProfile = localStorage.getItem(BACKUP_CONFIG.STORAGE_KEY);
      if (savedProfile) {
        const parsed = JSON.parse(savedProfile) as UserProfile;
        
        // Vérification de la version et migration si nécessaire
        if (parsed.metadata?.version === BACKUP_CONFIG.VERSION) {
          this.currentProfile = { ...this.currentProfile, ...parsed };
          console.log('📦 Profil utilisateur chargé depuis la sauvegarde locale');
        } else {
          console.log('🔄 Migration du profil utilisateur nécessaire');
          this.migrateProfile(parsed);
        }
      }
    } catch (error) {
      console.warn('⚠️ Erreur lors du chargement du profil utilisateur:', error);
    }
  }

  /**
   * Sauvegarde le profil dans le localStorage
   */
  private saveToLocalStorage(): void {
    try {
      // Nettoyer les données avant sauvegarde (limites)
      const cleanedProfile = this.cleanProfileForSave();
      
      localStorage.setItem(BACKUP_CONFIG.STORAGE_KEY, JSON.stringify(cleanedProfile));
      this.lastSaveTime = Date.now();
      
      // Notifier les callbacks
      this.saveCallbacks.forEach(callback => callback(cleanedProfile));
      
      console.log('💾 Profil utilisateur sauvegardé automatiquement');
    } catch (error) {
      console.warn('⚠️ Erreur lors de la sauvegarde du profil:', error);
    }
  }

  /**
   * Nettoie le profil avant sauvegarde (applique les limites)
   */
  private cleanProfileForSave(): UserProfile {
    const cleaned = { ...this.currentProfile };
    
    // Limiter le nombre de conversations
    if (cleaned.conversations.length > BACKUP_CONFIG.MAX_CONVERSATIONS) {
      cleaned.conversations = cleaned.conversations.slice(-BACKUP_CONFIG.MAX_CONVERSATIONS);
    }
    
    // Limiter le nombre de workflows
    if (cleaned.workflows.length > BACKUP_CONFIG.MAX_WORKFLOWS) {
      cleaned.workflows = cleaned.workflows.slice(-BACKUP_CONFIG.MAX_WORKFLOWS);
    }
    
    // Mettre à jour la date d'export
    cleaned.metadata.exportDate = new Date().toISOString();
    
    return cleaned;
  }

  /**
   * Migration du profil (versions futures)
   */
  private migrateProfile(oldProfile: any): void {
    // Pour l'instant, on réinitialise si version incompatible
    console.log('🔄 Réinitialisation du profil pour nouvelle version');
    this.currentProfile = this.initializeProfile();
    this.saveToLocalStorage();
  }

  /**
   * Démarre la sauvegarde automatique
   */
  private startAutoSave(): void {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
    }
    
    const interval = this.currentProfile.preferences.autoSave 
      ? (this.currentProfile.preferences.saveInterval || 30) * 1000
      : BACKUP_CONFIG.AUTO_SAVE_INTERVAL;
    
    this.autoSaveInterval = setInterval(() => {
      if (this.currentProfile.preferences.autoSave) {
        this.saveToLocalStorage();
      }
    }, interval);
  }

  /**
   * Ajoute une conversation à l'historique
   */
  public addConversation(message: Message): void {
    this.currentProfile.conversations.push({
      ...message,
      timestamp: Date.now()
    } as any);
  }

  /**
   * Ajoute un workflow à l'historique
   */
  public addWorkflow(workflow: Omit<WorkflowHistory, 'id' | 'timestamp'>): void {
    const newWorkflow: WorkflowHistory = {
      ...workflow,
      id: `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now()
    };
    
    this.currentProfile.workflows.push(newWorkflow);
  }

  /**
   * Met à jour les préférences utilisateur
   */
  public updatePreferences(preferences: Partial<UserPreferences>): void {
    this.currentProfile.preferences = {
      ...this.currentProfile.preferences,
      ...preferences
    };
    
    // Redémarrer l'auto-save si l'intervalle a changé
    if (preferences.saveInterval || preferences.autoSave !== undefined) {
      this.startAutoSave();
    }
    
    this.saveToLocalStorage();
  }

  /**
   * Met à jour les données personnelles
   */
  public updatePersonalData(data: Partial<PersonalData>): void {
    this.currentProfile.personalData = {
      ...this.currentProfile.personalData,
      ...data
    };
  }

  /**
   * Obtient le profil actuel
   */
  public getProfile(): UserProfile {
    return { ...this.currentProfile };
  }

  /**
   * Exporte le profil en JSON pour téléchargement
   */
  public exportProfile(): string {
    const exportProfile = this.cleanProfileForSave();
    return JSON.stringify(exportProfile, null, 2);
  }

  /**
   * Importe un profil depuis un fichier JSON
   */
  public importProfile(jsonData: string): boolean {
    try {
      const importedProfile = JSON.parse(jsonData) as UserProfile;
      
      // Validation basique
      if (!importedProfile.metadata || !importedProfile.conversations) {
        throw new Error('Format de profil invalide');
      }
      
      this.currentProfile = {
        ...this.initializeProfile(),
        ...importedProfile,
        metadata: {
          ...importedProfile.metadata,
          exportDate: new Date().toISOString()
        }
      };
      
      this.saveToLocalStorage();
      console.log('📥 Profil importé avec succès');
      return true;
    } catch (error) {
      console.error('❌ Erreur lors de l\'importation du profil:', error);
      return false;
    }
  }

  /**
   * Sauvegarde manuelle forcée
   */
  public forceSave(): void {
    this.saveToLocalStorage();
  }

  /**
   * Réinitialise le profil
   */
  public resetProfile(): void {
    this.currentProfile = this.initializeProfile();
    this.saveToLocalStorage();
    console.log('🔄 Profil utilisateur réinitialisé');
  }

  /**
   * Ajoute un callback pour les sauvegardes
   */
  public onSave(callback: (profile: UserProfile) => void): void {
    this.saveCallbacks.push(callback);
  }

  /**
   * Obtient les statistiques de sauvegarde
   */
  public getBackupStats() {
    return {
      lastSaveTime: this.lastSaveTime,
      conversationsCount: this.currentProfile.conversations.length,
      workflowsCount: this.currentProfile.workflows.length,
      autoSaveEnabled: this.currentProfile.preferences.autoSave,
      saveInterval: this.currentProfile.preferences.saveInterval
    };
  }

  /**
   * Nettoyage lors de la destruction
   */
  public destroy(): void {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
    }
  }
}

// Instance singleton
export const profileBackupService = ProfileBackupService.getInstance();
