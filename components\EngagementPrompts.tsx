import React, { useState } from 'react';

interface EngagementPromptsProps {
  currentStep: number;
  totalSteps: number;
  onSuggestedAction?: (action: string) => void;
}

const ENGAGEMENT_SUGGESTIONS = {
  early: [
    "🔍 Détaillez-moi un aspect qui vous préoccupe particulièrement",
    "⚠️ Partagez-moi vos contraintes spécifiques (budget, temps, équipe...)",
    "🔄 Explorons ensemble une approche alternative",
    "📝 Précisez-moi votre contexte d'entreprise"
  ],
  middle: [
    "✅ Cette direction correspond-elle à vos attentes ?",
    "🎯 Ajustons notre approche selon vos retours",
    "🔧 Quels éléments souhaitez-vous approfondir ?",
    "💡 Avez-vous d'autres idées à intégrer ?"
  ],
  late: [
    "🎉 Cette solution répond-elle parfaitement à vos besoins ?",
    "✨ Affinons les derniers détails ensemble",
    "🚀 Êtes-vous prêt pour votre prompt final optimisé ?",
    "📋 Validons ensemble les derniers points"
  ]
};

export const EngagementPrompts: React.FC<EngagementPromptsProps> = ({
  currentStep,
  totalSteps,
  onSuggestedAction
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const getPhase = () => {
    const progress = currentStep / totalSteps;
    if (progress < 0.33) return 'early';
    if (progress < 0.66) return 'middle';
    return 'late';
  };

  const getSuggestions = () => {
    const phase = getPhase();
    return ENGAGEMENT_SUGGESTIONS[phase];
  };

  const handleSuggestionClick = (suggestion: string) => {
    if (onSuggestedAction) {
      onSuggestedAction(suggestion);
    }
  };

  return (
    <div className="bg-gradient-to-r from-indigo-900/20 to-purple-900/20 rounded-xl border border-indigo-500/30 overflow-hidden shadow-lg">
      <div
        className="p-3 bg-gradient-to-r from-indigo-800/40 to-purple-800/40 border-b border-indigo-500/30 cursor-pointer hover:from-indigo-800/50 hover:to-purple-800/50 transition-all duration-200"
        onClick={() => setIsCollapsed(!isCollapsed)}
      >
        <div className="flex items-center gap-3">
          <div className="w-2 h-2 bg-indigo-400 rounded-full animate-pulse"></div>
          <span className="text-sm text-indigo-200 font-semibold">🤖 Roony vous suggère</span>
          <div className="ml-auto flex items-center gap-2">
            <div className="text-xs text-indigo-300/70">
              {isCollapsed ? 'Cliquez pour déplier' : 'Cliquez pour plier'}
            </div>
            <svg
              className={`w-4 h-4 text-indigo-300 transition-transform duration-200 ${isCollapsed ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
      </div>

      {!isCollapsed && (
        <div className="p-4 space-y-2 animate-in slide-in-from-top-2 duration-200">
          {getSuggestions().map((suggestion, index) => (
            <button
              key={index}
              onClick={() => handleSuggestionClick(suggestion)}
              className="w-full text-left text-sm text-slate-300 hover:text-white hover:bg-indigo-600/30 p-3 rounded-lg transition-all duration-300 border border-slate-600/30 hover:border-indigo-400/50 hover:shadow-md hover:scale-[1.02] group"
            >
              <div className="flex items-center gap-2">
                <span className="group-hover:scale-110 transition-transform duration-200">{suggestion.split(' ')[0]}</span>
                <span className="flex-1">{suggestion.substring(suggestion.indexOf(' ') + 1)}</span>
                <span className="text-indigo-400 opacity-0 group-hover:opacity-100 transition-opacity duration-200">→</span>
              </div>
            </button>
          ))}

          <div className="pt-3 border-t border-slate-700/50">
            <div className="bg-slate-800/50 rounded-lg p-3 text-center">
              <p className="text-xs text-slate-400 mb-1">
                💬 <strong>Besoin d'aide ?</strong>
              </p>
              <p className="text-xs text-slate-500">
                Cliquez sur une suggestion ci-dessus ou tapez votre réponse dans le champ principal
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
