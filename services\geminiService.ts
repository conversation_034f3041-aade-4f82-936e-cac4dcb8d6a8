import { WORKFLOW_STEPS, OPENROUTER_MODELS, MODEL_DETECTION_CONFIG, LANGUAGE_VALIDATION_CONFIG, OPENROUTER_API_URL } from '../constants';
import { modelDetectionService } from './modelDetectionService';
import { languageValidationService, type LanguageValidationResult } from './languageValidationService';
import { translationService } from './translationService';
import { apiKeyManager } from './apiKeyManager';
import { premiumApiService } from './premiumApiService';
import { expertConsultantService } from '../src/services/expertConsultantService';
import { knowledgeBaseService } from '../src/services/knowledgeBaseService';
import type { Step, Message } from '../types';

// Variable globale pour contrôler le mode d'utilisation
export let currentMode: 'free' | 'premium' = 'free';

/**
 * Change le mode d'utilisation entre gratuit et premium
 */
export function setUsageMode(mode: 'free' | 'premium'): void {
    currentMode = mode;
    console.log(`🎯 Mode d'utilisation changé vers: ${mode === 'premium' ? '🌟 Premium' : '💝 Gratuit'}`);
}

// Les clés API sont maintenant gérées par le service apiKeyManager
// pour une rotation intelligente et une gestion d'erreurs optimisée



// Utilisation du service de détection des modèles

/**
 * Sélectionne intelligemment un modèle pour une tâche donnée
 * Avec rotation automatique et vérification de disponibilité
 */
function selectModelForTask(task: Step['task'], excludeModels: string[] = []): string {
    const modelsForTask = OPENROUTER_MODELS[task];

    // Filtrer les modèles exclus et vérifier la disponibilité via le service
    const availableModels = modelsForTask.filter(model =>
        !excludeModels.includes(model) && modelDetectionService.isModelAvailable(model)
    );

    if (availableModels.length === 0) {
        console.warn(`⚠️ Aucun modèle disponible pour la tâche: ${task}`);
        // Fallback vers le premier modèle de la liste originale
        return modelsForTask[0];
    }

    // Sélection intelligente: varier les modèles pour optimiser le raisonnement agentique
    const selectedModel = availableModels[Math.floor(Math.random() * availableModels.length)];
    console.log(`🤖 Modèle sélectionné pour ${task}: ${selectedModel}`);

    return selectedModel;
}

/**
 * Fonction pour nettoyer le texte des symboles de réflexion indésirables
 */
const cleanThinkingSymbols = (text: string): string => {
    // Supprimer les symboles de réflexion bizarres comme ◁think▷, <think>, etc.
    return text
        .replace(/◁think▷/gi, '')
        .replace(/<think>/gi, '')
        .replace(/<\/think>/gi, '')
        .replace(/\[think\]/gi, '')
        .replace(/\[\/think\]/gi, '')
        .replace(/◁.*?▷/g, '')
        .replace(/【.*?】/g, '')
        .trim();
};

/**
 * Fonction principale pour envoyer un message à l'IA avec rotation automatique des modèles
 * et validation de langue française. Prend en charge les modes gratuit et Premium.
 */
export async function sendMessageToAI(
  messages: { role: string; content: string }[],
  task: Step['task']
): Promise<{ content: string; modelUsed: string; languageValidation?: LanguageValidationResult }> {
    console.log(`🎯 Envoi de message en mode ${currentMode === 'premium' ? '🌟 Premium' : '💝 Gratuit'} pour la tâche: ${task}`);

    // Si le mode Premium est activé et disponible, utiliser les modèles Premium
    if (currentMode === 'premium' && premiumApiService.isPremiumAvailable()) {
        try {
            return await premiumApiService.sendMessageToPremiumAI(messages, task);
        } catch (premiumError) {
            console.warn('⚠️ Échec du mode Premium, basculement vers le mode gratuit:', premiumError);
            // Continuer avec le mode gratuit en cas d'échec Premium
        }
    }

    // Mode gratuit (par défaut ou fallback)
    console.log('🔄 Utilisation du mode gratuit avec rotation intelligente des clés API');

    // Mise à jour de la cache des modèles via le service de détection
    await modelDetectionService.updateModelsIfNeeded();

    const excludedModels: string[] = [];
    let lastError: Error | null = null;
    let languageRetryCount = 0;

    // Tentatives avec rotation des modèles et validation de langue
    for (let attempt = 0; attempt < MODEL_DETECTION_CONFIG.MAX_RETRIES; attempt++) {
        const model = selectModelForTask(task, excludedModels);

        // Obtenir une clé API via le gestionnaire avec rotation intelligente
        const { key: apiKey, keyId } = apiKeyManager.getNextApiKey();

        try {
            console.log(`🚀 Tentative ${attempt + 1}/${MODEL_DETECTION_CONFIG.MAX_RETRIES} avec le modèle: ${model} (Clé: ${keyId})`);

            const response = await fetch(OPENROUTER_API_URL, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json',
                    // Headers recommandés par OpenRouter pour l'accès aux modèles gratuits
                    'HTTP-Referer': 'https://agentic-workflow-studio.web.app',
                    'X-Title': 'Agentic Workflow Studio',
                },
                body: JSON.stringify({
                    model: model,
                    messages: messages,
                    temperature: 0.7,
                    top_p: 0.95,
                }),
            });

            if (!response.ok) {
                const errorBody = await response.json().catch(() => ({
                    error: { message: 'Impossible de lire la réponse d\'erreur de l\'API.' }
                }));

                const errorMsg = `Erreur API OpenRouter: ${response.status} ${response.statusText} - ${errorBody.error?.message || 'Erreur inconnue'}`;
                console.warn(`❌ Échec avec ${model} (Clé: ${keyId}): ${errorMsg}`);

                // Enregistrer l'échec dans le gestionnaire de clés
                apiKeyManager.recordApiResult(apiKey, false, response.status, errorBody.error?.message);

                // Exclure ce modèle pour les prochaines tentatives
                excludedModels.push(model);
                lastError = new Error(errorMsg);

                // Attendre avant la prochaine tentative (délai adaptatif selon l'erreur)
                let retryDelay = MODEL_DETECTION_CONFIG.RETRY_DELAY;
                if (response.status === 429) {
                    retryDelay = 2000; // 2 secondes pour rate limit
                } else if (response.status === 503) {
                    retryDelay = 3000; // 3 secondes pour service unavailable
                }

                if (attempt < MODEL_DETECTION_CONFIG.MAX_RETRIES - 1) {
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
                continue;
            }

            const data = await response.json();
            const rawContent = data.choices[0]?.message?.content || "Désolé, je n'ai pas pu générer une réponse.";
            const content = cleanThinkingSymbols(rawContent);

            // Validation de langue et traduction automatique si activée
            if (LANGUAGE_VALIDATION_CONFIG.ENABLE_STRICT_VALIDATION) {
                const languageValidation = languageValidationService.validateFrenchResponse(content, model);

                console.log(`📊 Validation linguistique: ${languageValidation.detectedLanguage} (confiance: ${languageValidation.confidence.toFixed(1)}%)`);

                // NOUVELLE APPROCHE: Traduction automatique au lieu de rejet
                let finalContent = content;
                let wasTranslated = false;

                // Si la réponse n'est pas française, on la traduit automatiquement
                if (!languageValidation.isValid) {
                    console.log(`🔄 Traduction automatique nécessaire pour le modèle: ${model}`);

                    try {
                        const translationResult = await translationService.translateToFrench(
                            content,
                            languageValidation.detectedLanguage,
                            languageValidation.confidence
                        );

                        if (translationResult.wasTranslated) {
                            finalContent = cleanThinkingSymbols(translationResult.translatedText);
                            wasTranslated = true;
                            console.log(`✅ Traduction réussie avec le modèle: ${model}`);
                        } else {
                            console.log(`ℹ️ Pas de traduction nécessaire pour: ${model}`);
                        }
                    } catch (translationError) {
                        console.warn(`⚠️ Erreur de traduction pour ${model}:`, translationError);
                        // En cas d'erreur de traduction, on garde le contenu original
                    }
                }

                // Enregistrer le succès dans le gestionnaire de clés
                apiKeyManager.recordApiResult(apiKey, true);

                // Log du résultat final
                if (languageValidation.isValid) {
                    console.log(`✅ Succès avec le modèle: ${model} (Clé: ${keyId}) - français validé, confiance: ${languageValidation.confidence.toFixed(1)}%`);
                } else if (wasTranslated) {
                    console.log(`✅ Succès avec traduction: ${model} (Clé: ${keyId}) - ${languageValidation.detectedLanguage} → français`);
                } else {
                    console.warn(`⚠️ Réponse acceptée sans traduction: ${model} (Clé: ${keyId}) - ${languageValidation.detectedLanguage}, confiance: ${languageValidation.confidence.toFixed(1)}%`);
                }

                return {
                    content: finalContent,
                    modelUsed: model,
                    languageValidation: {
                        ...languageValidation,
                        wasTranslated,
                        finalContent
                    } as any
                };
            }

            // Enregistrer le succès dans le gestionnaire de clés (cas sans validation linguistique)
            apiKeyManager.recordApiResult(apiKey, true);

            console.log(`✅ Succès avec le modèle: ${model} (Clé: ${keyId})`);
            return { content, modelUsed: model };

        } catch (error) {
            console.warn(`❌ Erreur avec le modèle ${model} (Clé: ${keyId}):`, error);

            // Enregistrer l'échec dans le gestionnaire de clés
            apiKeyManager.recordApiResult(apiKey, false, undefined, error instanceof Error ? error.message : 'Erreur inconnue');

            excludedModels.push(model);
            lastError = error as Error;

            // Attendre avant la prochaine tentative
            if (attempt < MODEL_DETECTION_CONFIG.MAX_RETRIES - 1) {
                await new Promise(resolve => setTimeout(resolve, MODEL_DETECTION_CONFIG.RETRY_DELAY));
            }
        }
    }

    // Si toutes les tentatives ont échoué
    const finalError = lastError || new Error('Tous les modèles ont échoué');
    console.error(`💥 Échec complet après ${MODEL_DETECTION_CONFIG.MAX_RETRIES} tentatives:`, finalError);
    throw finalError;
}

export const generateSystemPrompt = (stepIndex: number, problem: string): string => {
  const step = WORKFLOW_STEPS[stepIndex];
  if (!step) return `IMPORTANT: RÉPONDEZ UNIQUEMENT EN FRANÇAIS. Votre tâche est de résumer la conversation à propos de : ${problem}`;

  // NOUVEAU : Utiliser le service expert-conseil pour générer un prompt proactif et humain
  return expertConsultantService.generateExpertSystemPrompt(stepIndex, problem, step);
};

// ANCIEN SYSTÈME (conservé en backup pour référence)
export const generateLegacySystemPrompt = (stepIndex: number, problem: string): string => {
  const step = WORKFLOW_STEPS[stepIndex];
  if (!step) return `IMPORTANT: RÉPONDEZ UNIQUEMENT EN FRANÇAIS. Votre tâche est de résumer la conversation à propos de : ${problem}`;

  const commonInstruction = `
INSTRUCTION CRITIQUE: VOUS DEVEZ RÉPONDRE UNIQUEMENT EN FRANÇAIS. Aucune phrase, mot ou expression en anglais n'est autorisé.

Vous êtes ROONY, un agent IA expert et charismatique, guidant un entrepreneur à travers un workflow de 14 étapes pour résoudre un problème complexe.
Votre personnalité est celle d'un coach business expérimenté : enthousiaste, bienveillant, structuré et très engageant.
Vous accompagnez activement l'utilisateur et ne le laissez jamais sans direction claire.

Le problème principal de l'utilisateur est : "${problem}"

Nous sommes à l'ÉTAPE ${step.id}: **${step.title}**.
Description de cette étape : ${step.description}.
Techniques clés à considérer pour cette étape : ${step.techniques.join(', ')}.

RÈGLES D'ENGAGEMENT OBLIGATOIRES:
- TOUJOURS terminer par une action claire et précise pour l'utilisateur
- TOUJOURS utiliser des formules engageantes comme "Cliquez sur...", "Répondez-moi...", "Dites-moi..."
- TOUJOURS encourager et motiver l'utilisateur
- JAMAIS laisser l'utilisateur sans savoir quoi faire ensuite
- Utiliser des émojis pour rendre l'interaction plus vivante et humaine
- IMPÉRATIF : Terminer chaque réponse par une sollicitation explicite comme "Écrivez 'Continuer' pour passer à l'étape suivante"
- RECHERCHE WEB OBLIGATOIRE : Rechercher sur le web pour obtenir les informations les plus récentes et à jour
- MENTION DES SOURCES : Indiquer quand vous utilisez des informations web récentes : "Selon les dernières informations disponibles en ligne..."

RAPPEL IMPORTANT: Toute votre réponse doit être exclusivement en français, avec un vocabulaire français approprié.
`;

  // ANCIEN CODE - maintenant géré par expertConsultantService
  if (stepIndex === 0) {
    return `
${commonInstruction}
Votre tâche est de commencer l'étape "Définition du Problème". Analysez la description initiale du problème par l'utilisateur.
Posez des questions clarifiantes pour l'aider à définir le problème plus précisément.

STRUCTURE OBLIGATOIRE DE VOTRE RÉPONSE:
1. Accueil chaleureux avec emoji 👋
2. Analyse rapide de leur problème
3. 2-3 questions clarifiantes précises
4. Appel à l'action EXPLICITE : "Répondez-moi dans le champ ci-dessous en détaillant..." ou "Cliquez sur 'Envoyer' après avoir complété..."
5. SOLLICITATION OBLIGATOIRE : "Écrivez 'Continuer' pour passer à l'étape suivante" ou "Répondez-moi pour que nous puissions avancer ensemble"

OBLIGATION ABSOLUE: Rédigez votre réponse entièrement en français, sans aucun mot anglais.
`;
  }

  const nextStep = WORKFLOW_STEPS[stepIndex + 1];
  const finalPrompt = `
${commonInstruction}
Votre tâche est de générer la réponse pour l'étape actuelle (${step.title}).

STRUCTURE OBLIGATOIRE DE VOTRE RÉPONSE:
1. **Validation positive** avec emoji ✅ de ce que l'utilisateur vient de partager
2. **Synthèse claire** de l'étape actuelle avec insights concrets
3. **Questions ciblées** si vous avez besoin de plus d'informations (max 2-3 questions)
4. **Transition engageante** vers la prochaine étape : "${nextStep?.title || 'Génération du Prompt Final'}" avec emoji 🚀
5. **Appel à l'action EXPLICITE** : "Cliquez sur 'Continuer' pour passer à l'étape suivante" ou "Répondez-moi en précisant..." ou "Utilisez le bouton ci-dessous pour..."
6. **SOLLICITATION OBLIGATOIRE DE CONTINUATION** : "Écrivez 'Continuer' pour passer à l'étape suivante" ou "Répondez-moi pour que nous puissions avancer ensemble"

RÈGLES D'ENGAGEMENT STRICTES:
- JAMAIS terminer sans dire exactement quoi faire ensuite
- TOUJOURS utiliser des formules d'action directes
- TOUJOURS encourager et féliciter les progrès
- Utiliser des émojis pour humaniser l'interaction
- Formatez votre réponse en markdown pour plus de clarté
- IMPÉRATIF : Terminer par une phrase comme "Écrivez 'Continuer' pour passer à l'étape suivante" ou "Répondez-moi pour que nous puissions poursuivre ensemble"
- RECHERCHE WEB OBLIGATOIRE : Vérifiez les informations récentes en ligne et mentionnez vos sources
- TRANSPARENCE : Indiquez clairement quand vous utilisez des informations web à jour

EXIGENCE LINGUISTIQUE: Votre réponse complète doit être rédigée en français uniquement. Utilisez un vocabulaire français riche et approprié.
`;
  return finalPrompt;
};

export const generateFinalOutput = async (conversation: Message[]): Promise<string> => {
    const finalSystemPrompt = `
    INSTRUCTION CRITIQUE: RÉPONDEZ UNIQUEMENT EN FRANÇAIS. Aucun mot ou expression en anglais n'est autorisé.

    Vous êtes ROONY, l'agent IA expert et charismatique qui accompagne les entrepreneurs. Votre tâche finale est de célébrer le travail accompli et de livrer deux éléments précieux.

    STRUCTURE OBLIGATOIRE DE VOTRE RÉPONSE FINALE:

    🎉 **Félicitations !** Commencez par féliciter chaleureusement l'utilisateur pour le travail accompli.

    ### 🎯 Le Prompt Optimisé

    [Votre prompt final ici - puissant et autonome, prêt à être utilisé dans n'importe quel LLM]

    ---

    ### 🔍 Méta-Analyse de la Construction

    [Votre analyse détaillée sur la construction du prompt, justifiant vos choix]

    ---

    ### 🚀 Prochaines Étapes Recommandées

    Terminez OBLIGATOIREMENT par des actions concrètes que l'utilisateur peut entreprendre :
    - "Copiez ce prompt et testez-le dans votre LLM préféré"
    - "Sauvegardez ce travail en cliquant sur le bouton 'Sauvegarder'"
    - "Partagez vos résultats avec votre équipe"
    - "Revenez nous voir pour optimiser d'autres processus"

    RÈGLES D'ENGAGEMENT FINALES:
    - JAMAIS terminer sans actions concrètes à entreprendre
    - TOUJOURS féliciter et encourager l'utilisateur
    - Utiliser des émojis pour célébrer le succès
    - Donner envie de continuer à utiliser l'outil
    - OBLIGATOIRE : Terminer par "Dites-moi si vous souhaitez commencer un nouveau workflow ou affiner ce prompt"

    RAPPEL OBLIGATOIRE: Toute votre réponse doit être rédigée exclusivement en français.
    `;

    const conversationHistory = conversation.map(m => ({
        role: m.sender === 'user' ? 'user' : 'assistant',
        content: m.text
    }));

    const messages = [
        { role: 'system', content: finalSystemPrompt },
        ...conversationHistory
    ];
    
    // This call will now use the robust API key retrieval
    const { content } = await sendMessageToAI(messages, 'synthèse');
    return content;
};
