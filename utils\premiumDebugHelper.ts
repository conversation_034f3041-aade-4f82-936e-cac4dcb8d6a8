/**
 * Utilitaire de debug pour le mode Premium
 * Permet de tester et diagnostiquer les problèmes d'authentification Premium
 */

import { premiumAuthService } from '../services/premiumAuthService';
import { premiumApiService } from '../services/premiumApiService';
import { usageModeService } from '../services/usageModeService';

/**
 * Classe d'aide au debug Premium
 */
class PremiumDebugHelper {
  /**
   * Lance un diagnostic complet du mode Premium
   */
  public async runFullDiagnostic(): Promise<void> {
    console.log('🔍 === DIAGNOSTIC COMPLET MODE PREMIUM ===\n');
    
    try {
      // 1. Diagnostic du service d'authentification
      const authDiagnostic = await premiumAuthService.runPremiumDiagnostic();
      
      console.log('📊 Résultats du diagnostic:');
      console.log(`   ✅ Authentifié: ${authDiagnostic.isAuthenticated}`);
      console.log(`   🔑 Clé API présente: ${authDiagnostic.hasApiKey}`);
      console.log(`   💳 Crédits: ${authDiagnostic.creditsStatus.credits}$ (${authDiagnostic.creditsStatus.isLow ? 'FAIBLE' : 'OK'})`);
      console.log(`   📦 Modèles disponibles: ${authDiagnostic.modelsCount}`);
      console.log(`   🔍 Validation API: ${authDiagnostic.apiKeyValidation.success ? 'SUCCÈS' : 'ÉCHEC'}`);
      
      if (authDiagnostic.apiKeyValidation.error) {
        console.log(`   ❌ Erreur validation: ${authDiagnostic.apiKeyValidation.error}`);
      }
      
      // 2. Test du service API Premium
      const premiumStatus = premiumApiService.getPremiumStatus();
      console.log(`\n📈 Statut Premium API:`);
      console.log(`   ✅ Disponible: ${premiumStatus.isAvailable}`);
      console.log(`   📊 Modèles: ${premiumStatus.modelsCount}`);
      
      // 3. Test du service de mode d'utilisation
      const currentMode = usageModeService.getCurrentMode();
      const canUsePremium = usageModeService.canUsePremium();
      console.log(`\n🎯 Mode d'utilisation:`);
      console.log(`   📍 Mode actuel: ${currentMode}`);
      console.log(`   🔓 Premium possible: ${canUsePremium}`);
      
      // 4. Recommandations
      if (authDiagnostic.recommendations.length > 0) {
        console.log(`\n💡 Recommandations:`);
        authDiagnostic.recommendations.forEach(rec => console.log(`   ${rec}`));
      }
      
      // 5. Résumé final
      console.log(`\n🎯 RÉSUMÉ:`);
      if (authDiagnostic.isAuthenticated && premiumStatus.isAvailable) {
        console.log('   ✅ Mode Premium prêt à l\'utilisation ! 🎉');
      } else {
        console.log('   ⚠️ Mode Premium non disponible - Suivez les recommandations ci-dessus');
      }
      
    } catch (error) {
      console.error('❌ Erreur lors du diagnostic:', error);
    }
    
    console.log('\n=== FIN DU DIAGNOSTIC ===');
  }

  /**
   * Test rapide de connexion avec une clé API
   */
  public async testApiKey(apiKey: string): Promise<void> {
    console.log('🧪 Test de clé API Premium...');
    
    try {
      const success = await premiumAuthService.authenticateUser(apiKey);
      
      if (success) {
        console.log('✅ Authentification réussie !');
        const status = premiumApiService.getPremiumStatus();
        console.log(`💳 Crédits: ${status.creditsStatus.credits}$`);
        console.log(`📦 Modèles: ${status.modelsCount}`);
      } else {
        console.log('❌ Échec de l\'authentification');
      }
    } catch (error) {
      console.error('❌ Erreur lors du test:', error);
    }
  }

  /**
   * Réinitialise complètement l'authentification Premium
   */
  public resetPremiumAuth(): void {
    console.log('🔄 Réinitialisation de l\'authentification Premium...');
    
    try {
      premiumAuthService.logout();
      usageModeService.setUsageMode('free');
      
      // Nettoyer le localStorage
      localStorage.removeItem('roony_premium_auth');
      localStorage.removeItem('roony_premium_models');
      localStorage.removeItem('roony_usage_mode');
      
      console.log('✅ Réinitialisation terminée');
      console.log('🔸 Vous pouvez maintenant vous reconnecter en mode Premium');
    } catch (error) {
      console.error('❌ Erreur lors de la réinitialisation:', error);
    }
  }

  /**
   * Affiche les informations de debug dans la console
   */
  public showDebugInfo(): void {
    console.log('🔧 === INFORMATIONS DE DEBUG PREMIUM ===');
    console.log('Utilisez ces commandes dans la console:');
    console.log('');
    console.log('// Diagnostic complet');
    console.log('window.premiumDebug.runFullDiagnostic()');
    console.log('');
    console.log('// Tester une clé API');
    console.log('window.premiumDebug.testApiKey("votre-clé-api-ici")');
    console.log('');
    console.log('// Réinitialiser l\'authentification');
    console.log('window.premiumDebug.resetPremiumAuth()');
    console.log('');
    console.log('// Voir les logs détaillés');
    console.log('// Ouvrez les DevTools > Console pour voir tous les logs');
  }
}

// Instance singleton
export const premiumDebugHelper = new PremiumDebugHelper();

// Exposer dans window pour debug facile
declare global {
  interface Window {
    premiumDebug: PremiumDebugHelper;
  }
}

// Exposer l'helper dans window en mode développement
if (typeof window !== 'undefined') {
  window.premiumDebug = premiumDebugHelper;
  
  // Afficher les infos de debug au chargement
  console.log('🔧 Premium Debug Helper chargé !');
  console.log('Tapez "window.premiumDebug.showDebugInfo()" pour voir les commandes disponibles');
}
