#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module d'Indexation de la Mémoire Persistante de Roony
=======================================================

Ce script indexe les cas résolus dans le répertoire /memoire
en utilisant ChromaDB pour la base vectorielle locale et 
sentence-transformers pour la vectorisation.

ATTENTION : Ce module fait partie du système critique de mémoire persistante.
Toute modification doit respecter scrupuleusement les spécifications.
"""

import chromadb
import json
import os
import sys
from sentence_transformers import SentenceTransformer
from pathlib import Path
import logging
from typing import Dict, Any, List

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MemoireIndexer:
    """
    Indexeur de la mémoire persistante de Roony
    
    Responsabilités:
    - Parcours du répertoire /memoire
    - Vectorisation des cas avec paraphrase-multilingual-MiniLM-L12-v2
    - Stockage dans ChromaDB local
    """
    
    def __init__(self, memoire_dir: str = "./memoire", chroma_dir: str = "./chroma_db"):
        """
        Initialise l'indexeur avec les chemins spécifiés
        
        Args:
            memoire_dir: Répertoire contenant les fichiers JSON des cas
            chroma_dir: Répertoire pour la base ChromaDB locale
        """
        self.memoire_dir = Path(memoire_dir)
        self.chroma_dir = Path(chroma_dir)
        
        # Vérifications critiques
        if not self.memoire_dir.exists():
            raise FileNotFoundError(f"Répertoire mémoire introuvable: {self.memoire_dir}")
            
        logger.info(f"Initialisation de l'indexeur")
        logger.info(f"Répertoire mémoire: {self.memoire_dir}")
        logger.info(f"Base ChromaDB: {self.chroma_dir}")
        
        # Initialisation des clients selon les spécifications EXACTES
        try:
            self.client = chromadb.PersistentClient(path=str(self.chroma_dir))
            logger.info("✅ Client ChromaDB initialisé")
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'initialisation ChromaDB: {e}")
            raise
            
        try:
            # Modèle EXACT selon spécifications
            self.model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
            logger.info("✅ Modèle sentence-transformers chargé")
        except Exception as e:
            logger.error(f"❌ Erreur lors du chargement du modèle: {e}")
            raise
            
        # Collection ChromaDB
        try:
            self.collection = self.client.get_or_create_collection(name="memoire_roony")
            logger.info("✅ Collection ChromaDB 'memoire_roony' prête")
        except Exception as e:
            logger.error(f"❌ Erreur lors de la création de la collection: {e}")
            raise
    
    def extraire_texte_document(self, data: Dict[str, Any]) -> str:
        """
        Concatène les champs textuels pertinents d'un cas JSON 
        en un document sémantique unifié
        
        Args:
            data: Données JSON du cas
            
        Returns:
            str: Document textuel concaténé pour vectorisation
        """
        try:
            # Extraction selon la structure définie dans les spécifications
            texte_elements = []
            
            # Problème principal
            if 'problem_summary' in data:
                texte_elements.append(f"Problème: {data['problem_summary']}")
                
            # Contraintes utilisateur
            if 'user_constraints' in data and isinstance(data['user_constraints'], list):
                contraintes_str = ', '.join(data['user_constraints'])
                texte_elements.append(f"Contraintes: {contraintes_str}")
                
            # Mots-clés
            if 'keywords' in data and isinstance(data['keywords'], list):
                mots_cles_str = ', '.join(data['keywords'])
                texte_elements.append(f"Mots-clés: {mots_cles_str}")
                
            # Points clés de la solution
            if 'solution_points_cles' in data and isinstance(data['solution_points_cles'], list):
                solution_str = '. '.join(data['solution_points_cles'])
                texte_elements.append(f"Solution: {solution_str}")
                
            # Contexte P.R.O.F. si disponible
            if 'contexte_prof' in data:
                prof = data['contexte_prof']
                if 'personnage' in prof:
                    texte_elements.append(f"Personnage: {prof['personnage']}")
                if 'objectif_principal' in prof:
                    texte_elements.append(f"Objectif: {prof['objectif_principal']}")
                if 'role_expert' in prof:
                    texte_elements.append(f"Rôle expert: {prof['role_expert']}")
                    
            # Leçons apprises
            if 'lecons_apprises' in data and isinstance(data['lecons_apprises'], list):
                lecons_str = '. '.join(data['lecons_apprises'])
                texte_elements.append(f"Leçons: {lecons_str}")
            
            document_final = '. '.join(texte_elements)
            logger.debug(f"Document extrait pour {data.get('id', 'UNKNOWN')}: {len(document_final)} caractères")
            
            return document_final
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'extraction du texte: {e}")
            raise
    
    def indexer_un_cas(self, filepath: Path) -> bool:
        """
        Indexe un fichier JSON de cas dans ChromaDB
        
        Args:
            filepath: Chemin vers le fichier JSON
            
        Returns:
            bool: True si succès, False sinon
        """
        try:
            logger.info(f"📝 Indexation de: {filepath.name}")
            
            # Lecture du fichier JSON
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Vérification de l'ID unique
            if 'id' not in data:
                logger.error(f"❌ ID manquant dans {filepath.name}")
                return False
                
            cas_id = data['id']
            
            # Extraction du document textuel
            document_text = self.extraire_texte_document(data)
            
            if not document_text.strip():
                logger.warning(f"⚠️ Document vide pour {cas_id}")
                return False
            
            # Vectorisation avec le modèle spécifié
            try:
                embedding = self.model.encode(document_text).tolist()
                logger.debug(f"✅ Embedding généré: {len(embedding)} dimensions")
            except Exception as e:
                logger.error(f"❌ Erreur de vectorisation pour {cas_id}: {e}")
                return False
            
            # Métadonnées enrichies
            metadata = {
                "source": filepath.name,
                "timestamp": data.get('timestamp', ''),
                "satisfaction": data.get('resultats_mesures', {}).get('satisfaction_utilisateur', 0),
                "performance": data.get('resultats_mesures', {}).get('performance_technique', 0)
            }
            
            # Ajout à la collection ChromaDB
            try:
                self.collection.add(
                    embeddings=[embedding],
                    documents=[document_text],
                    metadatas=[metadata],
                    ids=[cas_id]
                )
                logger.info(f"✅ Cas '{cas_id}' indexé avec succès")
                return True
                
            except Exception as e:
                if "already exists" in str(e).lower():
                    logger.info(f"🔄 Mise à jour du cas existant '{cas_id}'")
                    # Mise à jour du cas existant
                    self.collection.update(
                        ids=[cas_id],
                        embeddings=[embedding],
                        documents=[document_text],
                        metadatas=[metadata]
                    )
                    logger.info(f"✅ Cas '{cas_id}' mis à jour")
                    return True
                else:
                    logger.error(f"❌ Erreur ChromaDB pour {cas_id}: {e}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'indexation de {filepath}: {e}")
            return False
    
    def indexer_tous_les_cas(self) -> Dict[str, int]:
        """
        Indexe tous les fichiers JSON du répertoire mémoire
        
        Returns:
            Dict contenant les statistiques d'indexation
        """
        logger.info("🚀 Début de l'indexation complète")
        
        stats = {
            "total_fichiers": 0,
            "succes": 0,
            "echecs": 0,
            "fichiers_ignores": 0
        }
        
        # Parcours du répertoire mémoire
        for filepath in self.memoire_dir.iterdir():
            if filepath.is_file():
                stats["total_fichiers"] += 1
                
                if filepath.suffix.lower() == '.json':
                    if self.indexer_un_cas(filepath):
                        stats["succes"] += 1
                    else:
                        stats["echecs"] += 1
                else:
                    stats["fichiers_ignores"] += 1
                    logger.debug(f"Fichier ignoré (pas .json): {filepath.name}")
        
        # Rapport final
        logger.info("📊 RAPPORT D'INDEXATION")
        logger.info(f"   Total fichiers: {stats['total_fichiers']}")
        logger.info(f"   Succès: {stats['succes']}")
        logger.info(f"   Échecs: {stats['echecs']}")
        logger.info(f"   Ignorés: {stats['fichiers_ignores']}")
        
        if stats["echecs"] == 0:
            logger.info("🎉 Indexation complète réussie!")
        else:
            logger.warning(f"⚠️ {stats['echecs']} erreur(s) détectée(s)")
            
        return stats
    
    def get_collection_info(self) -> Dict[str, Any]:
        """
        Retourne des informations sur la collection ChromaDB
        
        Returns:
            Dict avec les informations de la collection
        """
        try:
            count = self.collection.count()
            return {
                "nombre_cas": count,
                "collection_name": "memoire_roony",
                "status": "active"
            }
        except Exception as e:
            logger.error(f"❌ Erreur lors de la récupération des infos: {e}")
            return {"error": str(e)}


def main():
    """
    Point d'entrée principal du script d'indexation
    """
    try:
        logger.info("🤖 ROONY - Indexeur de Mémoire Persistante v1.0")
        logger.info("=" * 50)
        
        # Initialisation de l'indexeur
        indexer = MemoireIndexer()
        
        # Affichage des informations avant indexation
        info_avant = indexer.get_collection_info()
        logger.info(f"📊 État initial: {info_avant}")
        
        # Indexation complète
        stats = indexer.indexer_tous_les_cas()
        
        # Affichage des informations après indexation
        info_apres = indexer.get_collection_info()
        logger.info(f"📊 État final: {info_apres}")
        
        # Résultat final
        if stats["echecs"] == 0:
            logger.info("🎉 SUCCÈS TOTAL - Mémoire de Roony mise à jour!")
            sys.exit(0)
        else:
            logger.error(f"⚠️ SUCCÈS PARTIEL - {stats['echecs']} erreur(s)")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"💥 ERREUR CRITIQUE: {e}")
        sys.exit(2)


if __name__ == "__main__":
    main()
