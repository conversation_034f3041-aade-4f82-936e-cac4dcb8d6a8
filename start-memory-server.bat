@echo off
echo 🚀 Démarrage du serveur de mémoire persistante Roony
echo ================================================

REM Vérification de Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js n'est pas installé ou accessible
    pause
    exit /b 1
)

REM Vérification de Python
.venv\Scripts\python.exe --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python virtuel non trouvé dans .venv\Scripts\python.exe
    echo 💡 Assurez-vous que l'environnement virtuel Python est configuré
    pause
    exit /b 1
)

REM Vérification du répertoire mémoire
if not exist "memoire" (
    echo ❌ Répertoire 'memoire' introuvable
    echo 💡 Créez le répertoire et ajoutez des fichiers JSON de cas
    pause
    exit /b 1
)

REM Vérification des dépendances Node.js
if not exist "node_modules\express" (
    echo ⚠️ Express non installé, installation en cours...
    npm install express cors
    if %errorlevel% neq 0 (
        echo ❌ Échec de l'installation des dépendances
        pause
        exit /b 1
    )
)

echo ✅ Environnement vérifié
echo 🔧 Démarrage du serveur sur http://localhost:8888
echo.
echo 📋 Endpoints disponibles :
echo    GET  /api/memory/health - Health check
echo    POST /api/memory/search - Recherche dans la mémoire
echo    POST /api/memory/save   - Sauvegarde d'un cas
echo    GET  /api/memory/stats  - Statistiques
echo.
echo 🛑 Appuyez sur Ctrl+C pour arrêter le serveur
echo.

REM Démarrage du serveur
node memory-server.js
