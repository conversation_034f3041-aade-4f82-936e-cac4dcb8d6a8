/**
 * Service de Décomposition et Hiérarchisation des Problèmes
 * Implémente le Principe N°2 : "Dissocier et Hiérarchiser les Pistes d'Analyse"
 * 
 * Identifie automatiquement les différentes entités administratives impliquées,
 * sépare les procédures indépendantes et hiérarchise les objectifs.
 */

export interface ProblemEntity {
  name: string;
  type: 'administrative' | 'legal' | 'financial' | 'social' | 'professional';
  description: string;
  independence: boolean;
  interactions: EntityInteraction[];
  priority: 'PRIMARY' | 'SECONDARY' | 'SUPPORTING';
}

export interface EntityInteraction {
  withEntity: string;
  type: 'INDEPENDENT' | 'DEPENDENT' | 'CONFLICTING' | 'SYNERGISTIC';
  description: string;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
}

export interface ObjectiveHierarchy {
  primary: string;
  secondary: string[];
  supporting: string[];
  risks: string[];
}

export interface DecomposedProblem {
  entities: ProblemEntity[];
  objectives: ObjectiveHierarchy;
  separationStrategy: string;
  coordinationPlan: string;
  riskMitigation: string[];
}

class ProblemDecompositionService {
  
  /**
   * Décompose un problème complexe en entités distinctes et objectifs hiérarchisés
   */
  decomposeProblem(userInput: string, domain: string, keywords: string[]): DecomposedProblem {
    const entities = this.identifyEntities(userInput, keywords);
    const objectives = this.hierarchizeObjectives(userInput, entities);
    const interactions = this.analyzeInteractions(entities);
    
    // Mettre à jour les entités avec les interactions
    entities.forEach(entity => {
      entity.interactions = interactions.filter(interaction => 
        interaction.withEntity === entity.name || 
        entities.some(e => e.name === interaction.withEntity)
      );
    });
    
    return {
      entities,
      objectives,
      separationStrategy: this.generateSeparationStrategy(entities),
      coordinationPlan: this.generateCoordinationPlan(entities, objectives),
      riskMitigation: this.generateRiskMitigation(entities, objectives)
    };
  }

  /**
   * Identifie les entités administratives et autres acteurs impliqués
   */
  private identifyEntities(userInput: string, keywords: string[]): ProblemEntity[] {
    const entities: ProblemEntity[] = [];
    const input = userInput.toLowerCase();
    
    // Entités administratives courantes
    const administrativeEntities = [
      {
        keywords: ['préfecture', 'prefecture', 'sous-préfecture'],
        entity: {
          name: 'Préfecture',
          type: 'administrative' as const,
          description: 'Administration en charge des titres de séjour et autorisations',
          independence: true,
          interactions: [],
          priority: 'PRIMARY' as const
        }
      },
      {
        keywords: ['urssaf', 'cotisations', 'charges sociales'],
        entity: {
          name: 'URSSAF',
          type: 'social' as const,
          description: 'Organisme de recouvrement des cotisations sociales',
          independence: true,
          interactions: [],
          priority: 'SECONDARY' as const
        }
      },
      {
        keywords: ['fisc', 'impôts', 'taxation', 'dgfip'],
        entity: {
          name: 'Administration fiscale',
          type: 'financial' as const,
          description: 'Services des impôts et taxation',
          independence: true,
          interactions: [],
          priority: 'SECONDARY' as const
        }
      },
      {
        keywords: ['consulat', 'ambassade', 'visa'],
        entity: {
          name: 'Consulat/Ambassade',
          type: 'administrative' as const,
          description: 'Représentation diplomatique pour les démarches consulaires',
          independence: false,
          interactions: [],
          priority: 'SUPPORTING' as const
        }
      },
      {
        keywords: ['mairie', 'état civil', 'commune'],
        entity: {
          name: 'Mairie',
          type: 'administrative' as const,
          description: 'Administration communale pour l\'état civil et attestations',
          independence: true,
          interactions: [],
          priority: 'SUPPORTING' as const
        }
      },
      {
        keywords: ['pôle emploi', 'chômage', 'recherche emploi'],
        entity: {
          name: 'Pôle Emploi',
          type: 'professional' as const,
          description: 'Service public de l\'emploi',
          independence: true,
          interactions: [],
          priority: 'SECONDARY' as const
        }
      }
    ];

    // Identifier les entités présentes
    for (const adminEntity of administrativeEntities) {
      if (adminEntity.keywords.some(keyword => 
        input.includes(keyword) || 
        keywords.some(k => k.toLowerCase().includes(keyword))
      )) {
        entities.push({ ...adminEntity.entity });
      }
    }

    // Ajouter des entités contextuelles si nécessaire
    if (this.containsKeywords(keywords, ['entreprise', 'création', 'activité'])) {
      entities.push({
        name: 'Registre du Commerce',
        type: 'administrative',
        description: 'Enregistrement et suivi des entreprises',
        independence: true,
        interactions: [],
        priority: 'SECONDARY'
      });
    }

    if (this.containsKeywords(keywords, ['avocat', 'juridique', 'contentieux'])) {
      entities.push({
        name: 'Conseil juridique',
        type: 'legal',
        description: 'Accompagnement juridique spécialisé',
        independence: true,
        interactions: [],
        priority: 'SUPPORTING'
      });
    }

    return entities;
  }

  /**
   * Hiérarchise les objectifs selon leur importance et urgence
   */
  private hierarchizeObjectives(userInput: string, entities: ProblemEntity[]): ObjectiveHierarchy {
    const input = userInput.toLowerCase();
    
    // Identifier l'objectif principal
    let primary = "Résoudre la situation administrative complexe";
    
    if (input.includes('renouvellement') || input.includes('titre')) {
      primary = "Obtenir le renouvellement du titre de séjour";
    } else if (input.includes('changement') && input.includes('statut')) {
      primary = "Réussir le changement de statut";
    } else if (input.includes('autorisation') && input.includes('travail')) {
      primary = "Obtenir l'autorisation de travail";
    } else if (input.includes('création') && input.includes('entreprise')) {
      primary = "Créer et développer l'activité entrepreneuriale";
    }

    // Objectifs secondaires basés sur les entités identifiées
    const secondary: string[] = [];
    const supporting: string[] = [];
    const risks: string[] = [];

    entities.forEach(entity => {
      switch (entity.name) {
        case 'URSSAF':
          secondary.push("Résoudre le litige avec l'URSSAF");
          risks.push("Impact du litige URSSAF sur les autres démarches");
          break;
        case 'Administration fiscale':
          secondary.push("Régulariser la situation fiscale");
          risks.push("Complications fiscales liées au changement de statut");
          break;
        case 'Pôle Emploi':
          supporting.push("Optimiser l'accompagnement Pôle Emploi");
          break;
        case 'Consulat/Ambassade':
          supporting.push("Coordonner avec les services consulaires");
          break;
      }
    });

    // Risques génériques
    risks.push(
      "Délais de traitement prolongés",
      "Demandes de pièces complémentaires",
      "Interprétation restrictive des règlements"
    );

    return {
      primary,
      secondary,
      supporting,
      risks
    };
  }

  /**
   * Analyse les interactions entre les différentes entités
   */
  private analyzeInteractions(entities: ProblemEntity[]): EntityInteraction[] {
    const interactions: EntityInteraction[] = [];
    
    // Interactions spécifiques connues
    const knownInteractions = [
      {
        entity1: 'Préfecture',
        entity2: 'URSSAF',
        type: 'DEPENDENT' as const,
        description: 'La préfecture peut vérifier la situation sociale auprès de l\'URSSAF',
        riskLevel: 'MEDIUM' as const
      },
      {
        entity1: 'Préfecture',
        entity2: 'Administration fiscale',
        type: 'DEPENDENT' as const,
        description: 'Vérification possible de la situation fiscale pour certaines demandes',
        riskLevel: 'LOW' as const
      },
      {
        entity1: 'URSSAF',
        entity2: 'Administration fiscale',
        type: 'SYNERGISTIC' as const,
        description: 'Coordination entre organismes sociaux et fiscaux',
        riskLevel: 'LOW' as const
      },
      {
        entity1: 'Pôle Emploi',
        entity2: 'Préfecture',
        type: 'INDEPENDENT' as const,
        description: 'Procédures généralement indépendantes',
        riskLevel: 'LOW' as const
      }
    ];

    // Générer les interactions pour les entités présentes
    for (const interaction of knownInteractions) {
      const hasEntity1 = entities.some(e => e.name === interaction.entity1);
      const hasEntity2 = entities.some(e => e.name === interaction.entity2);
      
      if (hasEntity1 && hasEntity2) {
        interactions.push({
          withEntity: interaction.entity2,
          type: interaction.type,
          description: interaction.description,
          riskLevel: interaction.riskLevel
        });
      }
    }

    return interactions;
  }

  /**
   * Génère une stratégie de séparation des procédures
   */
  private generateSeparationStrategy(entities: ProblemEntity[]): string {
    const independentEntities = entities.filter(e => e.independence);
    const dependentEntities = entities.filter(e => !e.independence);
    
    let strategy = "**Stratégie de séparation des procédures :**\n\n";
    
    if (independentEntities.length > 0) {
      strategy += "✅ **Procédures indépendantes** (à traiter séparément) :\n";
      independentEntities.forEach(entity => {
        strategy += `- ${entity.name} : ${entity.description}\n`;
      });
      strategy += "\n";
    }
    
    if (dependentEntities.length > 0) {
      strategy += "⚠️ **Procédures dépendantes** (coordination nécessaire) :\n";
      dependentEntities.forEach(entity => {
        strategy += `- ${entity.name} : ${entity.description}\n`;
      });
      strategy += "\n";
    }
    
    strategy += "**Principe clé :** Chaque administration a ses propres critères. Un problème avec l'entité A ne doit pas automatiquement compromettre les démarches avec l'entité B.";
    
    return strategy;
  }

  /**
   * Génère un plan de coordination entre les entités
   */
  private generateCoordinationPlan(entities: ProblemEntity[], objectives: ObjectiveHierarchy): string {
    let plan = "**Plan de coordination :**\n\n";
    
    plan += `1. **Objectif principal :** ${objectives.primary}\n`;
    plan += `2. **Entité prioritaire :** ${entities.find(e => e.priority === 'PRIMARY')?.name || 'À déterminer'}\n\n`;
    
    plan += "**Séquence recommandée :**\n";
    
    // Entités primaires d'abord
    const primaryEntities = entities.filter(e => e.priority === 'PRIMARY');
    primaryEntities.forEach((entity, index) => {
      plan += `${index + 1}. Traiter avec ${entity.name} (priorité absolue)\n`;
    });
    
    // Puis secondaires
    const secondaryEntities = entities.filter(e => e.priority === 'SECONDARY');
    secondaryEntities.forEach((entity, index) => {
      plan += `${primaryEntities.length + index + 1}. Parallèlement, gérer ${entity.name}\n`;
    });
    
    // Enfin support
    const supportingEntities = entities.filter(e => e.priority === 'SUPPORTING');
    if (supportingEntities.length > 0) {
      plan += `\n**En support :** ${supportingEntities.map(e => e.name).join(', ')}`;
    }
    
    return plan;
  }

  /**
   * Génère des stratégies de mitigation des risques
   */
  private generateRiskMitigation(entities: ProblemEntity[], objectives: ObjectiveHierarchy): string[] {
    const mitigations: string[] = [];
    
    // Mitigations génériques
    mitigations.push("Préparer un dossier explicatif détaillé pour chaque administration");
    mitigations.push("Documenter formellement toutes les contestations en cours");
    mitigations.push("Maintenir une communication proactive avec chaque entité");
    
    // Mitigations spécifiques aux entités
    if (entities.some(e => e.name === 'URSSAF')) {
      mitigations.push("Joindre les preuves de contestation du litige URSSAF à tous les dossiers");
      mitigations.push("Préparer une note explicative sur la différence entre litige et fraude");
    }
    
    if (entities.some(e => e.name === 'Préfecture')) {
      mitigations.push("Anticiper les vérifications croisées de la préfecture");
      mitigations.push("Préparer des justificatifs alternatifs en cas de blocage");
    }
    
    // Mitigations basées sur les risques identifiés
    objectives.risks.forEach(risk => {
      if (risk.includes('délais')) {
        mitigations.push("Déposer les demandes avec une marge de sécurité suffisante");
      }
      if (risk.includes('pièces complémentaires')) {
        mitigations.push("Constituer un dossier exhaustif dès le départ");
      }
    });
    
    return [...new Set(mitigations)]; // Supprimer les doublons
  }

  /**
   * Formate la décomposition pour inclusion dans un prompt
   */
  formatDecompositionForPrompt(decomposition: DecomposedProblem): string {
    let formatted = "**Analyse de votre situation complexe :**\n\n";
    
    // Objectif principal
    formatted += `🎯 **Votre objectif principal :** ${decomposition.objectives.primary}\n\n`;
    
    // Entités impliquées
    formatted += "**Entités administratives identifiées :**\n";
    decomposition.entities.forEach(entity => {
      const priorityEmoji = entity.priority === 'PRIMARY' ? '🔴' : 
                           entity.priority === 'SECONDARY' ? '🟡' : '🟢';
      formatted += `${priorityEmoji} **${entity.name}** : ${entity.description}\n`;
    });
    formatted += "\n";
    
    // Stratégie de séparation
    formatted += decomposition.separationStrategy + "\n\n";
    
    // Points de vigilance
    if (decomposition.objectives.risks.length > 0) {
      formatted += "⚠️ **Points de vigilance :**\n";
      decomposition.objectives.risks.slice(0, 3).forEach(risk => {
        formatted += `- ${risk}\n`;
      });
      formatted += "\n";
    }
    
    return formatted;
  }

  /**
   * Priorise les entités/objectifs selon une heuristique simple de dépendance
   * Retourne un tableau d'entités ordonnées (chemin critique approximatif)
   */
  prioritizeByDependency(decomposition: DecomposedProblem): string[] {
    // If advanced graph mode is enabled, delegate to dependencyGraphService
    try {
      const useGraph = (process && process.env && process.env.USE_ADV_DEP_GRAPH === 'true');
      if (useGraph) {
        // Lazy require to avoid circular deps
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const { dependencyGraphService } = require('./dependencyGraphService');
        return dependencyGraphService.computePriorityPath(decomposition);
      }
    } catch (e) {
      // ignore and fall back to heuristic
    }

    // Heuristique par défaut : entités avec le plus d'interactions et priorité PRIMARY sont critiques
    const entities = decomposition.entities.slice();

    entities.sort((a, b) => {
      const aScore = (a.priority === 'PRIMARY' ? 3 : a.priority === 'SECONDARY' ? 2 : 1)
        + (a.interactions ? a.interactions.length : 0);
      const bScore = (b.priority === 'PRIMARY' ? 3 : b.priority === 'SECONDARY' ? 2 : 1)
        + (b.interactions ? b.interactions.length : 0);
      return bScore - aScore;
    });

    return entities.map(e => e.name);
  }

  /**
   * Vérifie si les mots-clés contiennent certains termes
   */
  private containsKeywords(keywords: string[], searchTerms: string[]): boolean {
    return searchTerms.some(term => 
      keywords.some(keyword => 
        keyword.toLowerCase().includes(term.toLowerCase()) ||
        term.toLowerCase().includes(keyword.toLowerCase())
      )
    );
  }
}

// Instance singleton
export const problemDecompositionService = new ProblemDecompositionService();
