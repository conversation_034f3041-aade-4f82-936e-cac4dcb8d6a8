/**
 * Service d'Amélioration Continue Stratégique
 * Apprend et s'améliore en permanence grâce aux interactions pour maintenir l'excellence stratégique
 * 
 * MISSION : Faire évoluer constamment l'agent vers une expertise stratégique supérieure
 */

import { strategicMonitoringService, type StrategicMetrics } from './strategicMonitoringService';
import { workflowMemoryService } from '../../services/workflowMemoryService';

export interface StrategicPattern {
  pattern: string;
  domain: string;
  frequency: number;
  successRate: number;
  improvementPotential: number;
  bestPractices: string[];
  commonMistakes: string[];
}

export interface StrategicLearning {
  insight: string;
  context: string;
  applicability: string[];
  confidence: number;
  source: 'user_feedback' | 'pattern_analysis' | 'quality_metrics';
}

export interface StrategicEvolution {
  currentLevel: 'developing' | 'proficient' | 'expert' | 'master';
  strengths: string[];
  improvementAreas: string[];
  nextMilestone: string;
  learningRecommendations: string[];
}

class StrategicContinuousImprovementService {
  private strategicPatterns: StrategicPattern[] = [];
  private learnings: StrategicLearning[] = [];
  private performanceHistory: { date: Date; metrics: StrategicMetrics }[] = [];

  /**
   * Analyse une interaction complète et en tire des apprentissages stratégiques
   */
  analyzeAndLearn(
    userInput: string,
    aiResponse: string,
    stepTitle: string,
    userFeedback?: { satisfaction: number; comments?: string }
  ): void {
    // Évaluer la qualité stratégique
    const evaluation = strategicMonitoringService.evaluateStrategicQuality(userInput, aiResponse, stepTitle);
    
    // Enregistrer les métriques pour l'historique
    this.performanceHistory.push({
      date: new Date(),
      metrics: evaluation.metrics
    });

    // Maintenir un historique limité (500 dernières interactions)
    if (this.performanceHistory.length > 500) {
      this.performanceHistory = this.performanceHistory.slice(-500);
    }

    // Identifier les patterns stratégiques
    this.identifyStrategicPatterns(userInput, aiResponse, stepTitle, evaluation.metrics);

    // Extraire les apprentissages
    this.extractStrategicLearnings(userInput, aiResponse, evaluation, userFeedback);

    // Détecter les domaines d'amélioration prioritaires
    this.updateImprovementPriorities(evaluation);
  }

  /**
   * Identifie les patterns stratégiques récurrents
   */
  private identifyStrategicPatterns(
    userInput: string,
    aiResponse: string,
    stepTitle: string,
    metrics: StrategicMetrics
  ): void {
    // Extraire les caractéristiques de l'interaction
    const domain = this.extractDomain(userInput);
    const responseApproach = this.analyzeResponseApproach(aiResponse);
    const strategicElements = this.extractStrategicElements(aiResponse);

    // Identifier le pattern
    const patternSignature = `${domain}_${responseApproach}_${stepTitle}`;
    
    // Trouver ou créer le pattern
    let pattern = this.strategicPatterns.find(p => p.pattern === patternSignature);
    
    if (!pattern) {
      pattern = {
        pattern: patternSignature,
        domain,
        frequency: 0,
        successRate: 0,
        improvementPotential: 0,
        bestPractices: [],
        commonMistakes: []
      };
      this.strategicPatterns.push(pattern);
    }

    // Mettre à jour le pattern
    pattern.frequency += 1;
    
    // Calculer le taux de succès basé sur les métriques
    const currentSuccess = metrics.overallStrategicScore / 100;
    pattern.successRate = (pattern.successRate * (pattern.frequency - 1) + currentSuccess) / pattern.frequency;

    // Analyser les bonnes pratiques et erreurs
    if (metrics.overallStrategicScore >= 80) {
      const practices = this.extractBestPractices(aiResponse);
      practices.forEach(practice => {
        if (!pattern.bestPractices.includes(practice)) {
          pattern.bestPractices.push(practice);
        }
      });
    }

    if (metrics.overallStrategicScore < 60) {
      const mistakes = this.extractCommonMistakes(aiResponse, metrics);
      mistakes.forEach(mistake => {
        if (!pattern.commonMistakes.includes(mistake)) {
          pattern.commonMistakes.push(mistake);
        }
      });
    }

    // Calculer le potentiel d'amélioration
    pattern.improvementPotential = Math.max(0, 100 - pattern.successRate * 100);
  }

  /**
   * Extrait les apprentissages stratégiques d'une interaction
   */
  private extractStrategicLearnings(
    userInput: string,
    aiResponse: string,
    evaluation: any,
    userFeedback?: { satisfaction: number; comments?: string }
  ): void {
    const learnings: StrategicLearning[] = [];

    // Apprentissages basés sur les métriques
    if (evaluation.metrics.strategicConsistency >= 90) {
      learnings.push({
        insight: "Maintien exemplaire du fil rouge stratégique",
        context: this.extractStrategicContext(aiResponse),
        applicability: ["all_domains"],
        confidence: 0.9,
        source: 'quality_metrics'
      });
    }

    if (evaluation.metrics.valueEnrichment >= 85) {
      learnings.push({
        insight: "Enrichissement optimal avec pépites de valeur",
        context: this.extractValueContext(aiResponse),
        applicability: this.extractApplicability(userInput),
        confidence: 0.85,
        source: 'quality_metrics'
      });
    }

    // Apprentissages basés sur le feedback utilisateur
    if (userFeedback) {
      if (userFeedback.satisfaction >= 4) {
        learnings.push({
          insight: "Approche très appréciée par l'utilisateur",
          context: userFeedback.comments || "Satisfaction élevée",
          applicability: [this.extractDomain(userInput)],
          confidence: userFeedback.satisfaction / 5,
          source: 'user_feedback'
        });
      }
    }

    // Ajouter les apprentissages valides
    learnings.forEach(learning => {
      if (learning.confidence >= 0.7) {
        this.learnings.push(learning);
      }
    });

    // Maintenir une base d'apprentissages limitée (200 max)
    if (this.learnings.length > 200) {
      this.learnings = this.learnings
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, 200);
    }
  }

  /**
   * Met à jour les priorités d'amélioration basées sur l'analyse
   */
  private updateImprovementPriorities(evaluation: any): void {
    const priorities: string[] = [];

    if (evaluation.metrics.strategicConsistency < 70) {
      priorities.push("Renforcer la cohérence stratégique et le maintien du fil rouge");
    }

    if (evaluation.metrics.valueEnrichment < 60) {
      priorities.push("Augmenter la richesse des pépites de valeur intégrées");
    }

    if (evaluation.metrics.terminologyPrecision < 80) {
      priorities.push("Améliorer la précision terminologique experte");
    }

    // Stocker les priorités pour utilisation future
    if (priorities.length > 0) {
      this.learnings.push({
        insight: `Priorités d'amélioration identifiées: ${priorities.join(', ')}`,
        context: `Score stratégique: ${evaluation.metrics.overallStrategicScore}%`,
        applicability: ["system_improvement"],
        confidence: 0.95,
        source: 'pattern_analysis'
      });
    }
  }

  /**
   * Génère des recommandations d'amélioration personnalisées
   */
  generatePersonalizedImprovements(currentStep: string, userInput: string): string[] {
    const domain = this.extractDomain(userInput);
    const relevantPatterns = this.strategicPatterns.filter(p => 
      p.domain === domain && p.frequency >= 3
    );

    const improvements: string[] = [];

    // Recommandations basées sur les patterns de succès
    relevantPatterns
      .filter(p => p.successRate >= 0.8)
      .forEach(pattern => {
        pattern.bestPractices.forEach(practice => {
          improvements.push(`✅ Pratique éprouvée: ${practice}`);
        });
      });

    // Recommandations basées sur les erreurs communes
    relevantPatterns.forEach(pattern => {
      pattern.commonMistakes.forEach(mistake => {
        improvements.push(`⚠️ Éviter: ${mistake}`);
      });
    });

    // Recommandations basées sur les apprentissages
    const relevantLearnings = this.learnings.filter(l => 
      l.applicability.includes(domain) || l.applicability.includes("all_domains")
    );

    relevantLearnings
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 3)
      .forEach(learning => {
        improvements.push(`💡 Insight: ${learning.insight}`);
      });

    return improvements.slice(0, 5); // Limiter à 5 recommandations maximum
  }

  /**
   * Évalue le niveau d'évolution stratégique actuel
   */
  assessStrategicEvolution(): StrategicEvolution {
    const recentMetrics = this.performanceHistory
      .slice(-50) // 50 dernières interactions
      .map(h => h.metrics);

    if (recentMetrics.length === 0) {
      return {
        currentLevel: 'developing',
        strengths: [],
        improvementAreas: ['Pas encore assez de données pour évaluation'],
        nextMilestone: 'Collecter plus d\'interactions pour analyse',
        learningRecommendations: ['Continuer les interactions pour établir une baseline']
      };
    }

    // Calculer les moyennes récentes
    const avgStrategicConsistency = recentMetrics.reduce((sum, m) => sum + m.strategicConsistency, 0) / recentMetrics.length;
    const avgValueEnrichment = recentMetrics.reduce((sum, m) => sum + m.valueEnrichment, 0) / recentMetrics.length;
    const avgTerminologyPrecision = recentMetrics.reduce((sum, m) => sum + m.terminologyPrecision, 0) / recentMetrics.length;
    const avgOverallScore = recentMetrics.reduce((sum, m) => sum + m.overallStrategicScore, 0) / recentMetrics.length;

    // Déterminer le niveau
    let currentLevel: StrategicEvolution['currentLevel'];
    if (avgOverallScore >= 90) currentLevel = 'master';
    else if (avgOverallScore >= 80) currentLevel = 'expert';
    else if (avgOverallScore >= 70) currentLevel = 'proficient';
    else currentLevel = 'developing';

    // Identifier les forces
    const strengths: string[] = [];
    if (avgStrategicConsistency >= 80) strengths.push("Maintien excellent du fil rouge stratégique");
    if (avgValueEnrichment >= 80) strengths.push("Enrichissement optimal avec pépites de valeur");
    if (avgTerminologyPrecision >= 85) strengths.push("Maîtrise terminologique experte");

    // Identifier les zones d'amélioration
    const improvementAreas: string[] = [];
    if (avgStrategicConsistency < 70) improvementAreas.push("Cohérence stratégique à renforcer");
    if (avgValueEnrichment < 60) improvementAreas.push("Enrichissement en pépites de valeur insuffisant");
    if (avgTerminologyPrecision < 75) improvementAreas.push("Précision terminologique à améliorer");

    // Définir le prochain jalon
    const nextMilestone = this.defineNextMilestone(currentLevel, avgOverallScore);

    // Recommandations d'apprentissage
    const learningRecommendations = this.generateLearningRecommendations(
      currentLevel, 
      avgStrategicConsistency, 
      avgValueEnrichment, 
      avgTerminologyPrecision
    );

    return {
      currentLevel,
      strengths,
      improvementAreas,
      nextMilestone,
      learningRecommendations
    };
  }

  /**
   * Génère un rapport d'évolution stratégique complet
   */
  generateEvolutionReport(): string {
    const evolution = this.assessStrategicEvolution();
    const topPatterns = this.strategicPatterns
      .sort((a, b) => b.successRate - a.successRate)
      .slice(0, 5);

    let report = `=== RAPPORT D'ÉVOLUTION STRATÉGIQUE ===\n\n`;
    
    report += `🎯 NIVEAU ACTUEL: ${evolution.currentLevel.toUpperCase()}\n\n`;

    if (evolution.strengths.length > 0) {
      report += `💪 FORCES STRATÉGIQUES:\n`;
      evolution.strengths.forEach(strength => {
        report += `• ${strength}\n`;
      });
      report += '\n';
    }

    if (evolution.improvementAreas.length > 0) {
      report += `🎯 ZONES D'AMÉLIORATION:\n`;
      evolution.improvementAreas.forEach(area => {
        report += `• ${area}\n`;
      });
      report += '\n';
    }

    report += `🚀 PROCHAIN JALON: ${evolution.nextMilestone}\n\n`;

    report += `📚 RECOMMANDATIONS D'APPRENTISSAGE:\n`;
    evolution.learningRecommendations.forEach(rec => {
      report += `• ${rec}\n`;
    });
    report += '\n';

    if (topPatterns.length > 0) {
      report += `🏆 PATTERNS DE SUCCÈS IDENTIFIÉS:\n`;
      topPatterns.forEach((pattern, index) => {
        report += `${index + 1}. ${pattern.pattern} (Succès: ${Math.round(pattern.successRate * 100)}%)\n`;
        if (pattern.bestPractices.length > 0) {
          report += `   Bonnes pratiques: ${pattern.bestPractices.join(', ')}\n`;
        }
      });
    }

    return report;
  }

  // Méthodes utilitaires privées
  private extractDomain(userInput: string): string {
    const domains = ['administrative', 'legal', 'financial', 'social', 'technical'];
    for (const domain of domains) {
      if (userInput.toLowerCase().includes(domain)) {
        return domain;
      }
    }
    return 'general';
  }

  private analyzeResponseApproach(aiResponse: string): string {
    if (aiResponse.includes('plan') && aiResponse.includes('étape')) return 'structured_plan';
    if (aiResponse.includes('hypothèse') || aiResponse.includes('supposant')) return 'hypothesis_based';
    if (aiResponse.includes('conseil') || aiResponse.includes('recommande')) return 'consultative';
    return 'informational';
  }

  private extractStrategicElements(aiResponse: string): string[] {
    const elements: string[] = [];
    if (aiResponse.includes('objectif')) elements.push('objective_focus');
    if (aiResponse.includes('priorité')) elements.push('priority_based');
    if (aiResponse.includes('étape')) elements.push('step_by_step');
    return elements;
  }

  private extractBestPractices(aiResponse: string): string[] {
    const practices: string[] = [];
    
    if (aiResponse.includes('modèle') || aiResponse.includes('template')) {
      practices.push("Fourniture de modèles concrets");
    }
    
    if (aiResponse.includes('contact') || aiResponse.includes('.gouv')) {
      practices.push("Référencement de contacts officiels");
    }
    
    if (aiResponse.includes('astuce') || aiResponse.includes('attention')) {
      practices.push("Partage d'astuces d'initié");
    }
    
    return practices;
  }

  private extractCommonMistakes(aiResponse: string, metrics: StrategicMetrics): string[] {
    const mistakes: string[] = [];
    
    if (metrics.strategicConsistency < 60) {
      mistakes.push("Manque de cohérence avec l'objectif central");
    }
    
    if (metrics.valueEnrichment < 50) {
      mistakes.push("Insuffisance de valeur ajoutée concrète");
    }
    
    if (metrics.terminologyPrecision < 70) {
      mistakes.push("Imprécision terminologique");
    }
    
    return mistakes;
  }

  private extractStrategicContext(aiResponse: string): string {
    return aiResponse.substring(0, 200) + "...";
  }

  private extractValueContext(aiResponse: string): string {
    const valueKeywords = ['modèle', 'contact', 'astuce', 'conseil'];
    for (const keyword of valueKeywords) {
      const index = aiResponse.toLowerCase().indexOf(keyword);
      if (index !== -1) {
        return aiResponse.substring(Math.max(0, index - 50), index + 150);
      }
    }
    return "Contexte de valeur non identifié";
  }

  private extractApplicability(userInput: string): string[] {
    return [this.extractDomain(userInput)];
  }

  private defineNextMilestone(currentLevel: StrategicEvolution['currentLevel'], avgScore: number): string {
    switch (currentLevel) {
      case 'developing':
        return `Atteindre le niveau "proficient" (score > 70%) - Actuellement: ${Math.round(avgScore)}%`;
      case 'proficient':
        return `Atteindre le niveau "expert" (score > 80%) - Actuellement: ${Math.round(avgScore)}%`;
      case 'expert':
        return `Atteindre le niveau "master" (score > 90%) - Actuellement: ${Math.round(avgScore)}%`;
      case 'master':
        return `Maintenir l'excellence et innover constamment - Score actuel: ${Math.round(avgScore)}%`;
      default:
        return "Évaluation en cours";
    }
  }

  private generateLearningRecommendations(
    level: StrategicEvolution['currentLevel'],
    strategicConsistency: number,
    valueEnrichment: number,
    terminologyPrecision: number
  ): string[] {
    const recommendations: string[] = [];

    if (strategicConsistency < 80) {
      recommendations.push("Pratiquer le maintien systématique du fil rouge stratégique");
    }

    if (valueEnrichment < 75) {
      recommendations.push("Développer une bibliothèque de pépites de valeur par domaine");
    }

    if (terminologyPrecision < 85) {
      recommendations.push("Approfondir la maîtrise terminologique spécialisée");
    }

    if (level === 'developing' || level === 'proficient') {
      recommendations.push("Étudier les patterns de succès des interactions les mieux notées");
    }

    return recommendations;
  }
}

// Instance singleton
export const strategicContinuousImprovementService = new StrategicContinuousImprovementService();
