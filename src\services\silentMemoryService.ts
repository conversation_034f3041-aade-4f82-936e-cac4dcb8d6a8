/**
 * Service de mémoire silencieuse - SOLUTION ANTI-RÉINJECTION
 *
 * ✅ OBJECTIF : Lire la mémoire en arrière-plan sans jamais l'afficher dans le chat
 * ✅ PRINCIPE : L'IA utilise le contexte en interne, mais l'utilisateur ne le voit jamais
 * ✅ RÉSULTAT : Économie de tokens + expérience utilisateur fluide
 */

import { silentMemoryConfigManager } from '../config/silentMemoryConfig';

export interface SilentMemoryContext {
  hasPertinentCases: boolean;
  casesCount: number;
  internalContext: string; // Utilisé par l'IA, jamais affiché
  timestamp: number;
  tokensEstimated: number; // ✅ Estimation tokens économisés
}

export interface SilentMemoryConfig {
  maxCases: number;
  enableLogging: boolean;
  forceInvisible: boolean;
}

class SilentMemoryService {
  private config: SilentMemoryConfig;

  constructor() {
    // ✅ Configuration centralisée
    const globalConfig = silentMemoryConfigManager.getConfig();
    this.config = {
      maxCases: globalConfig.tokenOptimization.maxCasesPerQuery,
      enableLogging: globalConfig.logging.enableConsoleLog,
      forceInvisible: globalConfig.privacy.forceInvisibleMode
    };
  }

  /**
   * ✅ MÉTHODE PRINCIPALE : Lecture silencieuse de la mémoire
   * 
   * @param userQuery Question de l'utilisateur
   * @param conversationStage Stage actuel (pour éviter les doublons)
   * @returns Contexte silencieux pour l'IA uniquement
   */
  async readMemorySilently(
    userQuery: string,
    conversationStage?: string
  ): Promise<SilentMemoryContext> {
    try {
      if (this.config.enableLogging) {
        console.log('🔇 [SILENT] Lecture mémoire silencieuse...');
        console.log(`📍 [SILENT] Stage: ${conversationStage || 'non défini'}`);
      }

      // ✅ Toujours lire la mémoire, mais ne jamais l'afficher
      const memoryData = await this.fetchMemoryData(userQuery);

      if (!memoryData || memoryData.length === 0) {
        if (this.config.enableLogging) {
          console.log('ℹ️ [SILENT] Aucun cas pertinent trouvé');
        }
        
        return {
          hasPertinentCases: false,
          casesCount: 0,
          internalContext: '',
          timestamp: Date.now()
        };
      }

      // ✅ Formater le contexte pour l'IA uniquement (jamais affiché)
      const internalContext = this.formatForAIOnly(memoryData);

      if (this.config.enableLogging) {
        console.log(`🧠 [SILENT] ${memoryData.length} cas lus silencieusement`);
        console.log('✅ [SILENT] Contexte préparé pour l\'IA (invisible utilisateur)');
      }

      return {
        hasPertinentCases: true,
        casesCount: memoryData.length,
        internalContext,
        timestamp: Date.now()
      };

    } catch (error) {
      console.error('❌ [SILENT] Erreur lecture mémoire silencieuse:', error);
      
      return {
        hasPertinentCases: false,
        casesCount: 0,
        internalContext: '',
        timestamp: Date.now()
      };
    }
  }

  /**
   * ✅ Enrichit un prompt avec le contexte silencieux
   * L'utilisateur ne voit JAMAIS ce contexte
   */
  enrichPromptSilently(
    originalPrompt: string,
    silentContext: SilentMemoryContext
  ): string {
    if (!silentContext.hasPertinentCases || this.config.forceInvisible) {
      // ✅ Mode silencieux : pas d'ajout visible au prompt
      if (this.config.enableLogging && silentContext.hasPertinentCases) {
        console.log('🔇 [SILENT] Contexte utilisé en interne, invisible utilisateur');
      }
      
      return originalPrompt; // ✅ Prompt original inchangé
    }

    // ✅ Cette partie ne devrait jamais être atteinte avec forceInvisible=true
    return originalPrompt;
  }

  /**
   * ✅ Sauvegarde une nouvelle interaction (silencieusement)
   */
  async saveInteractionSilently(
    userQuery: string,
    aiResponse: string,
    userSatisfaction?: number
  ): Promise<void> {
    try {
      if (this.config.enableLogging) {
        console.log('💾 [SILENT] Sauvegarde interaction...');
      }

      // Ici, on sauvegarderait dans la base de données/fichier
      // Mais de façon complètement invisible pour l'utilisateur
      
      const interactionData = {
        query: userQuery,
        response: aiResponse,
        satisfaction: userSatisfaction || 0,
        timestamp: Date.now()
      };

      // TODO: Implémenter la sauvegarde réelle
      // await this.saveToStorage(interactionData);

      if (this.config.enableLogging) {
        console.log('✅ [SILENT] Interaction sauvegardée silencieusement');
      }

    } catch (error) {
      console.error('❌ [SILENT] Erreur sauvegarde silencieuse:', error);
    }
  }

  /**
   * ✅ Récupère les données de mémoire (méthode privée)
   */
  private async fetchMemoryData(query: string): Promise<any[]> {
    // TODO: Implémenter la récupération réelle depuis le stockage
    // Pour l'instant, simulation
    
    // Simulation de données pertinentes
    if (query.toLowerCase().includes('problème') || query.toLowerCase().includes('difficulté')) {
      return [
        {
          context: "Situation similaire résolue précédemment",
          solution: "Approche structurée en 3 étapes",
          score: 0.85
        },
        {
          context: "Cas comparable avec succès",
          solution: "Méthode d'analyse progressive",
          score: 0.78
        }
      ];
    }

    return []; // Aucun cas pertinent
  }

  /**
   * ✅ Formate le contexte pour l'IA uniquement (jamais affiché)
   */
  private formatForAIOnly(memoryData: any[]): string {
    if (!memoryData || memoryData.length === 0) {
      return '';
    }

    // ✅ Format interne pour l'IA - l'utilisateur ne voit jamais ceci
    const formattedCases = memoryData
      .slice(0, this.config.maxCases)
      .map((cas, index) => `Cas ${index + 1}: ${cas.context} → ${cas.solution}`)
      .join('\n');

    return `CONTEXTE INTERNE (INVISIBLE UTILISATEUR):
${formattedCases}

Instructions IA: Utilise subtilement ces informations pour enrichir ta réponse, mais ne les mentionne jamais explicitement.`;
  }

  /**
   * ✅ Configuration du service
   */
  configure(newConfig: Partial<SilentMemoryConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (this.config.enableLogging) {
      console.log('⚙️ [SILENT] Configuration mise à jour:', this.config);
    }
  }
}

// ✅ Instance singleton
export const silentMemoryService = new SilentMemoryService();

// ✅ Configuration par défaut optimisée
silentMemoryService.configure({
  maxCases: 2, // Limiter pour économiser les tokens
  enableLogging: true, // Logs pour debug, mais invisibles utilisateur
  forceInvisible: true // TOUJOURS invisible
});
