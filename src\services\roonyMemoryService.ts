/**
 * Service de Mémoire Persistante de Roony
 * ======================================
 * 
 * Ce service intègre la mémoire persistante vectorielle de Roony
 * dans le workflow principal de l'application React/TypeScript.
 * 
 * ATTENTION: Module critique pour l'injection de contexte dynamique.
 * Toute modification doit respecter les spécifications de Tasks.md.
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import fs from 'fs';

const execAsync = promisify(exec);

export interface MemoireCas {
  id: string;
  timestamp: string;
  problem_summary: string;
  user_constraints: string[];
  keywords: string[];
  solution_points_cles: string[];
  contexte_prof: {
    personnage: string;
    objectif_principal: string;
    contraintes_inviolables: string[];
    role_expert: string;
  };
  resultats_mesures: {
    satisfaction_utilisateur: number;
    performance_technique: number;
    respect_contraintes: number;
  };
  lecons_apprises: string[];
}

export interface MemoireSearchResult {
  cas_ids: string[];
  cas_details: MemoireCas[];
  nombre_resultats: number;
  question_originale: string;
}

export interface MemoireContexteInjection {
  contexte_disponible: boolean;
  contexte_formate: string;
  nombre_cas_pertinents: number;
  cas_utilises: MemoireCas[];
}

/**
 * Service principal de gestion de la mémoire persistante
 */
class RoonyMemoryService {
  private pythonExecutable: string;
  private projectRoot: string;
  private memoireDir: string;
  private isInitialized: boolean = false;

  constructor() {
    // Configuration des chemins selon l'environnement du projet
    this.projectRoot = process.cwd();
    this.pythonExecutable = path.join(this.projectRoot, '.venv', 'Scripts', 'python.exe');
    this.memoireDir = path.join(this.projectRoot, 'memoire');
    
    console.log('🧠 RoonyMemoryService initialisé');
    console.log(`📂 Projet: ${this.projectRoot}`);
    console.log(`🐍 Python: ${this.pythonExecutable}`);
    console.log(`💾 Mémoire: ${this.memoireDir}`);
  }

  /**
   * Vérification de l'environnement et initialisation
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('🔧 Initialisation du service mémoire...');

      // Vérification de l'exécutable Python
      if (!fs.existsSync(this.pythonExecutable)) {
        console.error('❌ Exécutable Python introuvable:', this.pythonExecutable);
        return false;
      }

      // Vérification du répertoire mémoire
      if (!fs.existsSync(this.memoireDir)) {
        console.error('❌ Répertoire mémoire introuvable:', this.memoireDir);
        return false;
      }

      // Test de la recherche mémoire
      const testResult = await this.testMemorySystem();
      if (!testResult) {
        console.error('❌ Test du système mémoire échoué');
        return false;
      }

      this.isInitialized = true;
      console.log('✅ Service mémoire initialisé avec succès');
      return true;

    } catch (error) {
      console.error('💥 Erreur lors de l\'initialisation:', error);
      return false;
    }
  }

  /**
   * Test du système de mémoire persistante
   */
  private async testMemorySystem(): Promise<boolean> {
    try {
      console.log('🧪 Test du système mémoire...');
      
      const command = `"${this.pythonExecutable}" -c "from recherche_memoire import MemoireSearcher; searcher = MemoireSearcher(); print('Test OK')"`;
      const { stdout, stderr } = await execAsync(command, { 
        cwd: this.projectRoot,
        timeout: 30000 
      });

      if (stderr && !stderr.includes('INFO')) {
        console.error('❌ Erreur test mémoire:', stderr);
        return false;
      }

      console.log('✅ Test mémoire réussi');
      return true;

    } catch (error) {
      console.error('❌ Échec du test mémoire:', error);
      return false;
    }
  }

  /**
   * Recherche de cas similaires dans la mémoire persistante
   * 
   * @param questionUtilisateur Question/problème de l'utilisateur
   * @param topN Nombre maximum de cas à retourner
   * @returns Promesse avec les résultats de recherche
   */
  async rechercherCasSimilaires(
    questionUtilisateur: string, 
    topN: number = 2
  ): Promise<MemoireSearchResult | null> {
    try {
      if (!this.isInitialized) {
        console.warn('⚠️ Service mémoire non initialisé');
        return null;
      }

      if (!questionUtilisateur || questionUtilisateur.trim().length === 0) {
        console.warn('⚠️ Question utilisateur vide');
        return null;
      }

      console.log(`🔍 Recherche mémoire pour: "${questionUtilisateur.substring(0, 100)}..."`);

      // Construction de la commande Python pour recherche avec détails
      const pythonScript = `
import sys
sys.path.append('.')
from recherche_memoire import MemoireSearcher
import json

try:
    searcher = MemoireSearcher()
    resultats = searcher.rechercher_avec_details("${questionUtilisateur.replace(/"/g, '\\"')}", ${topN})
    print(json.dumps(resultats, ensure_ascii=False))
except Exception as e:
    print(json.dumps({"error": str(e)}, ensure_ascii=False))
`;

      const command = `"${this.pythonExecutable}" -c "${pythonScript}"`;
      const { stdout, stderr } = await execAsync(command, { 
        cwd: this.projectRoot,
        timeout: 30000,
        encoding: 'utf8'
      });

      if (stderr && !stderr.includes('INFO')) {
        console.error('❌ Erreur recherche mémoire:', stderr);
        return null;
      }

      // Parse du résultat JSON
      const resultLines = stdout.split('\\n').filter(line => line.trim());
      const jsonResult = resultLines[resultLines.length - 1];
      
      const searchResult = JSON.parse(jsonResult);
      
      if (searchResult.error) {
        console.error('❌ Erreur Python:', searchResult.error);
        return null;
      }

      console.log(`✅ ${searchResult.nombre_resultats} cas trouvés`);
      return searchResult as MemoireSearchResult;

    } catch (error) {
      console.error('💥 Erreur lors de la recherche:', error);
      return null;
    }
  }

  /**
   * Génère le contexte mémoire formaté pour injection dans un prompt
   * 
   * @param questionUtilisateur Question de l'utilisateur
   * @param topN Nombre de cas à rechercher
   * @returns Contexte formaté prêt pour injection
   */
  async genererContexteMemoire(
    questionUtilisateur: string, 
    topN: number = 2
  ): Promise<MemoireContexteInjection> {
    try {
      console.log('🎯 Génération contexte mémoire...');

      const searchResult = await this.rechercherCasSimilaires(questionUtilisateur, topN);

      if (!searchResult || searchResult.nombre_resultats === 0) {
        console.log('ℹ️ Aucun cas pertinent trouvé');
        return {
          contexte_disponible: false,
          contexte_formate: '',
          nombre_cas_pertinents: 0,
          cas_utilises: []
        };
      }

      // Formatage du contexte selon les spécifications
      const contexteParts: string[] = [];
      contexteParts.push('--- MÉMOIRE TAMPON : EXEMPLES DE CAS PRÉCÉDENTS RÉSOLUS AVEC SUCCÈS ---');

      searchResult.cas_details.forEach((cas, index) => {
        contexteParts.push(`\\n**Cas pertinent #${index + 1} : ${cas.problem_summary}**`);
        
        // Points clés de la solution
        if (cas.solution_points_cles && cas.solution_points_cles.length > 0) {
          const pointsCles = cas.solution_points_cles.join('. ');
          contexteParts.push(`- Points clés de la solution : ${pointsCles}`);
        }

        // Contraintes respectées
        if (cas.user_constraints && cas.user_constraints.length > 0) {
          const contraintes = cas.user_constraints.join(', ');
          contexteParts.push(`- Contraintes respectées : ${contraintes}`);
        }

        // Leçons apprises
        if (cas.lecons_apprises && cas.lecons_apprises.length > 0) {
          const lecons = cas.lecons_apprises.join('. ');
          contexteParts.push(`- Leçons apprises : ${lecons}`);
        }

        // Résultats de performance
        if (cas.resultats_mesures) {
          const { satisfaction_utilisateur, performance_technique, respect_contraintes } = cas.resultats_mesures;
          contexteParts.push(`- Résultats : Satisfaction ${satisfaction_utilisateur}%, Performance ${performance_technique}%, Contraintes ${respect_contraintes}%`);
        }

        // Contexte P.R.O.F. si disponible
        if (cas.contexte_prof) {
          contexteParts.push(`- Contexte expert : ${cas.contexte_prof.role_expert} pour ${cas.contexte_prof.personnage}`);
        }
      });

      contexteParts.push('---\\n');

      const contexteFormate = contexteParts.join('\\n');

      console.log(`✅ Contexte généré (${contexteFormate.length} caractères)`);

      return {
        contexte_disponible: true,
        contexte_formate: contexteFormate,
        nombre_cas_pertinents: searchResult.nombre_resultats,
        cas_utilises: searchResult.cas_details || []
      };

    } catch (error) {
      console.error('💥 Erreur génération contexte:', error);
      return {
        contexte_disponible: false,
        contexte_formate: '',
        nombre_cas_pertinents: 0,
        cas_utilises: []
      };
    }
  }

  /**
   * Sauvegarde d'un nouveau cas résolu avec succès
   * 
   * @param casData Données du cas à sauvegarder
   * @returns Succès de la sauvegarde
   */
  async sauvegarderNouveauCas(casData: Partial<MemoireCas>): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        console.warn('⚠️ Service mémoire non initialisé');
        return false;
      }

      // Génération d'un ID unique si non fourni
      const timestamp = new Date().toISOString();
      const id = casData.id || `cas_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      const casSauvegarde: MemoireCas = {
        id,
        timestamp,
        problem_summary: casData.problem_summary || 'Problème non défini',
        user_constraints: casData.user_constraints || [],
        keywords: casData.keywords || [],
        solution_points_cles: casData.solution_points_cles || [],
        contexte_prof: casData.contexte_prof || {
          personnage: 'Utilisateur',
          objectif_principal: 'Résoudre le problème',
          contraintes_inviolables: [],
          role_expert: 'Expert généraliste'
        },
        resultats_mesures: casData.resultats_mesures || {
          satisfaction_utilisateur: 0,
          performance_technique: 0,
          respect_contraintes: 0
        },
        lecons_apprises: casData.lecons_apprises || []
      };

      // Sauvegarde du fichier JSON
      const fichierPath = path.join(this.memoireDir, `${id}.json`);
      const jsonContent = JSON.stringify(casSauvegarde, null, 2);
      
      fs.writeFileSync(fichierPath, jsonContent, 'utf8');
      console.log(`💾 Nouveau cas sauvegardé: ${id}`);

      // Ré-indexation automatique
      await this.reindexerMemoire();

      return true;

    } catch (error) {
      console.error('💥 Erreur sauvegarde cas:', error);
      return false;
    }
  }

  /**
   * Ré-indexation de la mémoire après ajout de nouveaux cas
   */
  async reindexerMemoire(): Promise<boolean> {
    try {
      console.log('🔄 Ré-indexation de la mémoire...');

      const command = `"${this.pythonExecutable}" indexer_memoire.py`;
      const { stdout, stderr } = await execAsync(command, { 
        cwd: this.projectRoot,
        timeout: 60000 
      });

      if (stderr && !stderr.includes('INFO')) {
        console.error('❌ Erreur ré-indexation:', stderr);
        return false;
      }

      console.log('✅ Ré-indexation terminée');
      return true;

    } catch (error) {
      console.error('💥 Erreur ré-indexation:', error);
      return false;
    }
  }

  /**
   * Injection de contexte mémoire dans un prompt système
   *
   * @param promptOriginal Prompt système original
   * @param questionUtilisateur Question de l'utilisateur
   * @param conversationStage Stage actuel de la conversation (pour éviter la réinjection)
   * @param topN Nombre de cas à rechercher
   * @returns Prompt enrichi avec contexte mémoire
   */
  async enrichirPromptAvecMemoire(
    promptOriginal: string,
    questionUtilisateur: string,
    conversationStage?: string,
    topN: number = 2
  ): Promise<{ prompt_enrichi: string; contexte_info: MemoireContexteInjection }> {
    try {
      console.log('🎭 Enrichissement du prompt avec mémoire...');
      console.log(`📍 Stage de conversation: ${conversationStage || 'non défini'}`);

      // NOUVELLE LOGIQUE : Éviter la réinjection selon le contexte
      const shouldInjectFullContext = this.shouldInjectFullMemoryContext(conversationStage, questionUtilisateur);

      if (!shouldInjectFullContext) {
        console.log('🚫 Réinjection de contexte évitée - conversation en cours');
        return {
          prompt_enrichi: promptOriginal,
          contexte_info: {
            contexte_disponible: false,
            contexte_formate: 'Contexte évité pour continuité',
            nombre_cas_pertinents: 0,
            cas_utilises: []
          }
        };
      }

      const contexteMemoire = await this.genererContexteMemoire(questionUtilisateur, topN);

      let promptEnrichi: string;

      if (contexteMemoire.contexte_disponible) {
        // Injection SUBTILE du contexte - pas de répétition massive
        promptEnrichi = `${promptOriginal}

🧠 CONTEXTE MÉMOIRE DISCRET :
${this.formatContexteDiscret(contexteMemoire)}`;

        console.log(`✅ Prompt enrichi discrètement avec ${contexteMemoire.nombre_cas_pertinents} cas pertinents`);
      } else {
        // Pas de contexte mémoire disponible
        promptEnrichi = promptOriginal;
        console.log('ℹ️ Aucun contexte mémoire disponible, prompt standard utilisé');
      }

      return {
        prompt_enrichi: promptEnrichi,
        contexte_info: contexteMemoire
      };

    } catch (error) {
      console.error('💥 Erreur enrichissement prompt:', error);

      // Fallback : retourner le prompt original sans modification
      return {
        prompt_enrichi: promptOriginal,
        contexte_info: {
          contexte_disponible: false,
          contexte_formate: '',
          nombre_cas_pertinents: 0,
          cas_utilises: []
        }
      };
    }
  }

  /**
   * Détermine si le contexte mémoire complet doit être injecté
   * Évite la réinjection répétitive dans les conversations en cours
   */
  private shouldInjectFullMemoryContext(conversationStage?: string, questionUtilisateur?: string): boolean {
    // Détecter les mots-clés de continuation AVANT de vérifier le stage
    if (questionUtilisateur) {
      const motsContinuation = [
        'continuer', 'continue', 'suite', 'suivant', 'étape', 'après',
        'oui', 'ok', 'd\'accord', 'parfait', 'exact', 'correct'
      ];

      const questionLower = questionUtilisateur.toLowerCase();
      const estContinuation = motsContinuation.some(mot => questionLower.includes(mot));

      if (estContinuation) {
        console.log('🚫 Évitement réinjection - détection continuation');
        return false;
      }
    }

    // Si pas de stage défini, on injecte (sécurité)
    if (!conversationStage) {
      return true;
    }

    // Stages où on ÉVITE la réinjection (conversation en cours)
    const stagesAvecContexteExistant = [
      'personnage', 'role', 'objectif', 'format', 'synthesis', 'ready'
    ];

    if (stagesAvecContexteExistant.includes(conversationStage)) {
      console.log(`🚫 Évitement réinjection pour stage: ${conversationStage}`);
      return false;
    }

    // Par défaut, on injecte pour les nouvelles conversations
    return true;
  }

  /**
   * Formate le contexte mémoire de manière discrète
   * Évite les répétitions massives et les instructions redondantes
   */
  private formatContexteDiscret(contexteMemoire: MemoireContexteInjection): string {
    if (!contexteMemoire.cas_utilises || contexteMemoire.cas_utilises.length === 0) {
      return '';
    }

    // Format discret - juste les points clés
    const casFormates = contexteMemoire.cas_utilises.map(cas =>
      `• ${cas.solution_points_cles.slice(0, 2).join(' | ')}`
    ).join('\n');

    return `Expérience pertinente disponible :
${casFormates}

(Utilise cette expérience subtilement, sans répéter le contexte utilisateur)`;
  }

  /**
   * Statistiques de la mémoire persistante
   */
  async getStatistiquesMemoire(): Promise<{
    nombre_cas_total: number;
    derniere_indexation: string;
    status: string;
  }> {
    try {
      if (!fs.existsSync(this.memoireDir)) {
        return { nombre_cas_total: 0, derniere_indexation: 'Jamais', status: 'Non configuré' };
      }

      const fichiers = fs.readdirSync(this.memoireDir).filter(f => f.endsWith('.json'));
      
      return {
        nombre_cas_total: fichiers.length,
        derniere_indexation: new Date().toISOString(),
        status: this.isInitialized ? 'Actif' : 'Non initialisé'
      };

    } catch (error) {
      console.error('💥 Erreur statistiques:', error);
      return { nombre_cas_total: 0, derniere_indexation: 'Erreur', status: 'Erreur' };
    }
  }
}

// Instance singleton
export const roonyMemoryService = new RoonyMemoryService();

export default roonyMemoryService;
