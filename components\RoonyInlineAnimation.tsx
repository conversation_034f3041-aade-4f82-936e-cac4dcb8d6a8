import React, { useEffect, useState } from 'react';
import RoonyMascot, { type RoonyAnimationType } from './RoonyMascot';

interface RoonyInlineAnimationProps {
  /** Déclencher l'animation quand cette condition devient vraie */
  trigger: boolean;
  /** Type d'animation à jouer */
  animation: RoonyAnimationType;
  /** Taille de l'animation */
  size?: number;
  /** Durée d'affichage (en ms) */
  duration?: number;
  /** Classes CSS additionnelles */
  className?: string;
  /** Callback quand l'animation se termine */
  onComplete?: () => void;
  /** Réinitialiser automatiquement après l'animation */
  autoReset?: boolean;
}

/**
 * Composant pour afficher des animations de Roony inline dans l'interface
 * Utile pour des animations contextuelles qui apparaissent temporairement
 */
export const RoonyInlineAnimation: React.FC<RoonyInlineAnimationProps> = ({
  trigger,
  animation,
  size = 80,
  duration = 2500,
  className = '',
  onComplete,
  autoReset = true
}) => {
  const [isActive, setIsActive] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);

  useEffect(() => {
    // Déclencher l'animation quand le trigger devient true
    if (trigger && !hasTriggered) {
      setIsActive(true);
      setHasTriggered(true);
    }
    
    // Réinitialiser si le trigger redevient false et autoReset est activé
    if (!trigger && autoReset) {
      setHasTriggered(false);
      setIsActive(false);
    }
  }, [trigger, hasTriggered, autoReset]);

  const handleAnimationComplete = () => {
    setIsActive(false);
    onComplete?.();
    
    // Réinitialiser pour permettre de nouvelles animations
    if (autoReset) {
      setTimeout(() => {
        setHasTriggered(false);
      }, 100);
    }
  };

  if (!isActive) {
    return null;
  }

  return (
    <div className={`roony-inline-animation ${className}`}>
      <RoonyMascot
        animation={animation}
        size={size}
        duration={duration}
        onAnimationComplete={handleAnimationComplete}
        entranceAnimation="bounceIn"
        exitAnimation="fadeOut"
      />
    </div>
  );
};

export default RoonyInlineAnimation;
