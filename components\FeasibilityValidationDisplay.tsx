import React, { useState } from 'react';

interface FeasibilityValidationDisplayProps {
  validation: {
    isValidated: boolean;
    confidence: 'HIGH' | 'MEDIUM' | 'LOW';
    issues: number;
    report?: string;
  };
  className?: string;
}

export const FeasibilityValidationDisplay: React.FC<FeasibilityValidationDisplayProps> = ({
  validation,
  className = ''
}) => {
  const [showDetailedReport, setShowDetailedReport] = useState(false);

  const getStatusInfo = () => {
    if (validation.isValidated && validation.confidence === 'HIGH') {
      return {
        icon: '✅',
        color: 'text-green-400',
        bgColor: 'bg-green-900/20',
        borderColor: 'border-green-700',
        status: 'Plan Validé',
        description: 'Faisabilité opérationnelle confirmée'
      };
    } else if (validation.isValidated && validation.confidence === 'MEDIUM') {
      return {
        icon: '⚡',
        color: 'text-yellow-400',
        bgColor: 'bg-yellow-900/20',
        borderColor: 'border-yellow-700',
        status: 'Plan Optimisé',
        description: 'Améliorations appliquées automatiquement'
      };
    } else {
      return {
        icon: '🔧',
        color: 'text-orange-400',
        bgColor: 'bg-orange-900/20',
        borderColor: 'border-orange-700',
        status: 'Plan Ajusté',
        description: 'Corrections critiques appliquées'
      };
    }
  };

  const getConfidenceColor = () => {
    switch (validation.confidence) {
      case 'HIGH': return 'text-green-400';
      case 'MEDIUM': return 'text-yellow-400';
      case 'LOW': return 'text-red-400';
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <div className={`${className} ${statusInfo.bgColor} ${statusInfo.borderColor} border rounded-lg p-3`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <span className="text-lg">{statusInfo.icon}</span>
          <div>
            <h4 className={`text-sm font-semibold ${statusInfo.color}`}>
              Principe 5 : {statusInfo.status}
            </h4>
            <p className="text-xs text-slate-400">{statusInfo.description}</p>
          </div>
        </div>
        
        <div className="text-right">
          <div className={`text-xs font-medium ${getConfidenceColor()}`}>
            Confiance: {validation.confidence}
          </div>
          <div className="text-xs text-slate-500">
            {validation.issues} problème{validation.issues > 1 ? 's' : ''} détecté{validation.issues > 1 ? 's' : ''}
          </div>
        </div>
      </div>

      {/* Indicateur de méthode appliquée */}
      <div className="mb-2">
        <div className="text-xs text-slate-400 mb-1">Vérifications appliquées :</div>
        <div className="flex flex-wrap gap-1">
          <span className="px-2 py-1 bg-slate-700 text-xs rounded">
            🔄 Séquençage
          </span>
          <span className="px-2 py-1 bg-slate-700 text-xs rounded">
            ⚖️ Faisabilité Juridique
          </span>
          <span className="px-2 py-1 bg-slate-700 text-xs rounded">
            ⏱️ Réalisme Temporel
          </span>
        </div>
      </div>

      {/* Bouton pour afficher le rapport détaillé */}
      {validation.report && validation.issues > 0 && (
        <div className="mt-2">
          <button
            onClick={() => setShowDetailedReport(!showDetailedReport)}
            className="text-xs text-blue-400 hover:text-blue-300 underline"
          >
            {showDetailedReport ? 'Masquer' : 'Voir'} le rapport détaillé
          </button>
          
          {showDetailedReport && (
            <div className="mt-2 p-2 bg-slate-800/50 rounded border border-slate-600">
              <pre className="text-xs text-slate-300 whitespace-pre-wrap">
                {validation.report}
              </pre>
            </div>
          )}
        </div>
      )}

      {/* Message d'expertise */}
      <div className="mt-2 pt-2 border-t border-slate-600">
        <p className="text-xs text-slate-400 italic">
          💡 Ce plan a été challengé automatiquement par l'expertise "chef de projet senior" 
          pour éviter les écueils courants du terrain.
        </p>
      </div>
    </div>
  );
};
